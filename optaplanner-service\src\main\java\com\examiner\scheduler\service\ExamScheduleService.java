package com.examiner.scheduler.service;

import com.examiner.scheduler.domain.*;
import com.examiner.scheduler.rest.ScheduleResponse;
import com.examiner.scheduler.config.HolidayConfig;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import java.util.Map;
import java.util.Set;
import java.util.HashMap;
import java.util.HashSet;

/**
 * 考试排班服务类
 * 负责创建问题实例和构建响应结果
 */
@ApplicationScoped
public class ExamScheduleService {
    
    private static final Logger LOGGER = Logger.getLogger(ExamScheduleService.class.getName());
    
    @Inject
    private HolidayConfig holidayConfig;
    
    // ⭐ HC5约束：跟踪每天已分配的考官，避免同一天同一考官参与多场考试
    private Map<String, Set<String>> examinerDailyAssignments = new HashMap<>();
    
    /**
     * 创建问题实例
     */
    public ExamSchedule createProblemInstance(List<Student> students, 
                                             List<Teacher> teachers, 
                                             String startDate, 
                                             String endDate,
                                             OptimizedConstraintConfiguration constraints) {
        // ⭐ HC5：清空考官占用跟踪器
        examinerDailyAssignments.clear();
        
        LOGGER.info("创建问题实例: 学员=" + students.size() + ", 考官=" + teachers.size() +
                   ", 开始日期=" + startDate + ", 结束日期=" + endDate);
        
        ExamSchedule schedule = new ExamSchedule();
        
        // 设置基础数据
        schedule.setStudents(students);
        schedule.setTeachers(teachers);
        
        // 生成可用日期（关键修复）
        List<String> availableDates = generateAvailableDates(startDate, endDate);
        schedule.setAvailableDates(availableDates);
        LOGGER.info("生成可用日期: " + availableDates.size() + " 天");
        // 🔧 优化：只显示前5个和后5个日期，避免日志过长
        if (availableDates.size() <= 10) {
        LOGGER.info("可用日期详情: " + String.join(", ", availableDates));
        } else {
            String firstFive = String.join(", ", availableDates.subList(0, 5));
            String lastFive = String.join(", ", availableDates.subList(availableDates.size() - 5, availableDates.size()));
            LOGGER.info("可用日期范围: " + firstFive + " ... " + lastFive + " (共" + availableDates.size() + "天)");
        }
        
        // 添加调试信息
        LOGGER.info("考官详情: " + teachers.stream().map(t -> t.getName() + "(" + t.getDepartment() + ")").collect(java.util.stream.Collectors.joining(", ")));
        LOGGER.info("学员详情: " + students.stream().map(s -> s.getName() + "(" + s.getDepartment() + ")").collect(java.util.stream.Collectors.joining(", ")));
        
        // 生成时间段
        List<TimeSlot> timeSlots = generateTimeSlots(startDate, endDate);
        schedule.setTimeSlots(timeSlots);
        
        // 🔧 创建考试分配实体（确保HC6连续日期 + HC5避免冲突）
        List<ExamAssignment> assignments = new ArrayList<>();
        int assignmentCounter = 1;
        
        // ⭐ 跟踪已使用的日期对，避免所有学员挤在同一天
        List<String> usedDates = new ArrayList<>();
        
        for (Student student : students) {
            // ========================================
            // Step 1: 🎯 确定连续的考试日期对（强制满足HC6，且尽量分散）
            // ========================================
            System.err.println("━━━ 开始为学员分配日期: " + student.getName() + " (科室:" + 
                       normalizeDepartment(student.getDepartment()) + ", 班组:" + student.getGroup() + ") ━━━");
            LOGGER.info("━━━ 开始为学员分配日期: " + student.getName() + " (科室:" + 
                       normalizeDepartment(student.getDepartment()) + ", 班组:" + student.getGroup() + ") ━━━");
            
            String[] examDates = findConsecutiveDatePairAvoidingUsed(student, availableDates, usedDates);
            if (examDates == null || examDates[0] == null || examDates[1] == null) {
                LOGGER.severe("❌ [HC6] 无法为学员 " + student.getName() + " 找到连续日期对，跳过该学员");
                continue;
            }
            
            String day1Date = examDates[0];
            String day2Date = examDates[1];
            
            System.err.println("✅ 选定日期: " + day1Date + " → " + day2Date + " (学员:" + student.getName() + ")");
            LOGGER.info("✅ 选定日期: " + day1Date + " → " + day2Date + " (学员:" + student.getName() + ")");
            
            // 标记这两天为已使用
            usedDates.add(day1Date);
            usedDates.add(day2Date);
            
            LOGGER.info("✅ [HC6] 学员 " + student.getName() + " 连续日期: " + day1Date + " → " + day2Date);
            
            // ========================================
            // Step 2: 🚀 为两天分配考官（满足HC2、HC5）
            // ========================================
            Teacher[] preAssignedExaminers = intelligentPreAssignExaminersWithConflictCheck(
                student, teachers, day1Date, day2Date);
            
            if (preAssignedExaminers == null) {
                LOGGER.severe("❌ [HC5] 无法为学员 " + student.getName() + " 智能分配考官，使用随机分配作为初始解");
                // 🔧 修复：不再跳过学员，而是随机分配考官作为初始解
                // OptaPlanner会在求解过程中调整以满足约束
                preAssignedExaminers = assignRandomTeachers(student, teachers);
                if (preAssignedExaminers == null) {
                    LOGGER.severe("❌ 连随机分配都失败，跳过该学员");
                    continue;
                }
            }
            
            // ========================================
            // Step 3: 创建day1 assignment
            // ========================================
            ExamAssignment day1Assignment = new ExamAssignment();
            day1Assignment.setId("EXAM_" + student.getId() + "_DAY1_" + assignmentCounter);
            day1Assignment.setStudent(student);
            day1Assignment.setExamType("day1");
            day1Assignment.setSubjects(List.of("现场", "模拟机1"));
            day1Assignment.setExamDate(day1Date);
            
            if (preAssignedExaminers[0] != null) {
                day1Assignment.setExaminer1(preAssignedExaminers[0]);
                LOGGER.info("📋 Day1 考官1: " + preAssignedExaminers[0].getName());
            }
            if (preAssignedExaminers[1] != null) {
                day1Assignment.setExaminer2(preAssignedExaminers[1]);
                LOGGER.info("📋 Day1 考官2: " + preAssignedExaminers[1].getName());
            }
            if (preAssignedExaminers[2] != null) {
                day1Assignment.setBackupExaminer(preAssignedExaminers[2]);
                LOGGER.info("📋 Day1 备份: " + preAssignedExaminers[2].getName());
            }
            
            assignments.add(day1Assignment);
            
            // ========================================
            // Step 4: 创建day2 assignment
            // 注意：初始解中day1和day2使用相同考官，但OptaPlanner会根据SC2约束优化
            // 最终day1的考官2来自考官1推荐科室，day2的考官2来自考官2推荐科室
            // ========================================
            ExamAssignment day2Assignment = new ExamAssignment();
            day2Assignment.setId("EXAM_" + student.getId() + "_DAY2_" + assignmentCounter);
            day2Assignment.setStudent(student);
            day2Assignment.setExamType("day2");
            day2Assignment.setSubjects(List.of("模拟机2", "口试"));
            day2Assignment.setExamDate(day2Date);
            
            if (preAssignedExaminers[0] != null) {
                day2Assignment.setExaminer1(preAssignedExaminers[0]);
            }
            if (preAssignedExaminers[1] != null) {
                day2Assignment.setExaminer2(preAssignedExaminers[1]);
            }
            if (preAssignedExaminers[2] != null) {
                day2Assignment.setBackupExaminer(preAssignedExaminers[2]);
            }
            
            assignments.add(day2Assignment);
            
            assignmentCounter++;
        }
        
        // 🔧 [优化策略修正] 不再pin住assignment，让OptaPlanner优化所有考官选择
        // 新策略：
        //   - 考官1可以在Day1和Day2不同，只需满足HC2（同科室）硬约束
        //   - 考官2、备份考官可以根据推荐科室池优化
        //   - OptaPlanner根据软约束（SC1晚班、SC3/SC5休息等）选择最优考官
        //   - HC2极高权重（100000）会自动保证考官1与学员同科室
        LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        LOGGER.info("🚀 [优化策略] OptaPlanner将优化所有考官选择");
        LOGGER.info("   ✅ 初始日期分配: 连续且分散（已固定）");
        LOGGER.info("   🔧 考官1: Day1和Day2可以不同，HC2保证同科室");
        LOGGER.info("   🔧 考官2: 根据day1/day2推荐科室池优化");
        LOGGER.info("   🔧 备份考官: 根据推荐科室池优化");
        LOGGER.info("   💡 初始解仅作为起点，OptaPlanner将寻找最优组合");
        for (ExamAssignment assignment : assignments) {
            // ⚠️ 不再pin住assignment，允许OptaPlanner优化
            assignment.setPinned(false);
            
            if (assignment.getExaminer1() != null) {
                String studentDept = assignment.getStudent() != null ? 
                    normalizeDepartment(assignment.getStudent().getDepartment()) : "未知";
                String examiner1Dept = normalizeDepartment(assignment.getExaminer1().getDepartment());
                LOGGER.info("  🔓 可优化: " + assignment.getId() + 
                           " | 学员:" + (assignment.getStudent() != null ? assignment.getStudent().getName() : "null") +
                           " (" + studentDept + ")" +
                           " | 日期:" + assignment.getExamDate() +
                           " | 初始考官1:" + assignment.getExaminer1().getName() + " (" + examiner1Dept + ")");
            }
        }
        LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        schedule.setExamAssignments(assignments);
        
        // 设置约束配置
        if (constraints != null) {
            schedule.setConstraintConfig(constraints);
        }
        
        return schedule;
    }
    
    /**
     * 🎯 HC6：寻找连续的考试日期对（尽量避免已使用的日期）
     * 优先选择完全未使用的日期对，如果找不到则选择部分重叠的
     * @return [day1, day2] 其中 day2 = day1 + 1天，或 null
     */
    private String[] findConsecutiveDatePairAvoidingUsed(Student student, List<String> availableDates, List<String> usedDates) {
        List<String[]> allPairs = new ArrayList<>();
        List<String[]> partiallyUsedPairs = new ArrayList<>();
        
        String studentGroup = student.getGroup(); // 学员班组
        
        // 收集所有连续日期对
        for (int i = 0; i < availableDates.size() - 1; i++) {
            String date1 = availableDates.get(i);
            try {
                LocalDate day1 = LocalDate.parse(date1);
                LocalDate day2 = day1.plusDays(1);
                String date2 = day2.toString();
                
                if (availableDates.contains(date2)) {
                    // ✅ 关键修复：检查是否是学员白班日
                    boolean date1IsDayShift = isStudentOnDayShift(studentGroup, date1);
                    boolean date2IsDayShift = isStudentOnDayShift(studentGroup, date2);
                    
                    if (date1IsDayShift || date2IsDayShift) {
                        String msg = "⚠️ [HC6避让] 跳过白班日: " + date1 + "~" + date2 + 
                                   " (学员:" + student.getName() + " 班组:" + studentGroup + ")";
                        System.err.println(msg);
                        LOGGER.info(msg);
                        continue; // 跳过白班日
                    }
                    
                    boolean date1Used = usedDates.contains(date1);
                    boolean date2Used = usedDates.contains(date2);
                    
                    if (!date1Used && !date2Used) {
                        // 完全未使用的日期对
                        allPairs.add(new String[]{date1, date2});
                    } else {
                        // 部分使用的日期对（作为备选）
                        partiallyUsedPairs.add(new String[]{date1, date2});
                    }
                }
            } catch (Exception e) {
                LOGGER.warning("⚠️ 日期解析失败: " + date1);
            }
        }
        
        // 优先返回完全未使用的日期对
        if (!allPairs.isEmpty()) {
            String[] selected = allPairs.get(0);
            LOGGER.info("✅ [HC6] 找到未使用的连续日期对: " + selected[0] + " → " + selected[1]);
            return selected;
        }
        
        // 如果没有完全未使用的，返回部分使用的
        if (!partiallyUsedPairs.isEmpty()) {
            String[] selected = partiallyUsedPairs.get(0);
            LOGGER.warning("⚠️ [HC6] 使用部分重叠的日期对: " + selected[0] + " → " + selected[1]);
            return selected;
        }
        
        return null;
    }
    
    /**
     * 🚀 智能预分配考官（优化版）
     * ✨ 在满足硬约束的同时，优先考虑软约束优化
     * 🔧 考虑因素：晚班优先、休息日优先、工作量均衡、推荐科室
     */
    private Teacher[] intelligentPreAssignExaminersWithConflictCheck(
            Student student, List<Teacher> teachers, String day1Date, String day2Date) {
        
        Teacher[] result = new Teacher[3]; // [考官1, 考官2, 备份]
        String studentDept = normalizeDepartment(student.getDepartment());
        
        LOGGER.info("🔍 [智能预分配] 开始为学员 " + student.getName() + " (科室:" + studentDept + 
                   ") 选择考官，日期:" + day1Date + "~" + day2Date);
        
        // 🔧 获取两天的班组轮换状态
        DutySchedule dutyDay1 = DutySchedule.forDate(day1Date);
        DutySchedule dutyDay2 = DutySchedule.forDate(day2Date);
        
        // ========================================
        // Step 1: 选择考官1（满足HC2 + 软约束优化）
        // ========================================
        List<TeacherCandidate> examiner1Candidates = new ArrayList<>();
        
        for (Teacher teacher : teachers) {
            String teacherDept = normalizeDepartment(teacher.getDepartment());
            
            // HC2检查：考官1必须与学员同科室（或3/7互通）
            if (!isValidExaminer1Department(studentDept, teacherDept)) {
                continue;
            }
            
            // HC3检查：两天都不能是白班执勤
            if (!isTeacherAvailableOnDate(teacher, dutyDay1) || 
                !isTeacherAvailableOnDate(teacher, dutyDay2)) {
                continue;
            }
            
            // HC5检查：两天都不能已被分配
            if (!isExaminerAvailable(teacher, day1Date) || !isExaminerAvailable(teacher, day2Date)) {
                continue;
            }
            
            // ✨ 计算优先级分数（考虑软约束）
            int priority = calculateTeacherPriority(teacher, day1Date, day2Date, dutyDay1, dutyDay2);
            examiner1Candidates.add(new TeacherCandidate(teacher, priority));
        }
        
        if (examiner1Candidates.isEmpty()) {
            LOGGER.severe("❌ [智能预分配] 无法为学员 " + student.getName() + " 找到满足HC2+HC3+HC5的考官1");
            return null;
        }
        
        // 按优先级排序，选择最优的
        examiner1Candidates.sort((a, b) -> Integer.compare(b.priority, a.priority));
        result[0] = examiner1Candidates.get(0).teacher;
        markExaminerAsAssigned(result[0], day1Date);
        markExaminerAsAssigned(result[0], day2Date);
        
        LOGGER.info("✅ [考官1] " + result[0].getName() + " (科室:" + 
                   normalizeDepartment(result[0].getDepartment()) + 
                   ", 优先级:" + examiner1Candidates.get(0).priority + ")");
        
        // ========================================
        // Step 2: 选择考官2（满足HC7 + 软约束优化）
        // ========================================
        String examiner1Dept = normalizeDepartment(result[0].getDepartment());
        List<TeacherCandidate> examiner2Candidates = new ArrayList<>();
        
        // 🆕 获取学员的推荐科室（使用实际的Student对象字段）
        // 注意：初始解中day1和day2使用相同考官2，但OptaPlanner会根据SC2约束优化
        // 初始解优先使用考官1推荐科室（day1的规则）
        List<String> recommendedDepts = student.getExaminer2RecommendedDepartments();
        
        for (Teacher teacher : teachers) {
            if (teacher.equals(result[0])) continue;
            
            String teacherDept = normalizeDepartment(teacher.getDepartment());
            
            // HC7检查：考官2必须与考官1不同科室
            if (teacherDept.equals(examiner1Dept)) continue;
            
            // HC2检查：考官2必须与学员不同科室
            if (teacherDept.equals(studentDept)) continue;
            
            // HC3检查
            if (!isTeacherAvailableOnDate(teacher, dutyDay1) || 
                !isTeacherAvailableOnDate(teacher, dutyDay2)) {
                continue;
            }
            
            // HC5检查
            if (!isExaminerAvailable(teacher, day1Date) || !isExaminerAvailable(teacher, day2Date)) {
                continue;
            }
            
            // ✨ 计算优先级分数
            int priority = calculateTeacherPriority(teacher, day1Date, day2Date, dutyDay1, dutyDay2);
            
            // ✨ SC2加分：如果来自推荐科室，优先级+100
            if (recommendedDepts.contains(teacher.getDepartment())) {
                priority += 100;
                LOGGER.fine("💡 [SC2加分] " + teacher.getName() + " 来自推荐科室 " + teacher.getDepartment());
            }
            
            examiner2Candidates.add(new TeacherCandidate(teacher, priority));
        }
        
        if (!examiner2Candidates.isEmpty()) {
            examiner2Candidates.sort((a, b) -> Integer.compare(b.priority, a.priority));
            result[1] = examiner2Candidates.get(0).teacher;
            markExaminerAsAssigned(result[1], day1Date);
            markExaminerAsAssigned(result[1], day2Date);
            
            LOGGER.info("✅ [考官2] " + result[1].getName() + " (科室:" + 
                       normalizeDepartment(result[1].getDepartment()) + 
                       ", 优先级:" + examiner2Candidates.get(0).priority + ")");
        }
        
        // ========================================
        // Step 3: 选择备份考官（软约束优化）
        // ========================================
        List<TeacherCandidate> backupCandidates = new ArrayList<>();
        
        for (Teacher teacher : teachers) {
            if (teacher.equals(result[0]) || teacher.equals(result[1])) continue;
            
            // HC3检查
            if (!isTeacherAvailableOnDate(teacher, dutyDay1) || 
                !isTeacherAvailableOnDate(teacher, dutyDay2)) {
                continue;
            }
            
            // HC5检查
            if (!isExaminerAvailable(teacher, day1Date) || !isExaminerAvailable(teacher, day2Date)) {
                continue;
            }
            
            // ✨ 计算优先级分数
            int priority = calculateTeacherPriority(teacher, day1Date, day2Date, dutyDay1, dutyDay2);
            
            // ✨ SC4加分：如果来自推荐科室，优先级+50
            if (recommendedDepts.contains(teacher.getDepartment())) {
                priority += 50;
            }
            
            backupCandidates.add(new TeacherCandidate(teacher, priority));
        }
        
        if (!backupCandidates.isEmpty()) {
            backupCandidates.sort((a, b) -> Integer.compare(b.priority, a.priority));
            result[2] = backupCandidates.get(0).teacher;
            markExaminerAsAssigned(result[2], day1Date);
            markExaminerAsAssigned(result[2], day2Date);
            
            LOGGER.info("✅ [备份考官] " + result[2].getName() + " (科室:" + 
                       normalizeDepartment(result[2].getDepartment()) + 
                       ", 优先级:" + backupCandidates.get(0).priority + ")");
        }
        
        return result;
    }
    
    /**
     * ✨ 计算考官优先级分数（软约束优化）
     * 分数越高，越优先被选择
     */
    private int calculateTeacherPriority(Teacher teacher, String day1Date, String day2Date,
                                        DutySchedule dutyDay1, DutySchedule dutyDay2) {
        int score = 100; // 基础分数
        
        // SC1: 晚班考官优先 (+100分)
        if (isTeacherOnNightShift(teacher, dutyDay1) || isTeacherOnNightShift(teacher, dutyDay2)) {
            score += 100;
            LOGGER.fine("💡 [SC1] " + teacher.getName() + " 晚班考官 +100");
        }
        
        // SC3: 休息第一天考官优先 (+80分)
        if (isTeacherOnFirstRestDay(teacher, dutyDay1) || isTeacherOnFirstRestDay(teacher, dutyDay2)) {
            score += 80;
            LOGGER.fine("💡 [SC3] " + teacher.getName() + " 休息第一天 +80");
        }
        
        // SC5: 休息第二天考官次优先 (+60分)
        if (isTeacherOnSecondRestDay(teacher, dutyDay1) || isTeacherOnSecondRestDay(teacher, dutyDay2)) {
            score += 60;
            LOGGER.fine("💡 [SC5] " + teacher.getName() + " 休息第二天 +60");
        }
        
        // SC10: 工作量较少的考官优先 (工作量每少1次 +10分)
        int currentWorkload = getTeacherCurrentWorkload(teacher);
        int workloadBonus = Math.max(0, (5 - currentWorkload)) * 10; // 假设5次为标准
        score += workloadBonus;
        if (workloadBonus > 0) {
            LOGGER.fine("💡 [SC10] " + teacher.getName() + " 工作量较少(" + currentWorkload + "次) +" + workloadBonus);
        }
        
        // 避免连续工作惩罚
        if (hasConsecutiveWork(teacher, day1Date, day2Date)) {
            score -= 50;
            LOGGER.fine("⚠️ [SC10] " + teacher.getName() + " 有连续工作 -50");
        }
        
        return score;
    }
    
    /**
     * 检查考官是否在晚班
     */
    private boolean isTeacherOnNightShift(Teacher teacher, DutySchedule dutySchedule) {
        String group = teacher.getGroup();
        return dutySchedule != null && group != null && group.equals(dutySchedule.getNightShift());
    }
    
    /**
     * 检查考官是否在休息第一天
     */
    private boolean isTeacherOnFirstRestDay(Teacher teacher, DutySchedule dutySchedule) {
        String group = teacher.getGroup();
        List<String> restGroups = dutySchedule != null ? dutySchedule.getRestGroups() : null;
        return restGroups != null && !restGroups.isEmpty() && group != null && group.equals(restGroups.get(0));
    }
    
    /**
     * 检查考官是否在休息第二天
     */
    private boolean isTeacherOnSecondRestDay(Teacher teacher, DutySchedule dutySchedule) {
        String group = teacher.getGroup();
        List<String> restGroups = dutySchedule != null ? dutySchedule.getRestGroups() : null;
        return restGroups != null && restGroups.size() > 1 && group != null && group.equals(restGroups.get(1));
    }
    
    /**
     * 获取考官当前工作量
     */
    private int getTeacherCurrentWorkload(Teacher teacher) {
        int count = 0;
        String teacherId = String.valueOf(teacher.getId());
        for (Set<String> assignments : examinerDailyAssignments.values()) {
            if (assignments.contains(teacherId)) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 检查考官是否有连续工作
     */
    private boolean hasConsecutiveWork(Teacher teacher, String date1, String date2) {
        // 简化实现：检查前一天和后一天是否也被分配
        try {
            java.time.LocalDate d1 = java.time.LocalDate.parse(date1);
            java.time.LocalDate d2 = java.time.LocalDate.parse(date2);
            
            String beforeDate1 = d1.minusDays(1).toString();
            String afterDate2 = d2.plusDays(1).toString();
            
            String teacherId = String.valueOf(teacher.getId());
            Set<String> beforeAssignments = examinerDailyAssignments.get(beforeDate1);
            Set<String> afterAssignments = examinerDailyAssignments.get(afterDate2);
            
            return (beforeAssignments != null && beforeAssignments.contains(teacherId)) ||
                   (afterAssignments != null && afterAssignments.contains(teacherId));
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取学员的推荐科室（用于SC2、SC4）
     * @deprecated 已废弃。现在使用Student对象的getExaminer2RecommendedDepartments()方法
     * 该方法使用硬编码规则，不如使用Student对象中实际的推荐科室字段
     */
    @Deprecated
    @SuppressWarnings("unused")
    private List<String> getRecommendedDepartments(String studentDept) {
        // 根据业务规则返回推荐科室
        List<String> recommended = new ArrayList<>();
        
        // 示例规则：每个科室都有2个推荐科室
        switch (studentDept) {
            case "一":
                recommended.add("区域五室");
                recommended.add("区域六室");
                break;
            case "二":
                recommended.add("区域四室");
                recommended.add("区域六室");
                break;
            case "三":
                recommended.add("区域五室");
                recommended.add("区域七室");
                break;
            case "四":
                recommended.add("区域二室");
                recommended.add("区域六室");
                break;
            case "五":
                recommended.add("区域一室");
                recommended.add("区域三室");
                break;
            case "六":
                recommended.add("区域一室");
                recommended.add("区域二室");
                break;
            case "七":
                recommended.add("区域三室");
                recommended.add("区域五室");
                break;
        }
        
        return recommended;
    }
    
    /**
     * 考官候选者（用于排序）
     */
    private static class TeacherCandidate {
        Teacher teacher;
        int priority;
        
        TeacherCandidate(Teacher teacher, int priority) {
            this.teacher = teacher;
            this.priority = priority;
        }
    }
    
    /**
     * ⭐ HC5：检查考官在指定日期是否可用
     */
    private boolean isExaminerAvailable(Teacher teacher, String date) {
        if (teacher == null || date == null) return false;
        if (!examinerDailyAssignments.containsKey(date)) {
            examinerDailyAssignments.put(date, new HashSet<>());
        }
        return !examinerDailyAssignments.get(date).contains(String.valueOf(teacher.getId()));
    }
    
    /**
     * ⭐ HC5：标记考官在指定日期已被分配
     */
    private void markExaminerAsAssigned(Teacher teacher, String date) {
        if (teacher == null || date == null) return;
        if (!examinerDailyAssignments.containsKey(date)) {
            examinerDailyAssignments.put(date, new HashSet<>());
        }
        examinerDailyAssignments.get(date).add(String.valueOf(teacher.getId()));
    }
    
    /**
     * 🔧 随机分配考官（当智能分配失败时的fallback）
     * 虽然可能不满足约束，但至少给OptaPlanner一个非null的起点
     * OptaPlanner会在求解过程中调整这些分配以满足所有约束
     */
    private Teacher[] assignRandomTeachers(Student student, List<Teacher> teachers) {
        if (teachers == null || teachers.isEmpty()) {
            return null;
        }
        
        Teacher[] result = new Teacher[3];
        String studentDept = normalizeDepartment(student.getDepartment());
        
        LOGGER.warning("🎲 随机分配考官给学员: " + student.getName() + " (科室:" + studentDept + ")");
        
        // 🔧 [HC2修复] 严格遵守HC2约束：只分配符合科室规则的考官1
        // 尝试找一个同科室的作为考官1（或符合3室7室互通规则）
        for (Teacher teacher : teachers) {
            if (isValidExaminer1Department(studentDept, normalizeDepartment(teacher.getDepartment()))) {
                result[0] = teacher;
                LOGGER.info("✅ [HC2] 找到符合科室规则的考官1: " + teacher.getName() + " (科室:" + teacher.getDepartment() + ")");
                break;
            }
        }
        
        // 🚨 [HC2修复] 如果找不到符合HC2的考官1，直接返回null，不生成违反约束的初始解
        if (result[0] == null) {
            LOGGER.severe("🚨 [HC2] 严重错误：无法为学员 " + student.getName() + " (科室:" + studentDept + ") 找到符合HC2约束的考官1！");
            LOGGER.severe("💡 [HC2] 建议：检查是否有足够的" + studentDept + "室考官，或考虑3室7室互通规则");
            LOGGER.severe("⚠️ [HC2] 该学员将被跳过，不生成初始解，避免HC2违反");
            return null; // 🔧 关键修复：返回null而不是部分填充的数组
        }
        
        // 找一个不同的作为考官2
        for (Teacher teacher : teachers) {
            if (!teacher.equals(result[0])) {
                result[1] = teacher;
                break;
            }
        }
        
        // 找一个不同的作为备份
        for (Teacher teacher : teachers) {
            if (!teacher.equals(result[0]) && !teacher.equals(result[1])) {
                result[2] = teacher;
                break;
            }
        }
        
        LOGGER.info("✅ 随机分配完成: 考官1=" + result[0].getName() +
                   ", 考官2=" + (result[1] != null ? result[1].getName() : "null") +
                   ", 备份=" + (result[2] != null ? result[2].getName() : "null"));
        
        return result;
    }
    
    /**
     * 🎯 智能预分配考官方法
     * 🔧 修复：集成班组轮换算法，基于科室规则和班组状态为学员预分配最合适的考官组合
     * 
     * ⚠️ 注意：此方法已被 intelligentPreAssignExaminersWithConflictCheck 替代
     * 保留此方法以备将来参考或特殊用途
     * 
     * @param student 学员
     * @param teachers 考官列表  
     * @param availableDates 可用日期
     * @return 预分配的考官数组 [考官1, 考官2, 备份考官]
     */
    @SuppressWarnings("unused")  // 此方法已被新版本替代，但保留以备将来使用
    private Teacher[] intelligentPreAssignExaminers(Student student, List<Teacher> teachers, List<String> availableDates) {
        Teacher[] result = new Teacher[3]; // [考官1, 考官2, 备份考官]
        String studentDept = normalizeDepartment(student.getDepartment());
        
        LOGGER.info("🎯 开始为学员 " + student.getName() + " (科室:" + studentDept + ") 智能预分配考官");
        
        // 🔧 获取学员适合的考试日期（非白班执勤日）
        String bestExamDate = findBestExamDateForStudent(student, availableDates);
        if (bestExamDate == null) {
            LOGGER.warning("⚠️ 学员 " + student.getName() + " 没有合适的考试日期");
            return result;
        }
        
        // 🔧 获取该日期的班组轮换状态
        DutySchedule dutySchedule = DutySchedule.forDate(bestExamDate);
        LOGGER.info("📅 选择考试日期: " + bestExamDate + " (白班:" + dutySchedule.getDayShift() + 
                    ", 晚班:" + dutySchedule.getNightShift() + ", 休息:" + dutySchedule.getRestGroups() + ")");
        
        // 🔍 第一步：寻找考官1（同科室或三七室互通，且非白班执勤）
        for (Teacher teacher : teachers) {
            if (result[0] != null) break;
            
            String teacherDept = normalizeDepartment(teacher.getDepartment());
            if (isValidExaminer1Department(studentDept, teacherDept) && 
                isTeacherAvailableOnDate(teacher, dutySchedule)) {
                result[0] = teacher;
                LOGGER.info("✅ 找到合适考官1: " + teacher.getName() + " (科室:" + teacherDept + 
                           ", 班组:" + teacher.getGroup() + ")");
                break;
            }
        }
        
        // 🔍 第二步：寻找考官2（异科室，优先晚班或休息班组）
        // 🔧 修复：不再找到第一个就用，而是按优先级选择最佳考官
        Teacher bestExaminer2 = null;
        int bestExaminer2Priority = -1;
        
        for (Teacher teacher : teachers) {
            if (teacher.equals(result[0])) continue; // 不能与考官1重复
            
            String teacherDept = normalizeDepartment(teacher.getDepartment());
            if (!studentDept.equals(teacherDept) && 
                (result[0] == null || !teacherDept.equals(normalizeDepartment(result[0].getDepartment()))) &&
                isTeacherAvailableOnDate(teacher, dutySchedule)) {
                
                // 🔧 计算考官优先级：晚班100 > 休息第一天80 > 休息第二天60 > 其他10
                int priority = calculateTeacherPriority(teacher, dutySchedule);
                
                if (priority > bestExaminer2Priority) {
                    bestExaminer2 = teacher;
                    bestExaminer2Priority = priority;
                    LOGGER.info("🎯 发现更优考官2候选: " + teacher.getName() + " (科室:" + teacherDept + 
                               ", 班组:" + teacher.getGroup() + ", 优先级:" + priority + ")");
                }
            }
        }
        
        if (bestExaminer2 != null) {
            result[1] = bestExaminer2;
            LOGGER.info("✅ 最终选择考官2: " + bestExaminer2.getName() + " (优先级:" + bestExaminer2Priority + ")");
        }
        
        // 🔍 第三步：寻找备份考官（不同于考官1和考官2，优先晚班或休息班组）
        // 🔧 修复：按优先级选择最佳备份考官
        Teacher bestBackup = null;
        int bestBackupPriority = -1;
        
        for (Teacher teacher : teachers) {
            if (teacher.equals(result[0]) || teacher.equals(result[1])) continue;
            
            if (isTeacherAvailableOnDate(teacher, dutySchedule)) {
                int priority = calculateTeacherPriority(teacher, dutySchedule);
                
                if (priority > bestBackupPriority) {
                    bestBackup = teacher;
                    bestBackupPriority = priority;
                    LOGGER.info("🎯 发现更优备份考官候选: " + teacher.getName() + " (科室:" + 
                               normalizeDepartment(teacher.getDepartment()) + ", 班组:" + teacher.getGroup() + 
                               ", 优先级:" + priority + ")");
                }
            }
        }
        
        if (bestBackup != null) {
            result[2] = bestBackup;
            LOGGER.info("✅ 最终选择备份考官: " + bestBackup.getName() + " (优先级:" + bestBackupPriority + ")");
        }
        
        // 📊 预分配结果统计
        int successCount = 0;
        if (result[0] != null) successCount++;
        if (result[1] != null) successCount++;
        if (result[2] != null) successCount++;
        
        LOGGER.info("📊 学员 " + student.getName() + " 预分配完成: " + successCount + "/3 个考官已分配");
        
        return result;
    }
    
    /**
     * ✅ 检查学员是否在指定日期为白班执勤
     * @param studentGroup 学员班组
     * @param examDate 考试日期
     * @return true=白班执勤不能考试, false=可以考试
     */
    private boolean isStudentOnDayShift(String studentGroup, String examDate) {
        if (studentGroup == null || examDate == null) {
            return false; // 没有班组信息，默认可以考试
        }
        
        try {
            // 使用DutySchedule获取该日期的白班班组
            DutySchedule dutySchedule = DutySchedule.forDate(examDate);
            String dayShiftGroup = dutySchedule.getDayShift();
            
            // 判断学员班组是否为白班班组
            boolean isDayShift = studentGroup.equals(dayShiftGroup);
            
            if (isDayShift) {
                String msg = "🚨 [白班检查] 日期:" + examDate + " 白班班组:" + dayShiftGroup + 
                           " 学员班组:" + studentGroup + " → 是白班执勤日，需跳过";
                System.err.println(msg);
                LOGGER.info(msg);
            } else {
                System.err.println("✅ [白班检查] 日期:" + examDate + " 白班:" + dayShiftGroup + 
                                 " 学员:" + studentGroup + " → 可以考试");
            }
            
            return isDayShift;
        } catch (Exception e) {
            LOGGER.warning("⚠️ 白班检查失败: " + examDate + " - " + e.getMessage());
            return false; // 发生异常，默认可以考试
        }
    }
    
    /**
     * 科室名称标准化
     */
    private String normalizeDepartment(String department) {
        if (department == null) return null;
        
        String normalized = department.trim();
        
        // 标准化映射（与前端保持完全一致，包括"第X科室"格式）
        if (normalized.contains("区域一室") || normalized.contains("一室") || normalized.contains("1室") || normalized.contains("第1科室")) return "一";
        if (normalized.contains("区域二室") || normalized.contains("二室") || normalized.contains("2室") || normalized.contains("第2科室")) return "二";
        if (normalized.contains("区域三室") || normalized.contains("三室") || normalized.contains("3室") || normalized.contains("第3科室")) return "三";
        if (normalized.contains("区域四室") || normalized.contains("四室") || normalized.contains("4室") || normalized.contains("第4科室")) return "四";
        if (normalized.contains("区域五室") || normalized.contains("五室") || normalized.contains("5室") || normalized.contains("第5科室")) return "五";
        if (normalized.contains("区域六室") || normalized.contains("六室") || normalized.contains("6室") || normalized.contains("第6科室")) return "六";
        if (normalized.contains("区域七室") || normalized.contains("七室") || normalized.contains("7室") || normalized.contains("第7科室")) return "七";
        if (normalized.contains("区域八室") || normalized.contains("八室") || normalized.contains("8室") || normalized.contains("第8科室")) return "八";
        if (normalized.contains("区域九室") || normalized.contains("九室") || normalized.contains("9室") || normalized.contains("第9科室")) return "九";
        if (normalized.contains("区域十室") || normalized.contains("十室") || normalized.contains("10室") || normalized.contains("第10科室")) return "十";
        
        return normalized;
    }
    
    /**
     * 验证考官1科室是否有效（只允许同科室或三七室互通）
     */
    private boolean isValidExaminer1Department(String studentDept, String examiner1Dept) {
        if (studentDept == null || examiner1Dept == null) return false;
        
        // 同科室（优先匹配）
        if (studentDept.equals(examiner1Dept)) {
            return true;
        }
        
        // 三室七室互通（特殊规则）
        if ((studentDept.equals("三") && examiner1Dept.equals("七")) ||
            (studentDept.equals("七") && examiner1Dept.equals("三"))) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 构建排班响应结果
     */
    public ScheduleResponse buildScheduleResponse(ExamSchedule solution) {
        ScheduleResponse response = new ScheduleResponse();
        
        // 设置基本信息
        response.setSuccess(true);
        response.setScore(solution.getScore());
        
        // 设置分配结果
        List<ExamAssignment> assignments = solution.getExamAssignments();
        response.setAssignments(assignments);
        
        // 分配统计
        OptimizedConstraintConfiguration constraintConfig = solution.getConstraintConfiguration();
        long completeAssignments = assignments.stream()
                .mapToLong(assignment -> isAssignmentComplete(assignment, constraintConfig) ? 1 : 0)
                .sum();
        
        // 🔍 详细诊断：列出所有不完整的assignment
        LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        LOGGER.info("🔍 [分配完整性检查] 总数=" + assignments.size() + ", 完整=" + completeAssignments);
        int incompleteIndex = 1;
        for (ExamAssignment assignment : assignments) {
            if (!isAssignmentComplete(assignment, constraintConfig)) {
                LOGGER.severe("❌ [不完整#" + incompleteIndex + "] ID=" + assignment.getId() + 
                            " | 学员=" + (assignment.getStudent() != null ? assignment.getStudent().getName() : "NULL") +
                            " | 日期=" + assignment.getExamDate() +
                            " | 考官1=" + (assignment.getExaminer1() != null ? assignment.getExaminer1().getName() : "NULL") +
                            " | 考官2=" + (assignment.getExaminer2() != null ? assignment.getExaminer2().getName() : "NULL") +
                            " | 备份=" + (assignment.getBackupExaminer() != null ? assignment.getBackupExaminer().getName() : "NULL"));
                incompleteIndex++;
            }
        }
        LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        response.setTotalAssignments(assignments.size());
        response.setCompleteAssignments((int) completeAssignments);
        response.setIncompleteAssignments(assignments.size() - (int) completeAssignments);
        
        // 构建并设置统计数据
        ScheduleResponse.ScheduleStatistics statistics = new ScheduleResponse.ScheduleStatistics();
        
        // 设置基本统计
        statistics.setTotalStudents(assignments.size() / 2); // 每个学员有两次考试
        statistics.setAssignedStudents((int) completeAssignments / 2);
        statistics.setCompletionPercentage(assignments.isEmpty() ? 0 : (double) completeAssignments / assignments.size() * 100);
        
        // 🔍 先验证约束违反情况，获取准确的违反数量
        LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        LOGGER.info("🔍 [约束验证] 开始验证最终解的约束情况...");
        int hc2ViolationCount = 0;
        
        for (ExamAssignment assignment : assignments) {
            if (assignment.getStudent() != null && 
                assignment.getExaminer1() != null && 
                assignment.getExaminer2() != null) {
                
                String studentName = assignment.getStudent().getName();
                String studentDept = normalizeDepartment(assignment.getStudent().getDepartment());
                String examiner1Name = assignment.getExaminer1().getName();
                String examiner1Dept = normalizeDepartment(assignment.getExaminer1().getDepartment());
                String examiner2Name = assignment.getExaminer2().getName();
                String examiner2Dept = normalizeDepartment(assignment.getExaminer2().getDepartment());
                String examDate = assignment.getExamDate();
                
                // 检查考官1是否与学员同科室（或三七互通）
                boolean examiner1Valid = isValidExaminer1Department(studentDept, examiner1Dept);
                
                // 检查考官2是否与学员不同科室
                boolean examiner2Valid = !studentDept.equals(examiner2Dept);
                
                // 检查两个考官是否来自不同科室
                boolean differentExaminers = !examiner1Dept.equals(examiner2Dept);
                
                if (!examiner1Valid || !examiner2Valid || !differentExaminers) {
                    hc2ViolationCount++;
                    LOGGER.severe("🚨 [HC2违反] 学员: " + studentName + " (" + studentDept + "), " +
                                "日期: " + examDate + ", " +
                                "考官1: " + examiner1Name + " (" + examiner1Dept + ") " + 
                                (examiner1Valid ? "✅" : "❌") + ", " +
                                "考官2: " + examiner2Name + " (" + examiner2Dept + ") " +
                                (examiner2Valid ? "✅" : "❌") + ", " +
                                "考官间: " + (differentExaminers ? "✅异科室" : "❌同科室"));
                } else {
                    LOGGER.fine("✅ [HC2合规] 学员: " + studentName + " (" + studentDept + "), " +
                              "考官1: " + examiner1Name + " (" + examiner1Dept + "), " +
                              "考官2: " + examiner2Name + " (" + examiner2Dept + ")");
                }
            }
        }
        
        if (hc2ViolationCount > 0) {
            LOGGER.severe("🚨🚨🚨 [HC2验证失败] 最终解存在 " + hc2ViolationCount + " 个HC2约束违反！");
            LOGGER.severe("🚨 这表示后端约束检查逻辑存在严重Bug，或数据在求解后被意外修改！");
        } else {
            LOGGER.info("✅ [HC2验证通过] 所有分配都满足HC2约束");
        }
        LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        // 🔧 修复：使用实际的约束违反数量，而不是得分的绝对值
        // 设置得分信息
        if (solution.getScore() != null) {
            HardSoftScore score = solution.getScore();
            statistics.setFinalScore(score);
            
            // ✅ 使用实际验证得到的违反数量（目前只有HC2验证，后续可扩展其他约束）
            // 注意：hardScore是得分（带权重），不是违反数量！
            // 例如：21个违反 × 权重(-4) = -84分
            statistics.setHardConstraintViolations(hc2ViolationCount);
            statistics.setSoftConstraintsScore(score.softScore());
            
            LOGGER.info("📊 [统计信息] 硬约束违反数: " + hc2ViolationCount + " (得分: " + score.hardScore() + ")");
        }
        
        response.setStatistics(statistics);
        
        // 设置消息
        if (solution.getScore() != null) {
            HardSoftScore score = solution.getScore();
            if (score.hardScore() < 0) {
                response.setMessage("排班完成，但存在硬约束违规 (硬约束违反: " + hc2ViolationCount + "个, 得分: " + score.hardScore() + ")");
            } else {
                response.setMessage("排班成功完成 (得分: " + score + ")");
            }
        } else {
            response.setMessage("排班计算完成");
        }
        
        LOGGER.info("构建响应完成: 总分配=" + assignments.size() + 
                   ", 完整分配=" + completeAssignments + 
                   ", 硬约束违反=" + hc2ViolationCount +
                   ", 得分=" + solution.getScore());
        
        return response;
    }
    
    /**
     * 检查考官分配是否完整（根据约束配置）
     */
    private boolean isAssignmentComplete(ExamAssignment assignment, OptimizedConstraintConfiguration constraintConfig) {
        if (constraintConfig != null && !constraintConfig.isTwoMainExaminersRequired()) {
            // 如果不要求两名主考官，则只检查是否有考官1
            return assignment.getExaminer1() != null;
        }
        
        // 默认要求两名主考官
        return assignment.getExaminer1() != null && assignment.getExaminer2() != null;
    }
    
    /**
     * 生成时间段
     */
    private List<TimeSlot> generateTimeSlots(String startDate, String endDate) {
        List<TimeSlot> timeSlots = new ArrayList<>();
        
        try {
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE);
            
            LocalDate current = start;
            int slotId = 1;
            
            while (!current.isAfter(end)) {
                // 使用HolidayConfig统一判断工作日（考虑周末、节假日和调休）
                if (holidayConfig.isWorkingDay(current)) {
                    // 上午时段
                    TimeSlot morningSlot = new TimeSlot();
                    morningSlot.setId((long) slotId++);
                    morningSlot.setDate(current.toString());
                    morningSlot.setTimeRange("08:00-12:00");
                    morningSlot.setPeriod("上午");
                    timeSlots.add(morningSlot);
                    
                    // 下午时段
                    TimeSlot afternoonSlot = new TimeSlot();
                    afternoonSlot.setId((long) slotId++);
                    afternoonSlot.setDate(current.toString());
                    afternoonSlot.setTimeRange("14:00-18:00");
                    afternoonSlot.setPeriod("下午");
                    timeSlots.add(afternoonSlot);
                }
                
                current = current.plusDays(1);
            }
            
        } catch (Exception e) {
            LOGGER.severe("生成时间段时发生错误: " + e.getMessage());
        }
        
        LOGGER.info("生成时间段完成: " + timeSlots.size() + " 个时段");
        return timeSlots;
    }
    
    /**
     * 生成可用日期列表（仅工作日，考虑节假日和调休）
     */
    private List<String> generateAvailableDates(String startDate, String endDate) {
        List<String> availableDates = new ArrayList<>();
        
        try {
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE);
            
            LocalDate current = start;
            while (!current.isAfter(end)) {
                // ✅ [HC1修复] 严格过滤节假日：先检查是否是节假日，节假日绝对不能考试
                if (holidayConfig.isHoliday(current)) {
                    // 🚫 节假日（包括国庆、春节等），直接跳过
                    LOGGER.fine("⛔ 跳过节假日: " + current);
                    current = current.plusDays(1);
                    continue;
                }
                
                // ✅ 非节假日：工作日或普通周末都可以考试
                if (holidayConfig.isWorkingDay(current)) {
                    // 工作日（含调休）
                    availableDates.add(current.toString());
                    LOGGER.fine("✅ 添加工作日: " + current);
                } else {
                    // 非工作日（普通周末，非节假日）
                    DayOfWeek dayOfWeek = current.getDayOfWeek();
                    if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                        availableDates.add(current.toString());
                        LOGGER.fine("✅ 添加周末: " + current);
                    }
                }
                current = current.plusDays(1);
            }
            
        } catch (Exception e) {
            LOGGER.severe("生成可用日期时发生错误: " + e.getMessage());
        }
        
        return availableDates;
    }
    
    /**
     * 🔧 智能为学员寻找最佳考试日期（考虑HC6约束和连续两天要求）
     */
    private String findBestExamDateForStudent(Student student, List<String> availableDates) {
        String studentGroup = student.getGroup();
        if (studentGroup == null) {
            // 没有班组信息，返回第一个可用日期
            return availableDates.isEmpty() ? null : availableDates.get(0);
        }
        
        // 🎯 智能搜索：寻找连续两天都不违反HC6约束的日期对
        for (int i = 0; i < availableDates.size() - 1; i++) {
            String firstDate = availableDates.get(i);
            String secondDate = availableDates.get(i + 1);
            
            try {
                LocalDate firstDay = LocalDate.parse(firstDate);
                LocalDate secondDay = LocalDate.parse(secondDate);
                
                // 检查是否连续两天
                if (secondDay.equals(firstDay.plusDays(1))) {
                    DutySchedule firstDuty = DutySchedule.forDate(firstDate);
                    DutySchedule secondDuty = DutySchedule.forDate(secondDate);
                    
                    // 检查两天都不是学员白班执勤日（HC6约束）
                    boolean firstDayOk = !firstDuty.isGroupOnDayShift(studentGroup);
                    boolean secondDayOk = !secondDuty.isGroupOnDayShift(studentGroup);
                    
                    if (firstDayOk && secondDayOk) {
                        LOGGER.info("✅ 学员 " + student.getName() + " 找到最佳连续考试日期: " + firstDate + " -> " + secondDate + " (均非白班执勤日)");
                        return firstDate; // 返回第一天，第二天会自动设置
                    } else {
                        LOGGER.fine("⚠️ 学员 " + student.getName() + " 日期对 " + firstDate + " -> " + secondDate + " 不符合约束: 第一天=" + 
                                    (firstDayOk ? "可用" : "白班冲突") + ", 第二天=" + (secondDayOk ? "可用" : "白班冲突"));
                    }
                }
            } catch (Exception e) {
                LOGGER.warning("⚠️ 无法解析日期对: " + firstDate + " -> " + secondDate);
            }
        }
        
        // 如果找不到完美的连续日期对，寻找单独的非白班日期
        for (String date : availableDates) {
            try {
                DutySchedule dutySchedule = DutySchedule.forDate(date);
                if (!dutySchedule.isGroupOnDayShift(studentGroup)) {
                    LOGGER.info("✅ 学员 " + student.getName() + " 选择次优考试日期: " + date + " (非白班执勤日)");
                    return date;
                }
            } catch (Exception e) {
                LOGGER.warning("⚠️ 无法解析日期: " + date);
            }
        }
        
        // 最后选择：返回第一个可用日期（让约束系统处理冲突）
        String defaultDate = availableDates.isEmpty() ? "无" : availableDates.get(0);
        LOGGER.warning("⚠️ 学员 " + student.getName() + " 无法找到理想考试日期，使用默认日期 " + defaultDate + " (可能违反约束)");
        return availableDates.isEmpty() ? null : availableDates.get(0);
    }
    
    /**
     * 🔧 新增：判断考官在指定日期是否可用（非白班执勤且非行政班限制）
     */
    private boolean isTeacherAvailableOnDate(Teacher teacher, DutySchedule dutySchedule) {
        String teacherGroup = teacher.getGroup();
        
        // 🔧 修复：行政班考官判断（group为null、"无"或空）
        if (teacherGroup == null || "无".equals(teacherGroup) || teacherGroup.trim().isEmpty()) {
            return true; // 行政班考官始终可用
        }
        
        // 非白班执勤的考官可用（晚班或休息）
        boolean isAvailable = !dutySchedule.isGroupOnDayShift(teacherGroup);
        
        if (isAvailable) {
            if (dutySchedule.isGroupOnNightShift(teacherGroup)) {
                LOGGER.info("🌙 考官 " + teacher.getName() + " 为晚班，优先推荐");
            } else if (dutySchedule.isGroupResting(teacherGroup)) {
                LOGGER.info("😴 考官 " + teacher.getName() + " 为休息班组，可以安排");
            }
        }
        
        return isAvailable;
    }
    
    /**
     * 🔧 新增：计算考官在指定日期的优先级
     * 晚班100 > 休息第一天80 > 休息第二天60 > 行政班40 > 其他10
     */
    private int calculateTeacherPriority(Teacher teacher, DutySchedule dutySchedule) {
        String teacherGroup = teacher.getGroup();
        
        // 🔧 修复：行政班考官判断（group为null、"无"或空）
        if (teacherGroup == null || "无".equals(teacherGroup) || teacherGroup.trim().isEmpty()) {
            return 40; // 行政班中等优先级
        }
        
        // 晚班最高优先级
        if (dutySchedule.isGroupOnNightShift(teacherGroup)) {
            return 100;
        }
        
        // 休息班组次高优先级
        java.util.List<String> restGroups = dutySchedule.getRestGroups();
        if (restGroups != null && restGroups.size() >= 1 && restGroups.get(0).equals(teacherGroup)) {
            return 80; // 休息第一天
        }
        if (restGroups != null && restGroups.size() >= 2 && restGroups.get(1).equals(teacherGroup)) {
            return 60; // 休息第二天
        }
        
        return 10; // 其他情况（白班或未知）
    }
}