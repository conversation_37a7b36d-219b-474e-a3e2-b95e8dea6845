/**
 * OptaPlanner排班服务API
 * 调用OptaPlanner微服务进行排班计算
 */

// OptaPlanner API的基础URL
// Electron环境直接使用localhost，Web环境使用相对路径（通过Vite代理）
const getBaseURL = () => {
  // 检查是否在Electron环境中
  // @ts-ignore - electronAPI是在Electron环境中动态注入的
  if (window.electronAPI && window.electronAPI.isElectron) {
    return 'http://localhost:8081/schedule'
  }
  // Web环境使用相对路径（通过Vite代理）
  return '/api/schedule'
}

const OPTAPLANNER_BASE_URL = getBaseURL()

// 添加详细的日志记录器
class OptaPlannerLogger {
  private static instance: OptaPlannerLogger
  private logs: Array<{
    timestamp: string
    level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG'
    message: string
    data?: any
  }> = []

  static getInstance(): OptaPlannerLogger {
    if (!OptaPlannerLogger.instance) {
      OptaPlannerLogger.instance = new OptaPlannerLogger()
    }
    return OptaPlannerLogger.instance
  }

  log(level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG', message: string, data?: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data: data ? JSON.stringify(data, null, 2) : undefined
    }
    
    this.logs.push(logEntry)
    
    // 控制台输出
    const emoji = {
      'INFO': '📝',
      'WARN': '⚠️',
      'ERROR': '❌',
      'DEBUG': '🔍'
    }
    
    console.log(`${emoji[level]} [OptaPlanner-${level}] ${message}`)
    if (data) {
      console.log('📊 数据详情:', data)
    }
    
    // 保持日志数量在合理范围内
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-50)
    }
  }

  getLogs(): Array<{timestamp: string, level: string, message: string, data?: any}> {
    return [...this.logs]
  }

  clearLogs() {
    this.logs = []
  }
}

// 请求和响应类型定义
export interface OptaPlannerStudent {
  id: string
  name: string
  department: string
  group: string
  recommendedExaminer1Dept?: string
  recommendedExaminer2Dept?: string
  recommendedBackupDept?: string
  // ✨ 方案A：前端智能日期选择推荐的考试日期
  recommendedExamDate1?: string
  recommendedExamDate2?: string
}

export interface OptaPlannerTeacher {
  id: string
  name: string
  department: string
  group: string
  skills?: string[]
  workload?: number
  consecutiveDays?: number
}

export interface HardSoftScore {
  hardScore: number
  softScore: number
}

export interface OptaPlannerConstraints {
  // 硬约束
  workdaysOnlyExam: boolean           // HC1: 工作日考试限制
  examinerDepartmentRules: boolean    // HC2: 考官科室规则
  twoMainExaminersRequired: boolean   // HC3: 考官配备要求
  noDayShiftExaminer: boolean         // HC4: 白班禁止规则
  
  // 软约束权重
  allowDept37CrossUseWeight: HardSoftScore        // SC4: 三七室互通
  preferNoGroupTeachersWeight: HardSoftScore      // SC6: 无班组优先
  // 新增时间集中度约束
  timeConcentrationWeight: HardSoftScore
}

export interface OptaPlannerRequest {
  students: OptaPlannerStudent[]
  teachers: OptaPlannerTeacher[]
  startDate: string
  endDate: string
  examDates?: string[]
  constraints?: Partial<OptaPlannerConstraints>
  solverConfig?: {
    timeoutSeconds?: number
    maxIterations?: number
    enableMultiThreading?: boolean
    mode?: 'fast' | 'balanced' | 'optimal' | 'auto'
    description?: string
  }
}

export interface OptaPlannerExamAssignment {
  id: string
  student: OptaPlannerStudent
  examType: 'day1' | 'day2'
  subjects: string[]
  examDate: string
  examiner1: OptaPlannerTeacher
  examiner2: OptaPlannerTeacher
  backupExaminer: OptaPlannerTeacher
  location: string
  timeSlot: {
    start: string
    end: string
    period: 'morning' | 'afternoon' | 'evening'
  }
}

export interface OptaPlannerResponse {
  success: boolean
  message?: string
  assignments: OptaPlannerExamAssignment[]
  executionTime?: number
  algorithmUsed?: string
  conflicts?: any[]
  warnings?: any[]
  score?: string | { hardScore: number; softScore: number }  // 添加score属性支持
  examSchedule?: {
    assignments: OptaPlannerExamAssignment[]
  }
  statistics?: {
    totalStudents: number
    assignedStudents: number
    unassignedStudents: number
    totalTeachers: number
    activeTeachers: number
    averageWorkload: number
    maxWorkload: number
    finalScore?: string | {
      hardScore: number
      softScore: number
    }
    completionPercentage: number
    solvingTimeMillis: number
    hardConstraintViolations: number
    softConstraintViolations: number
  }

}

/**
 * OptaPlanner排班服务类
 */
export class OptaPlannerService {
  private baseUrl: string
  private logger = OptaPlannerLogger.getInstance()

  constructor(baseUrl: string = OPTAPLANNER_BASE_URL) {
    this.baseUrl = baseUrl
    this.logger.log('INFO', `OptaPlanner服务初始化，基础URL: ${baseUrl}`)
  }

  /**
   * 检查OptaPlanner服务健康状态
   */
  async checkHealth(): Promise<boolean> {
    try {
      this.logger.log('DEBUG', '开始检查OptaPlanner服务健康状态')
      
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const isHealthy = response.ok
      this.logger.log(isHealthy ? 'INFO' : 'ERROR', 
        `OptaPlanner服务健康检查${isHealthy ? '成功' : '失败'}`, 
        { 
          status: response.status, 
          statusText: response.statusText,
          url: `${this.baseUrl}/health`
        })
      
      return isHealthy
    } catch (error) {
      this.logger.log('ERROR', 'OptaPlanner服务健康检查异常', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        url: `${this.baseUrl}/health`
      })
      return false
    }
  }

  /**
   * 验证请求数据格式
   */
  private validateRequest(request: OptaPlannerRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!request.students || !Array.isArray(request.students)) {
      errors.push('学员数据无效或为空')
    } else if (request.students.length === 0) {
      errors.push('学员列表为空')
    }
    
    if (!request.teachers || !Array.isArray(request.teachers)) {
      errors.push('考官数据无效或为空')
    } else if (request.teachers.length === 0) {
      errors.push('考官列表为空')
    }
    
    if (!request.startDate) {
      errors.push('开始日期未设置')
    }
    
    if (!request.endDate) {
      errors.push('结束日期未设置')
    }
    
    if (request.startDate && request.endDate && new Date(request.startDate) >= new Date(request.endDate)) {
      errors.push('开始日期必须早于结束日期')
    }
    
    return { valid: errors.length === 0, errors }
  }

  /**
   * 生成排班计划 - 支持实时进度回调
   */
  async generateSchedule(
    request: OptaPlannerRequest, 
    onProgress?: (progress: {
      percentage: number
      currentSolution?: OptaPlannerResponse
      message?: string
      score?: { hardScore: number; softScore: number }
    }) => void
  ): Promise<OptaPlannerResponse> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      this.logger.log('INFO', `开始生成排班计划 [${requestId}]`, {
        studentsCount: request.students?.length || 0,
        teachersCount: request.teachers?.length || 0,
        dateRange: `${request.startDate} ~ ${request.endDate}`,
        hasProgressCallback: !!onProgress
      })
      
      // 验证请求数据
      const validation = this.validateRequest(request)
      if (!validation.valid) {
        const errorMsg = `请求数据验证失败: ${validation.errors.join(', ')}`
        this.logger.log('ERROR', errorMsg, { requestId, errors: validation.errors })
        throw new Error(errorMsg)
      }
      
      this.logger.log('DEBUG', `请求数据验证通过 [${requestId}]`)
      
      // 如果有进度回调，使用流式处理
      if (onProgress) {
        this.logger.log('DEBUG', `使用进度回调模式 [${requestId}]`)
        return await this.generateScheduleWithProgress(request, onProgress, requestId)
      }
      
      // 记录请求详情
      this.logger.log('DEBUG', `发送排班请求 [${requestId}]`, {
        url: `${this.baseUrl}/solve`,
        method: 'POST',
        requestData: {
          studentsCount: request.students.length,
          teachersCount: request.teachers.length,
          startDate: request.startDate,
          endDate: request.endDate,
          constraints: request.constraints,
          solverConfig: request.solverConfig
        }
      })
      
      let __sid_main = (window as any).__opta_session_id
      const mainHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId
      }
      if (!__sid_main) {
        const gen = (window.crypto && (window.crypto as any).randomUUID)
          ? (window.crypto as any).randomUUID()
          : (Date.now().toString(36) + Math.random().toString(36).slice(2))
        ;(window as any).__opta_session_id = gen
        __sid_main = gen
      }
      if (__sid_main) mainHeaders['X-Session-Id'] = __sid_main

      const response = await fetch(`${this.baseUrl}/solve`, {
        method: 'POST',
        headers: mainHeaders,
        body: JSON.stringify({
          ...request,
          solverConfig: {
            ...(request.solverConfig || {}),
            mode: request.solverConfig?.mode || 'adaptive'
          }
        }),
      })

      this.logger.log('DEBUG', `收到响应 [${requestId}]`, {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      })

      if (!response.ok) {
        let errorDetails = ''
        try {
          errorDetails = await response.text()
        } catch (e) {
          errorDetails = '无法读取错误详情'
        }
        
        const errorMsg = `OptaPlanner服务请求失败: ${response.status} ${response.statusText}`
        this.logger.log('ERROR', errorMsg, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          errorDetails,
          url: `${this.baseUrl}/solve`
        })
        
        throw new Error(`${errorMsg} - ${errorDetails}`)
      }

      const result: OptaPlannerResponse = await response.json()
      
      this.logger.log('INFO', `排班计算完成 [${requestId}]`, {
        success: result.success,
        assignmentsCount: result.assignments?.length || 0,
        executionTime: result.executionTime,
        algorithmUsed: result.algorithmUsed,
        statistics: result.statistics
      })
      
      if (result.conflicts && result.conflicts.length > 0) {
        this.logger.log('WARN', `发现冲突 [${requestId}]`, {
          conflictsCount: result.conflicts.length,
          conflicts: result.conflicts
        })
      }
      
      if (result.warnings && result.warnings.length > 0) {
        this.logger.log('WARN', `发现警告 [${requestId}]`, {
          warningsCount: result.warnings.length,
          warnings: result.warnings
        })
      }
      
      return result
    } catch (error) {
      this.logger.log('ERROR', `排班计算异常 [${requestId}]`, {
        error: (error as Error).message,
        stack: (error as Error).stack,
        requestData: {
          studentsCount: request.students?.length || 0,
          teachersCount: request.teachers?.length || 0,
          dateRange: `${request.startDate} ~ ${request.endDate}`
        }
      })
      
      throw new Error(`排班服务调用失败: ${(error as Error).message}`)
    }
  }

  /**
   * 带实时进度的排班生成 - 使用同步API + 模拟进度展示
   */
  private async generateScheduleWithProgress(
    request: OptaPlannerRequest,
    onProgress: (progress: {
      percentage: number
      currentSolution?: OptaPlannerResponse
      message?: string
      score?: { hardScore: number; softScore: number }
    }) => void,
    requestId: string
  ): Promise<OptaPlannerResponse> {
    const students = request.students
    const teachers = request.teachers
    
    this.logger.log('DEBUG', `开始进度模式排班 [${requestId}]`, {
      studentsCount: students.length,
      teachersCount: teachers.length
    })
    
    // 🚀 优化进度更新步骤 - 大幅缩短延迟时间
    const progressSteps = [
      { percentage: 10, message: '正在初始化排班问题...', delay: 100 },
      { percentage: 20, message: '正在生成初始解...', delay: 150 },
      { percentage: 35, message: '正在优化硬约束...', delay: 200 },
      { percentage: 50, message: '正在优化软约束...', delay: 250 },
      { percentage: 65, message: '正在平衡工作负载...', delay: 200 },
      { percentage: 80, message: '正在最终优化...', delay: 150 },
      { percentage: 95, message: '正在生成最终结果...', delay: 100 }
    ]
    let currentStepIndex = 0
    let progressTimer: NodeJS.Timeout | null = null

    const updateProgress = () => {
      if (currentStepIndex >= progressSteps.length) {
        return
      }

      const step = progressSteps[currentStepIndex]

      this.logger.log('DEBUG', `进度更新 [${requestId}]`, {
        percentage: step.percentage,
        message: step.message,
        step: currentStepIndex + 1,
        totalSteps: progressSteps.length
      })

      onProgress({
        percentage: step.percentage,
        message: step.message
      })

      currentStepIndex += 1

      if (currentStepIndex < progressSteps.length) {
        progressTimer = setTimeout(updateProgress, step.delay)
      }
    }

    progressTimer = setTimeout(updateProgress, 50)

    try {
      // 调用同步排班API
      this.logger.log('INFO', `调用同步排班API [${requestId}]`)
      
      let __sid_progress = (window as any).__opta_session_id
      const progressHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Request-ID': requestId
      }
      if (!__sid_progress) {
        const gen = (window.crypto && (window.crypto as any).randomUUID)
          ? (window.crypto as any).randomUUID()
          : (Date.now().toString(36) + Math.random().toString(36).slice(2))
        ;(window as any).__opta_session_id = gen
        __sid_progress = gen
      }
      if (__sid_progress) progressHeaders['X-Session-Id'] = __sid_progress

      const response = await fetch(`${this.baseUrl}/solve`, {
        method: 'POST',
        headers: progressHeaders,
        credentials: 'same-origin',
        mode: 'cors',
        body: JSON.stringify({
          ...request,
          solverConfig: {
            ...(request.solverConfig || {}),
            mode: request.solverConfig?.mode || 'adaptive'
          }
        }),
      })

      // 清理进度定时器
      if (progressTimer) {
        clearTimeout(progressTimer)
      }
      
      this.logger.log('DEBUG', `同步API响应 [${requestId}]`, {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      })

      if (!response.ok) {
        let errorDetails = ''
        try {
          errorDetails = await response.text()
        } catch (e) {
          errorDetails = '无法读取错误详情'
        }
        
        const errorMsg = `OptaPlanner服务请求失败: ${response.status} ${response.statusText}`
        this.logger.log('ERROR', errorMsg, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          errorDetails,
          url: `${this.baseUrl}/solve`
        })
        
        throw new Error(`${errorMsg} - ${errorDetails}`)
      }

      const result: OptaPlannerResponse = await response.json()
      
      // 最终进度更新
      onProgress({
        percentage: 100,
        currentSolution: result,
        message: '排班完成！',
        score: typeof result.statistics?.finalScore === 'object' ? result.statistics.finalScore : undefined
      })

      this.logger.log('INFO', `进度模式排班完成 [${requestId}]`, {
        success: result.success,
        assignmentsCount: result.assignments?.length || 0,
        executionTime: result.executionTime,
        finalScore: result.statistics?.finalScore
      })
      
      return result

    } catch (error) {
      // 清理进度定时器
      if (progressTimer) {
        clearTimeout(progressTimer)
      }
      
      this.logger.log('ERROR', `进度模式排班异常 [${requestId}]`, {
        error: (error as Error).message,
        stack: (error as Error).stack
      })
      
      throw new Error(`排班服务调用失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取智能时间段推荐
   */
  async getOptimalTimeSlotRecommendations(request: OptaPlannerRequest): Promise<{
    success: boolean
    message: string
    recommendations: Array<{
      startDate: string
      endDate: string
      qualityScore: number
      successProbability: number
      conflictAnalysis: {
        totalConflicts: number
        resourceShortage: number
        dutyRotationConflicts: number
      }
      resourceSufficiency: {
        totalSufficiency: number
        departmentSufficiency: { [key: string]: number }
      }
      recommendation: string
    }>
  }> {
    const requestId = `timeslot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      this.logger.log('INFO', `获取智能时间段推荐 [${requestId}]`, {
        studentsCount: request.students?.length || 0,
        teachersCount: request.teachers?.length || 0,
        dateRange: `${request.startDate} ~ ${request.endDate}`
      })
      
      const response = await fetch(`${this.baseUrl}/recommend-timeslots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId
        },
        body: JSON.stringify(request),
      })

      this.logger.log('DEBUG', `时间段推荐响应 [${requestId}]`, {
        status: response.status,
        statusText: response.statusText
      })

      if (!response.ok) {
        let errorDetails = ''
        try {
          errorDetails = await response.text()
        } catch (e) {
          errorDetails = '无法读取错误详情'
        }
        
        const errorMsg = `智能推荐请求失败: ${response.status} ${response.statusText}`
        this.logger.log('ERROR', errorMsg, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          errorDetails
        })
        
        throw new Error(`${errorMsg} - ${errorDetails}`)
      }

      const result = await response.json()
      this.logger.log('INFO', `智能时间段推荐完成 [${requestId}]`, {
        success: result.success,
        recommendationsCount: result.recommendations?.length || 0
      })
      
      return result
    } catch (error) {
      this.logger.log('ERROR', `智能时间段推荐异常 [${requestId}]`, {
        error: (error as Error).message,
        stack: (error as Error).stack
      })
      throw new Error(`智能推荐服务调用失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取学员个性化推荐
   */
  async getStudentPersonalizedRecommendations(request: OptaPlannerRequest): Promise<{
    success: boolean
    message: string
    studentRecommendations: { [studentId: string]: Array<{
      startDate: string
      endDate: string
      qualityScore: number
      conflictLevel: 'low' | 'medium' | 'high'
      recommendation: string
    }> }
  }> {
    const requestId = `student_rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      this.logger.log('INFO', `获取学员个性化推荐 [${requestId}]`, {
        studentsCount: request.students?.length || 0,
        teachersCount: request.teachers?.length || 0
      })
      
      const response = await fetch(`${this.baseUrl}/recommend-student-timeslots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId
        },
        body: JSON.stringify(request),
      })

      this.logger.log('DEBUG', `学员推荐响应 [${requestId}]`, {
        status: response.status,
        statusText: response.statusText
      })

      if (!response.ok) {
        let errorDetails = ''
        try {
          errorDetails = await response.text()
        } catch (e) {
          errorDetails = '无法读取错误详情'
        }
        
        const errorMsg = `学员个性化推荐请求失败: ${response.status} ${response.statusText}`
        this.logger.log('ERROR', errorMsg, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          errorDetails
        })
        
        throw new Error(`${errorMsg} - ${errorDetails}`)
      }

      const result = await response.json()
      this.logger.log('INFO', `学员个性化推荐完成 [${requestId}]`, {
        success: result.success,
        studentsWithRecommendations: Object.keys(result.studentRecommendations || {}).length
      })
      
      return result
    } catch (error) {
      this.logger.log('ERROR', `学员个性化推荐异常 [${requestId}]`, {
        error: (error as Error).message,
        stack: (error as Error).stack
      })
      throw new Error(`学员个性化推荐服务调用失败: ${(error as Error).message}`)
    }
  }

  /**
   * 异步生成排班计划
   */
  async generateScheduleAsync(request: OptaPlannerRequest): Promise<{ success: boolean; problemId: string; message: string }> {
    const requestId = `async_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      this.logger.log('INFO', `异步排班请求 [${requestId}]`, {
        studentsCount: request.students?.length || 0,
        teachersCount: request.teachers?.length || 0,
        dateRange: `${request.startDate} ~ ${request.endDate}`
      })
      
      const response = await fetch(`${this.baseUrl}/solve-async`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Request-ID': requestId
        },
        credentials: 'same-origin',
        mode: 'cors',
        body: JSON.stringify(request),
      })

      this.logger.log('DEBUG', `异步排班响应 [${requestId}]`, {
        status: response.status,
        statusText: response.statusText
      })

      if (!response.ok) {
        let errorDetails = ''
        try {
          errorDetails = await response.text()
        } catch (e) {
          errorDetails = '无法读取错误详情'
        }
        
        const errorMsg = `OptaPlanner异步服务请求失败: ${response.status} ${response.statusText}`
        this.logger.log('ERROR', errorMsg, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          errorDetails
        })
        
        throw new Error(`${errorMsg} - ${errorDetails}`)
      }

      const result = await response.json()
      this.logger.log('INFO', `异步排班任务提交成功 [${requestId}]`, {
        success: result.success,
        problemId: result.problemId
      })
      
      return result
    } catch (error) {
      this.logger.log('ERROR', `异步排班请求异常 [${requestId}]`, {
        error: (error as Error).message,
        stack: (error as Error).stack
      })
      throw new Error(`异步排班服务调用失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取排班结果
   */
  async getScheduleResult(problemId: string): Promise<OptaPlannerResponse> {
    try {
      this.logger.log('DEBUG', `获取排班结果`, { problemId })
      
      const response = await fetch(`${this.baseUrl}/result/${problemId}`)
      
      this.logger.log('DEBUG', `排班结果响应`, {
        problemId,
        status: response.status,
        statusText: response.statusText
      })
      
      if (!response.ok) {
        let errorDetails = ''
        try {
          errorDetails = await response.text()
        } catch (e) {
          errorDetails = '无法读取错误详情'
        }
        
        const errorMsg = `获取排班结果失败: ${response.status} ${response.statusText}`
        this.logger.log('ERROR', errorMsg, {
          problemId,
          status: response.status,
          statusText: response.statusText,
          errorDetails
        })
        
        throw new Error(`${errorMsg} - ${errorDetails}`)
      }

      const result = await response.json()
      this.logger.log('INFO', `排班结果获取成功`, {
        problemId,
        success: result.success,
        assignmentsCount: result.assignments?.length || 0
      })

      return result
    } catch (error) {
      this.logger.log('ERROR', `获取排班结果异常`, {
        problemId,
        error: (error as Error).message,
        stack: (error as Error).stack
      })
      throw new Error(`获取排班结果失败: ${(error as Error).message}`)
    }
  }

  /**
   * 取消排班任务
   */
  async cancelSchedule(problemId: string): Promise<boolean> {
    try {
      this.logger.log('DEBUG', `取消排班任务`, { problemId })
      
      const response = await fetch(`${this.baseUrl}/cancel/${problemId}`, {
        method: 'DELETE'
      })
      
      const success = response.ok
      this.logger.log(success ? 'INFO' : 'WARN', `排班任务取消${success ? '成功' : '失败'}`, {
        problemId,
        status: response.status,
        statusText: response.statusText
      })
      
      return success
    } catch (error) {
      this.logger.log('ERROR', `取消排班任务异常`, {
        problemId,
        error: (error as Error).message
      })
      return false
    }
  }

  /**
   * 获取服务状态
   */
  async getServiceStatus(): Promise<any> {
    try {
      this.logger.log('DEBUG', '获取服务状态')
      
      const response = await fetch(`${this.baseUrl}/status`)
      
      this.logger.log('DEBUG', '服务状态响应', {
        status: response.status,
        statusText: response.statusText
      })
      
      if (!response.ok) {
        const errorMsg = `获取服务状态失败: ${response.status}`
        this.logger.log('ERROR', errorMsg, {
          status: response.status,
          statusText: response.statusText
        })
        throw new Error(errorMsg)
      }

      const result = await response.json()
      this.logger.log('INFO', '服务状态获取成功', result)

      return result
    } catch (error) {
      this.logger.log('ERROR', '获取服务状态异常', {
        error: (error as Error).message,
        stack: (error as Error).stack
      })
      throw error
    }
  }

  /**
   * 获取服务日志
   */
  getServiceLogs(): Array<{timestamp: string, level: string, message: string, data?: any}> {
    return this.logger.getLogs()
  }

  /**
   * 清理服务日志
   */
  clearServiceLogs() {
    this.logger.clearLogs()
  }

  /**
   * 诊断服务连接
   */
  async diagnoseConnection(): Promise<{
    healthy: boolean
    baseUrl: string
    endpoints: Array<{
      path: string
      status: 'ok' | 'error' | 'timeout'
      responseTime?: number
      error?: string
    }>
  }> {
    const diagnosis = {
      healthy: false,
      baseUrl: this.baseUrl,
      endpoints: [] as Array<{
        path: string
        status: 'ok' | 'error' | 'timeout'
        responseTime?: number
        error?: string
      }>
    }

    const testEndpoints = [
      '/health',
      '/status',
      '/generate',
      '/solve'
    ]

    this.logger.log('INFO', '开始诊断服务连接', { baseUrl: this.baseUrl, endpoints: testEndpoints })

    for (const endpoint of testEndpoints) {
      const startTime = Date.now()
      try {
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method: endpoint === '/generate' || endpoint === '/solve' ? 'POST' : 'GET',
          headers: { 'Content-Type': 'application/json' },
          body: endpoint === '/generate' || endpoint === '/solve' ? JSON.stringify({
            students: [],
            teachers: [],
            startDate: '2025-01-01',
            endDate: '2025-01-02'
          }) : undefined,
          signal: AbortSignal.timeout(5000) // 5秒超时
        })

        const responseTime = Date.now() - startTime
        diagnosis.endpoints.push({
          path: endpoint,
          status: response.ok ? 'ok' : 'error',
          responseTime,
          error: response.ok ? undefined : `${response.status} ${response.statusText}`
        })

        if (response.ok && endpoint === '/health') {
          diagnosis.healthy = true
        }

      } catch (error) {
        const responseTime = Date.now() - startTime
        diagnosis.endpoints.push({
          path: endpoint,
          status: responseTime > 4900 ? 'timeout' : 'error',
          responseTime,
          error: (error as Error).message
        })
      }
    }

    this.logger.log('INFO', '服务连接诊断完成', diagnosis)
    return diagnosis
  }
}

/**
 * 计算时间集中度评分
 * 用于前端预评估和后端约束验证
 * @param examAssignments 考试分配结果
 * @param weight 权重（默认60）
 * @returns 时间集中度惩罚分数
 */
export function calculateTimeConcentrationScore(
  examAssignments: Array<{ examDate: string }>,
  weight: number = 60
): { score: number; dailyCount: Record<string, number>; stdDev: number } {
  const dailyExamCount: Record<string, number> = {}
  
  // 统计每日考试数量
  examAssignments.forEach(assignment => {
    const date = assignment.examDate
    dailyExamCount[date] = (dailyExamCount[date] || 0) + 1
  })
  
  const counts = Object.values(dailyExamCount)
  
  if (counts.length <= 1) {
    return { score: 0, dailyCount: dailyExamCount, stdDev: 0 }
  }

  const avg = counts.reduce((a, b) => a + b, 0) / counts.length
  const variance = counts.reduce((sum, c) => sum + Math.pow(c - avg, 2), 0) / counts.length
  const stdDev = Math.sqrt(variance)
  
  // 计算惩罚分：标准差越大，惩罚越重
  const penalty = Math.round(stdDev * weight)
  
  return {
    score: -penalty, // 返回负数表示惩罚
    dailyCount: dailyExamCount,
    stdDev: Math.round(stdDev * 100) / 100
  }
}

// 导出默认实例
export const optaPlannerService = new OptaPlannerService()
