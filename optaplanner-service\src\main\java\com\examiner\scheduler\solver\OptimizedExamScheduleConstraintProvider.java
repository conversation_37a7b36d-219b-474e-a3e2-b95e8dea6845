package com.examiner.scheduler.solver;

import com.examiner.scheduler.domain.*;
import com.examiner.scheduler.config.HolidayConfig;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;
import org.optaplanner.core.api.score.stream.ConstraintProvider;
import org.optaplanner.core.api.score.stream.ConstraintCollectors;
import org.optaplanner.core.api.score.stream.Joiners;
import com.examiner.model.UnifiedConstraintConfiguration;
import com.examiner.model.UnifiedConstraintConfiguration.HardConstraint;
import com.examiner.model.UnifiedConstraintConfiguration.SoftConstraint;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.List;
import java.util.ArrayList;
import com.examiner.scheduler.rest.ConstraintViolationSyncResource;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 优化后的考试排班约束提供者
 * 集成统一约束配置系统，支持动态约束管理
 * 
 * 硬约束（必须满足）：
 * - HC1: 法定节假日不安排考试（周六周日可以考试，但行政班考官周末不参加考试）（权重：100000）🔥
 * - HC2: 考官1与学员同科室（权重：100000）🔥
 * - HC3: 考官执勤白班不能安排考试（行政班考官除外）（权重：100000）🔥
 * - HC4: 每名考官每天只能监考一名考生（权重：100000）🔥
 * - HC5: 考生执勤白班不能安排考试（已合并到HC6）
 * - HC6: 考生需要在连续两天完成考试（权重：100000）🔥
 * - HC7: 必须有考官1和考官2两名考官，且不能同科室（权重：100000）🔥
 * - HC8: 备份考官不能与考官1和考官2是同一人（权重：100000）🔥
 * 
 * 🔧 所有硬约束权重统一设置为100000，确保绝对优先级，远高于所有软约束之和
 * 
 * 软约束（优先满足）：
 * - SC1: 晚班考官优先级最高权重（权重：100）
 * - SC2: 考官2专业匹配（权重：90）
 * - SC3: 休息第一天考官优先级次高权重（权重：80）
 * - SC4: 备份考官专业匹配（权重：70）
 * - SC5: 休息第二天考官优先级中等权重（权重：60）
 * - SC6: 考官2备选方案（权重：50）
 * - SC7: 行政班考官优先级最低权重（权重：40）
 * - SC8: 备份考官备选方案（权重：30）
 * - SC9: 区域协作鼓励（权重：20）
 * - SC10: 工作量均衡（权重：10）
 * - SC11: 日期分配均衡（权重：5）
 * - SC14: 同一学员Day1和Day2考官二应来自推荐科室池中的不同科室（权重：85）🆕
 * - SC15: 鼓励同一学员两天考试使用不同考官1（权重：60）🆕
 */
public class OptimizedExamScheduleConstraintProvider implements ConstraintProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(OptimizedExamScheduleConstraintProvider.class);
    private final HolidayConfig holidayConfig;
    
    // 当前约束配置（传统配置）
    private static OptimizedConstraintConfiguration currentConstraintConfig;
    
    // 统一约束配置（新配置系统）
    private UnifiedConstraintConfiguration unifiedConstraintConfig;
    
    // 约束统计信息
    private static final Map<String, AtomicInteger> constraintExecutionCount = new HashMap<>();
    private static final Map<String, AtomicInteger> constraintMatchCount = new HashMap<>();
    private static final Map<String, AtomicInteger> constraintTotalScore = new HashMap<>();
    
    // 初始化约束统计
    static {
        String[] hardConstraints = {"HC1", "HC2", "HC3", "HC4", "HC6", "HC7", "HC8"}; // HC5已合并到HC6
        String[] softConstraints = {"SC1", "SC2", "SC3", "SC4", "SC5", "SC6", "SC7", "SC8", "SC9", "SC10", "SC11", "SC14", "SC15"};

        // 初始化硬约束统计
        for (String constraint : hardConstraints) {
            constraintExecutionCount.put(constraint, new AtomicInteger(0));
            constraintMatchCount.put(constraint, new AtomicInteger(0));
            constraintTotalScore.put(constraint, new AtomicInteger(0));
        }

        // 初始化软约束统计
        for (String constraint : softConstraints) {
            constraintExecutionCount.put(constraint, new AtomicInteger(0));
            constraintMatchCount.put(constraint, new AtomicInteger(0));
            constraintTotalScore.put(constraint, new AtomicInteger(0));
        }
    }
    
    public OptimizedExamScheduleConstraintProvider() {
        this.holidayConfig = new HolidayConfig();
        logger.info("🚀 [约束系统] 约束提供者初始化完成，准备执行约束评估");
    }
    
    /**
     * 设置当前约束配置（传统配置）
     */
    public static void setConstraintConfiguration(OptimizedConstraintConfiguration config) {
        currentConstraintConfig = config;
        logger.info("约束配置已更新: {}", config != null ? "已设置" : "已清空");
    }
    
    /**
     * 获取当前约束配置（传统配置）
     */
    public static OptimizedConstraintConfiguration getConstraintConfiguration() {
        return currentConstraintConfig;
    }
    
    /**
     * 设置统一约束配置（新配置系统）
     */
    public void setUnifiedConstraintConfiguration(UnifiedConstraintConfiguration config) {
        this.unifiedConstraintConfig = config;
        logger.info("统一约束配置已更新: {}", config != null ? config.getConfigurationId() : "已清空");
    }
    
    /**
     * 获取统一约束配置（新配置系统）
     */
    public UnifiedConstraintConfiguration getUnifiedConstraintConfiguration() {
        return unifiedConstraintConfig;
    }
    
    /**
     * 检查约束是否启用（优先使用统一配置）
     */
    private boolean isConstraintEnabled(String constraintId) {
        // 优先使用统一约束配置
        if (unifiedConstraintConfig != null) {
            // 检查硬约束
            if (unifiedConstraintConfig.getHardConstraints() != null) {
                for (UnifiedConstraintConfiguration.HardConstraint hc : unifiedConstraintConfig.getHardConstraints().values()) {
                    if (constraintId.equals(hc.getId())) {
                        return hc.getStatus() == UnifiedConstraintConfiguration.ConstraintStatus.ENABLED;
                    }
                }
            }
            // 检查软约束
            if (unifiedConstraintConfig.getSoftConstraints() != null) {
                for (UnifiedConstraintConfiguration.SoftConstraint sc : unifiedConstraintConfig.getSoftConstraints().values()) {
                    if (constraintId.equals(sc.getId())) {
                        return sc.getStatus() == UnifiedConstraintConfiguration.ConstraintStatus.ENABLED;
                    }
                }
            }
        }
        
        // 🔧 关键修复：强制启用所有硬约束，不依赖配置
        // 硬约束是必须满足的规则，不应该被禁用
        // 回退到传统配置
        if (currentConstraintConfig != null) {
            switch (constraintId) {
                // 🔧 修复：所有硬约束强制启用 - 硬约束不应该被禁用!
                case "HC1": return true; // HC1: 法定节假日不安排考试 🔧 强制启用
                case "HC2": return true; // HC2: 考官1与学员同科室 🔧 强制启用 (关键!)
                case "HC3": return true; // HC3: 考官执勤白班不能安排考试 🔧 强制启用
                case "HC4": return true; // HC4: 每名考官每天只能监考一名考生 🔧 强制启用
                // HC5已合并到HC6中
                case "HC6": return true; // HC6: 学员连续两天考试+白班限制约束 🔧 强制启用
                case "HC7": return true; // HC7: 必须有考官1和考官2两名考官 🔧 强制启用 (关键!)
                case "HC8": return true; // HC8: 备份考官不能与考官1和考官2是同一人 🔧 强制启用
                
                // 🔧 修复：软约束启用状态映射 - 强制启用所有软约束以确保执行
                case "SC1": return true; // SC1: 晚班考官优先级最高权重 🔧 强制启用
                case "SC2": return true; // SC2: 考官2专业匹配 🔧 强制启用
                case "SC3": return true; // SC3: 休息第一天考官优先级次高权重 🔧 强制启用
                case "SC4": return true; // SC4: 备份考官专业匹配 🔧 强制启用
                case "SC5": return true; // SC5: 休息第二天考官优先级中等权重 🔧 强制启用
                case "SC6": return true; // SC6: 考官2备选方案 🔧 强制启用
                case "SC7": return true; // SC7: 行政班考官优先级最低权重 🔧 强制启用
                case "SC8": return true; // SC8: 备份考官备选方案 🔧 强制启用
                case "SC9": return true; // SC9: 区域协作鼓励 🔧 强制启用
                case "SC10": return true; // SC10: 工作量均衡 🔧 强制启用
                case "SC11": return true; // SC11: 日期分配均衡 🔧 强制启用
                case "SC12": return true; // SC12: 🔧 备份考官工作量均衡（默认启用）
                case "SC13": return true; // SC13: 🔧 限制行政班担任主考官（默认启用）
                case "SC14": return true; // SC14: 🔧 Day1/Day2考官二科室互斥（默认启用）🆕
            }
        }
        
        // 默认启用
        return true;
    }
    
    /**
     * 获取约束权重（优先使用统一配置）
     */
    @SuppressWarnings("unused")
    private HardSoftScore getConstraintWeight(String constraintId) {
        return getConstraintWeight(constraintId, HardSoftScore.ofSoft(1));
    }
    
    /**
     * 获取约束权重（带默认权重参数）
     */
    private HardSoftScore getConstraintWeight(String constraintId, HardSoftScore defaultWeight) {
        HardSoftScore finalWeight;
        String weightSource = "默认权重";
        
        // 优先使用统一约束配置
        if (unifiedConstraintConfig != null) {
            // 检查硬约束
            for (HardConstraint hc : unifiedConstraintConfig.getHardConstraints().values()) {
                if (constraintId.equals(hc.getId())) {
                    finalWeight = HardSoftScore.ofHard(hc.getWeight());
                    weightSource = "统一配置-硬约束";
                    logger.debug("🎯 [权重计算] {} 使用{}: {}", constraintId, weightSource, finalWeight);
                    return finalWeight;
                }
            }
            // 检查软约束
            for (SoftConstraint sc : unifiedConstraintConfig.getSoftConstraints().values()) {
                if (constraintId.equals(sc.getId())) {
                    finalWeight = HardSoftScore.ofSoft(sc.getWeight());
                    weightSource = "统一配置-软约束";
                    logger.debug("🎯 [权重计算] {} 使用{}: {}", constraintId, weightSource, finalWeight);
                    return finalWeight;
                }
            }
        }
        
        // 🔧 修复：无论配置是否为null，都使用传统权重作为后备
        // 回退到传统配置（无论currentConstraintConfig是否为null）
        {
            weightSource = "传统配置";
            switch (constraintId) {
                // 🔧 修复：统一所有硬约束权重为100000，确保硬约束绝对优先级
                // 100000 >> 所有软约束总和(~2000)，避免硬约束被软约束"压制"
                case "HC1": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 5000→100000
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (法定节假日限制)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC2": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 300000→100000 (统一权重)
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (考官1与学员同科室)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC3": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 280000→100000 (统一权重)
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (考官执勤白班限制)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC4": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 250000→100000 (统一权重)
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (每名考官每天只能监考一名考生)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC5": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 6000→100000
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (考生执勤白班限制)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC6": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 4000→100000 (关键!)
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (考生连续两天完成考试)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC7": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 10000→100000 (关键!)
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (考官1和考官2且不能同科室)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "HC8": 
                    finalWeight = HardSoftScore.ofHard(100000);  // 🔧 修复: 3000→100000 (关键!)
                    logger.debug("⚖️ [权重计算] {} 使用{}: {} (备份考官不能重复)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                
                // 软约束权重映射 - 🔧 优化权重配置，确保更好的约束平衡
                case "SC1": 
                    finalWeight = HardSoftScore.ofSoft(150);  // 🔧 从100提升到150
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (晚班考官优先级-高)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC2": 
                    finalWeight = HardSoftScore.ofSoft(100);  // 🔧 从90提升到100
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (考官2专业匹配-高)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC3": 
                    finalWeight = HardSoftScore.ofSoft(120);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (休息第一天考官优先级次高)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC4": 
                    finalWeight = HardSoftScore.ofSoft(80);  // 🔧 从70提升到80
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (备份考官专业匹配-中高)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC5": 
                    finalWeight = HardSoftScore.ofSoft(40);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (休息第二天考官优先级中等)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC6": 
                    finalWeight = HardSoftScore.ofSoft(50);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (考官2备选方案)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC7": 
                    finalWeight = HardSoftScore.ofSoft(60);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (行政班备份考官优先)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC8": 
                    finalWeight = HardSoftScore.ofSoft(30);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (备份考官备选方案)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC9": 
                    finalWeight = HardSoftScore.ofSoft(20);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (区域协作鼓励)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC14":
                    finalWeight = HardSoftScore.ofSoft(110);  // 🆕 高优先级：Day1和Day2考官二来自不同推荐科室
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (Day1/Day2考官二科室互斥)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC15":
                    finalWeight = HardSoftScore.ofSoft(60);  // 🆕 中等优先级：鼓励同一学员两天考试使用不同考官1
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (鼓励考官1多样性)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC10":
                    finalWeight = HardSoftScore.ofSoft(400);  // 🔧 从500降低到400，避免过度关注
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (工作量均衡-高优先级)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC11": 
                    finalWeight = HardSoftScore.ofSoft(300);  // 🔧 从200提升到300，大幅提升日期均衡优先级
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (日期分配均衡-最高优先级)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC12": 
                    finalWeight = HardSoftScore.ofSoft(50);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (备份考官工作量均衡)", constraintId, weightSource, finalWeight);
                    return finalWeight;
                case "SC13": 
                    finalWeight = HardSoftScore.ofSoft(30);  // 保持
                    logger.debug("🎯 [权重计算] {} 使用{}: {} (限制行政班担任主考官)", constraintId, weightSource, finalWeight);
                    return finalWeight;
            }
        }
        
        // 🔧 修复：返回默认权重时不再警告，因为传统配置已涵盖所有约束
        logger.debug("🔧 [权重计算] {} 使用最终默认权重: {} (未在传统配置中定义)", constraintId, defaultWeight);
        return defaultWeight;
    }

    @Override
    public Constraint[] defineConstraints(ConstraintFactory constraintFactory) {
        System.out.println("=== defineConstraints 方法被调用 ===");
        
        // 返回所有硬约束和软约束
        Constraint[] constraints = new Constraint[]{
            // 硬约束 HC1-HC8（所有硬约束权重统一为100000，确保绝对优先级）
            consecutiveTwoDaysExam(constraintFactory),              // HC6: 考生需要在连续两天完成考试（权重：100000）🔥
            noExaminerTimeConflict(constraintFactory),              // HC4: 每名考官每天只能监考一名考生（权重：100000）🔥
            examinerDepartmentRules(constraintFactory),             // HC2: 考官1与学员同科室（权重：100000）🔥
            mustHaveTwoDifferentDepartmentExaminers(constraintFactory), // HC7: 必须有考官1和考官2两名考官，且不能同科室（权重：100000）🔥
            noDayShiftExaminerConstraint(constraintFactory),        // HC3: 考官执勤白班不能安排考试（行政班考官除外）（权重：100000）🔥
            workdaysOnlyExam(constraintFactory),                    // HC1: 法定节假日不安排考试（权重：100000）🔥
            backupExaminerMustBeDifferentPerson(constraintFactory), // HC8: 备份考官不能与考官1和考官2是同一人（权重：100000）🔥
            
            // 软约束 SC1-SC15（按权重从高到低排序）
            preferNightShiftTeachers(constraintFactory),            // SC1: 晚班考官优先级最高权重（权重：150）
            preferDifferentRecommendedDeptsForDay1Day2(constraintFactory), // SC14: Day1/Day2考官二来自不同推荐科室（权重：110）🆕
            preferRecommendedExaminer2(constraintFactory),          // SC2: 考官2专业匹配（权重：100）
            preferFirstRestDayTeachers(constraintFactory),          // SC3: 休息第一天考官优先级次高权重（权重：120）
            preferRecommendedBackupExaminer(constraintFactory),     // SC4: 备份考官专业匹配（权重：70）
            encourageDifferentExaminer1ForTwoDays(constraintFactory), // SC15: 鼓励同一学员两天考试使用不同考官1（权重：60）🆕
            preferSecondRestDayTeachers(constraintFactory),         // SC5: 休息第二天考官优先级中等权重（权重：60）
            balanceBackupExaminerWorkload(constraintFactory),       // SC12: 🔧 备份考官工作量均衡（权重：50）
            preferNonRecommendedExaminer2(constraintFactory),       // SC6: 考官2备选方案（权重：50）
            preferAdminTeachers(constraintFactory),                 // SC7: 行政班备份考官优先（权重：60）
            limitAdminAsMainExaminers(constraintFactory),        // SC13: 限制行政班担任主考官（权重：30）
            preferNonRecommendedBackupExaminer(constraintFactory),  // SC8: 备份考官备选方案（权重：30）
            allowDept37CrossUse(constraintFactory),                 // SC9: 区域协作鼓励（权重：20）
            balanceWorkload(constraintFactory),                     // SC10: 考官1工作量均衡+连续工作惩罚（权重：500）
            balanceExaminer2Workload(constraintFactory),            // SC10b: 考官2连续工作惩罚（权重：500）
            balanceBackupWorkload(constraintFactory),               // SC10c: 备份考官连续工作惩罚（权重：500）
            preferLaterDates(constraintFactory)                     // SC11: 日期分配均衡（权重：5）
        };
        
        System.out.println("定义的约束数量: " + constraints.length);
        for (int i = 0; i < constraints.length; i++) {
            System.out.println("约束 " + (i + 1) + ": " + constraints[i].getConstraintName());
        }
        System.out.println("=== defineConstraints 方法完成 ===");
        
        return constraints;
    }

    // ==================== 硬约束实现 ====================
    
    /**
     * HC1: 法定节假日不安排考试（周六周日可以考试，但行政班考官周末不参加考试）
     * - 法定节假日（如春节、国庆节等）禁止安排考试
     * - 周六、周日可以安排考试
     * - 行政班考官（当日执勤白班的班组）在周末不能参与考试
     * - 夜班考官和休息班组考官可以在周末参与考试
     * - 权重：5000（硬约束）
     */
    private Constraint workdaysOnlyExam(ConstraintFactory constraintFactory) {
        logger.info("🚫 [HC1约束] 初始化: 法定节假日不安排考试约束 (权重:5000)");
        
        if (!isConstraintEnabled("HC1")) {
            logger.warn("⚠️ [HC1约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("workdaysOnlyExam");
        }
        
        logger.info("✅ [HC1约束] 约束已启用，开始检查节假日和周末限制");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    logger.debug("🔍 [HC1约束] 检查学员: {} 考试日期: {}", 
                            assignment.getStudent() != null ? assignment.getStudent().getName() : "未知", 
                            assignment.getExamDate());
                    
                    if (assignment.getExamDate() == null) {
                        logger.debug("❌ [HC1约束] 考试日期为空，跳过检查");
                        recordConstraintExecution("HC1", false, 0);
                        return false;
                    }
                    
                    try {
                        LocalDate date = LocalDate.parse(assignment.getExamDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        int dayOfWeek = date.getDayOfWeek().getValue();
                        String dayName = getDayName(dayOfWeek);
                        
                        logger.debug("🔍 [HC1约束] 解析日期: {} ({}) 星期{}", assignment.getExamDate(), dayName, dayOfWeek);
                        
                        // 法定节假日禁止排班
                        if (holidayConfig.isHoliday(date)) {
                            logger.error("🚫 [HC1约束] 违反! 学员{} 考试安排在法定节假日: {} ({})", 
                                    assignment.getStudent() != null ? assignment.getStudent().getName() : "未知",
                                    assignment.getExamDate(), dayName);
                            recordConstraintExecution("HC1", true, 5000);
                            return true;
                        }
                        
                        // 周末时，检查是否有行政班考官参与
                        if (dayOfWeek == 6 || dayOfWeek == 7) { // 周六或周日
                            logger.debug("🔍 [HC1约束] 周末日期检查: {} ({})", assignment.getExamDate(), dayName);
                            
                            boolean hasAdminTeacherViolation = false;
                            
                            // 检查考官1是否为行政班考官
                            if (assignment.getExaminer1() != null && isAdminTeacher(assignment.getExaminer1())) {
                                logger.error("🚫 [HC1约束] 违反! 考官1 {} (班组:{}) 为行政班考官，周末({})不能参与考试", 
                                        assignment.getExaminer1().getName(),
                                        assignment.getExaminer1().getGroup(),
                                        dayName);
                                hasAdminTeacherViolation = true;
                            }
                            
                            // 检查考官2是否为行政班考官
                            if (assignment.getExaminer2() != null && isAdminTeacher(assignment.getExaminer2())) {
                                logger.error("🚫 [HC1约束] 违反! 考官2 {} (班组:{}) 为行政班考官，周末({})不能参与考试", 
                                        assignment.getExaminer2().getName(),
                                        assignment.getExaminer2().getGroup(),
                                        dayName);
                                hasAdminTeacherViolation = true;
                            }
                            
                            // 检查备份考官是否为行政班考官
                            if (assignment.getBackupExaminer() != null && isAdminTeacher(assignment.getBackupExaminer())) {
                                logger.error("🚫 [HC1约束] 违反! 备份考官 {} (班组:{}) 为行政班考官，周末({})不能参与考试", 
                                        assignment.getBackupExaminer().getName(),
                                        assignment.getBackupExaminer().getGroup(),
                                        dayName);
                                hasAdminTeacherViolation = true;
                            }
                            
                            if (hasAdminTeacherViolation) {
                                recordConstraintExecution("HC1", true, 5000);
                                return true;
                            } else {
                                logger.debug("✅ [HC1约束] 周末安排合规: 无行政班考官参与 {}", assignment.getExamDate());
                            }
                        } else {
                            logger.debug("✅ [HC1约束] 工作日安排: {} ({})", assignment.getExamDate(), dayName);
                        }
                        
                        recordConstraintExecution("HC1", false, 0);
                        return false; // 不违反约束
                    } catch (Exception e) {
                        logger.error("🚫 [HC1约束] 日期解析错误: {} - {}", assignment.getExamDate(), e.getMessage());
                        recordConstraintExecution("HC1", true, 5000);
                        return true; // 日期格式错误也视为违反
                    }
                })
                .penalize(getConstraintWeight("HC1", HardSoftScore.ofHard(100000))) // HC1权重：100000 🔥 硬约束必须绝对满足
                .asConstraint("workdaysOnlyExam");
    }
    
    /**
     * 获取星期几的中文名称
     */
    private String getDayName(int dayOfWeek) {
        switch (dayOfWeek) {
            case 1: return "星期一";
            case 2: return "星期二";
            case 3: return "星期三";
            case 4: return "星期四";
            case 5: return "星期五";
            case 6: return "星期六";
            case 7: return "星期日";
            default: return "未知";
        }
    }
    
    /**
     * HC2: 考官1与学员同科室
     * - 考官1必须与考生同科室（或3室7室互通）
     * - 考官1与考官2需来自不同科室
     */
    private Constraint examinerDepartmentRules(ConstraintFactory constraintFactory) {
        logger.info("🏢 [HC2约束] 初始化: 考官1与学员同科室约束 (权重:8000)");
        
        if (!isConstraintEnabled("HC2")) {
            logger.warn("⚠️ [HC2约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("examinerDepartmentRules");
        }
        
        logger.info("✅ [HC2约束] 约束已启用，开始检查科室匹配规则");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    // 🔧 改为INFO级别，确保能看到所有检查
                    String assignmentId = assignment.getId() != null ? assignment.getId() : "未知ID";
                    String studentName = assignment.getStudent() != null ? assignment.getStudent().getName() : "未知学员";
                    String examDate = assignment.getExamDate() != null ? assignment.getExamDate() : "未知日期";
                    
                    logger.info("🔍 [HC2约束-START] Assignment={}, 学员={}, 日期={}", 
                            assignmentId, studentName, examDate);
                    
                    // 只有当考官和学员都已分配时才检查科室规则
                    if (assignment.getExaminer1() == null || 
                        assignment.getExaminer2() == null || 
                        assignment.getStudent() == null) {
                        logger.warn("⚠️ [HC2约束-SKIP] Assignment={} 考官或学员未完全分配，跳过检查 (考官1={}, 考官2={}, 学员={})",
                                assignmentId,
                                assignment.getExaminer1() != null ? "已分配" : "null",
                                assignment.getExaminer2() != null ? "已分配" : "null",
                                assignment.getStudent() != null ? "已分配" : "null");
                        recordConstraintExecution("HC2", false, 0);
                        return false; // 未分配时跳过检查，由twoMainExaminersRequired约束处理
                    }
                    
                    String studentDept = normalizeDepartment(assignment.getStudent().getDepartment());
                    String examiner1Dept = normalizeDepartment(assignment.getExaminer1().getDepartment());
                    String examiner2Dept = normalizeDepartment(assignment.getExaminer2().getDepartment());
                    
                    // 🔧 新增：检测null（数据错误 - 非法科室名称）
                    if (studentDept == null) {
                        logger.error("🚨 [HC2约束] 严重错误! 学员{} 科室数据为null或非法: 原始值={}", 
                                assignment.getStudent().getName(), 
                                assignment.getStudent().getDepartment());
                        recordConstraintExecution("HC2", true, 100000);
                        return true;  // 强制违反
                    }
                    
                    if (examiner1Dept == null) {
                        logger.error("🚨 [HC2约束] 严重错误! 考官1{} 科室数据为null或非法: 原始值={}", 
                                assignment.getExaminer1().getName(), 
                                assignment.getExaminer1().getDepartment());
                        recordConstraintExecution("HC2", true, 100000);
                        return true;  // 强制违反
                    }
                    
                    if (examiner2Dept == null) {
                        logger.error("🚨 [HC2约束] 严重错误! 考官2{} 科室数据为null或非法: 原始值={}", 
                                assignment.getExaminer2().getName(), 
                                assignment.getExaminer2().getDepartment());
                        recordConstraintExecution("HC2", true, 100000);
                        return true;  // 强制违反
                    }
                    
                    logger.info("🔍 [HC2约束-CHECK] 学员{}({}), 考官1{}({}), 考官2{}({}), 日期={}", 
                            assignment.getStudent().getName(), studentDept,
                            assignment.getExaminer1().getName(), examiner1Dept,
                            assignment.getExaminer2().getName(), examiner2Dept,
                            examDate);
                    
                    // 检查考官1是否与考生同科室（或3室7室互通）
                    boolean examiner1Valid = isValidExaminer1Department(studentDept, examiner1Dept);
                    
                    // 🔧 修复: 考官2逻辑 - 考官2必须与学员不同科室
                    boolean examiner2Valid = !Objects.equals(studentDept, examiner2Dept);
                    
                    // 检查考官1和考官2是否来自不同科室
                    boolean differentExaminers = !Objects.equals(examiner1Dept, examiner2Dept);
                    
                    // 详细的违反条件检查和日志记录
                    boolean hasViolation = false;
                    
                    if (!examiner1Valid) {
                        logger.error("🚨🚨🚨 [HC2约束-VIOLATION] Assignment={}, 考官1科室不匹配! 学员{}({}) vs 考官1{}({}), 日期={}", 
                                assignmentId,
                                assignment.getStudent().getName(), studentDept,
                                assignment.getExaminer1().getName(), examiner1Dept,
                                examDate);
                        hasViolation = true;
                    }
                    
                    if (!examiner2Valid) {
                        logger.error("🚨🚨🚨 [HC2约束-VIOLATION] Assignment={}, 考官2与学员同科室! 学员{}({}) vs 考官2{}({}), 日期={}", 
                                assignmentId,
                                assignment.getStudent().getName(), studentDept,
                                assignment.getExaminer2().getName(), examiner2Dept,
                                examDate);
                        hasViolation = true;
                    }
                    
                    if (!differentExaminers) {
                        logger.error("🚨🚨🚨 [HC2约束-VIOLATION] Assignment={}, 考官1和考官2来自同一科室! 考官1{}({}) vs 考官2{}({}), 日期={}", 
                                assignmentId,
                                assignment.getExaminer1().getName(), examiner1Dept,
                                assignment.getExaminer2().getName(), examiner2Dept,
                                examDate);
                        hasViolation = true;
                    }
                    
                    if (hasViolation) {
                        logger.error("🚨 [HC2约束-PENALIZE] Assignment={} 将被惩罚，返回true触发penalize", assignmentId);
                        recordConstraintExecution("HC2", true, 8000);
                        return true;
                    } else {
                        logger.info("✅ [HC2约束-PASS] Assignment={} 科室配置合规", assignmentId);
                        recordConstraintExecution("HC2", false, 0);
                        return false;
                    }
                })
                .penalize(getConstraintWeight("HC2", HardSoftScore.ofHard(1000000000))) // 🚨 HC2权重：10亿！绝对最高优先级，确保考官1科室规则必须满足
                .asConstraint("examinerDepartmentRules");
    }
    
    /**
     * HC3: 考官执勤白班不能安排考试（行政班考官除外）
     * - 权重：7000（硬约束）
     * - 检查考官1、考官2、备份考官的工作安排
     * - 如果任一考官在考试日期执勤白班，则不能安排考试
     * - 行政班考官不受四班组轮班制度限制，可以在任何时间安排考试（除法定节假日和周末）
     */
    private Constraint noDayShiftExaminerConstraint(ConstraintFactory constraintFactory) {
        logger.info("🌅 [HC3约束] 初始化: 考官执勤白班不能安排考试约束 (权重:7000)");
        
        if (!isConstraintEnabled("HC3")) {
            logger.warn("⚠️ [HC3约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("noDayShiftExaminerConstraint");
        }
        
        logger.info("✅ [HC3约束] 约束已启用，开始检查考官白班执勤冲突");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getExamDate() == null) {
                        return false;
                    }
                    
                    // 🔧 修复：使用domain.DutySchedule的静态方法获取值班安排
                    com.examiner.scheduler.domain.DutySchedule dutySchedule = 
                        com.examiner.scheduler.domain.DutySchedule.forDate(assignment.getExamDate());
                    
                    logger.debug("🔍 [HC3约束] 检查学员: {} 考试日期: {} 白班值勤: {}", 
                            assignment.getStudent() != null ? assignment.getStudent().getName() : "未知", 
                            assignment.getExamDate(),
                            dutySchedule.getDayShift());
                    
                    boolean hasViolation = false;
                    
                    // 检查考官1是否在白班值勤（行政班考官除外）
                    if (assignment.getExaminer1() != null) {
                        boolean isAdmin = isAdminTeacher(assignment.getExaminer1());
                        boolean isDayShift = Objects.equals(assignment.getExaminer1().getGroup(), dutySchedule.getDayShift());
                        
                        logger.debug("🔍 [HC3约束] 考官1检查: {} (班组:{}) 行政班:{} 白班执勤:{}", 
                                assignment.getExaminer1().getName(),
                                assignment.getExaminer1().getGroup(),
                                isAdmin ? "是" : "否",
                                isDayShift ? "是" : "否");
                        
                        if (!isAdmin && isDayShift) {
                            logger.error("🚫 [HC3约束] 违反! 考官1 {} (班组:{}) 在白班执勤，不能参与考试 日期:{}", 
                                    assignment.getExaminer1().getName(),
                                    assignment.getExaminer1().getGroup(),
                                    assignment.getExamDate());
                            hasViolation = true;
                        }
                    }
                    
                    // 检查考官2是否在白班值勤（行政班考官除外）
                    if (assignment.getExaminer2() != null) {
                        boolean isAdmin = isAdminTeacher(assignment.getExaminer2());
                        boolean isDayShift = Objects.equals(assignment.getExaminer2().getGroup(), dutySchedule.getDayShift());
                        
                        logger.debug("🔍 [HC3约束] 考官2检查: {} (班组:{}) 行政班:{} 白班执勤:{}", 
                                assignment.getExaminer2().getName(),
                                assignment.getExaminer2().getGroup(),
                                isAdmin ? "是" : "否",
                                isDayShift ? "是" : "否");
                        
                        if (!isAdmin && isDayShift) {
                            logger.error("🚫 [HC3约束] 违反! 考官2 {} (班组:{}) 在白班执勤，不能参与考试 日期:{}", 
                                    assignment.getExaminer2().getName(),
                                    assignment.getExaminer2().getGroup(),
                                    assignment.getExamDate());
                            hasViolation = true;
                        }
                    }
                    
                    // 检查备份考官是否在白班值勤（行政班考官除外）
                    if (assignment.getBackupExaminer() != null) {
                        boolean isAdmin = isAdminTeacher(assignment.getBackupExaminer());
                        boolean isDayShift = Objects.equals(assignment.getBackupExaminer().getGroup(), dutySchedule.getDayShift());
                        
                        logger.debug("🔍 [HC3约束] 备份考官检查: {} (班组:{}) 行政班:{} 白班执勤:{}", 
                                assignment.getBackupExaminer().getName(),
                                assignment.getBackupExaminer().getGroup(),
                                isAdmin ? "是" : "否",
                                isDayShift ? "是" : "否");
                        
                        if (!isAdmin && isDayShift) {
                            logger.error("🚫 [HC3约束] 违反! 备份考官 {} (班组:{}) 在白班执勤，不能参与考试 日期:{}", 
                                    assignment.getBackupExaminer().getName(),
                                    assignment.getBackupExaminer().getGroup(),
                                    assignment.getExamDate());
                            hasViolation = true;
                        }
                    }
                    
                    if (hasViolation) {
                        recordConstraintExecution("HC3", true, 7000);
                        return true;
                    } else {
                        logger.debug("✅ [HC3约束] 无白班执勤冲突: 所有考官均可参与考试 日期:{}", assignment.getExamDate());
                        recordConstraintExecution("HC3", false, 0);
                        return false;
                    }
                })
                .penalize(getConstraintWeight("HC3", HardSoftScore.ofHard(100000))) // HC3权重：100000 🔥 硬约束必须绝对满足
                .asConstraint("noDayShiftExaminerConstraint");
    }
    
    /**
     * HC6: 考生需要在连续两天完成考试
     */
    // 连续两天考试约束 - 包级别可见性以便测试访问
    public Constraint consecutiveTwoDaysExam(ConstraintFactory constraintFactory) {
        // 🔧 强制调试输出
        System.out.println("🔥🔥🔥 HC6约束方法被调用！！！");
        System.err.println("🔥🔥🔥 HC6约束方法被调用！！！");
        logger.error("🔥🔥🔥 HC6约束方法被调用！！！");
        
        if (!isConstraintEnabled("HC6")) {
            System.out.println("❌❌❌ HC6约束被禁用！！！");
            System.err.println("❌❌❌ HC6约束被禁用！！！");
            logger.error("❌❌❌ HC6约束被禁用！！！");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("consecutiveTwoDaysExam");
        }
        
        System.out.println("✅✅✅ HC6约束已启用，开始执行检查！！！");
        System.err.println("✅✅✅ HC6约束已启用，开始执行检查！！！");
        logger.error("✅✅✅ HC6约束已启用，开始执行检查！！！");
        
        System.out.println("=== HC6约束方法被调用 ===");
        logger.warn("🔥 HC6连续两天考试约束开始执行 (权重: 50000)");
        
        // 🔧 HC6修复版：分别检查白班限制和连续性
        return constraintFactory.forEach(ExamAssignment.class)
                .filter(assignment -> {
                    System.out.println("🔍🔍🔍 HC6检查考试分配: " + assignment.getStudent().getName() + " 日期: " + assignment.getExamDate());
                    System.err.println("🔍🔍🔍 HC6检查考试分配: " + assignment.getStudent().getName() + " 日期: " + assignment.getExamDate());
                    logger.error("🔍🔍🔍 HC6检查考试分配: {} 日期: {}", assignment.getStudent().getName(), assignment.getExamDate());
                    
                    Student student = assignment.getStudent();
                    String examDate = assignment.getExamDate();
                    
                    if (student == null || examDate == null) {
                        return false;
                    }
                    
                    // 🔍 检查：学员在白班值勤日不能参加考试
                    DutySchedule dutySchedule = DutySchedule.forDate(examDate);
                    boolean isStudentOnDayShift = Objects.equals(student.getGroup(), dutySchedule.getDayShift());
                    
                    System.out.println("🔍🔍🔍 白班班组: " + dutySchedule.getDayShift() + " 学员班组: " + student.getGroup() + " 是否白班: " + isStudentOnDayShift);
                    System.err.println("🔍🔍🔍 白班班组: " + dutySchedule.getDayShift() + " 学员班组: " + student.getGroup() + " 是否白班: " + isStudentOnDayShift);
                    logger.error("🔍🔍🔍 白班班组: {} 学员班组: {} 是否白班: {}", dutySchedule.getDayShift(), student.getGroup(), isStudentOnDayShift);
                    
                    if (isStudentOnDayShift) {
                        System.out.println("🚨🚨🚨 HC6违反-白班: " + student.getName() + " 在 " + examDate);
                        System.err.println("🚨🚨🚨 HC6违反-白班: " + student.getName() + " 在 " + examDate);
                        logger.error("🚨 [HC6违反-白班] 学员 {} (班组:{}) 在白班执勤日 {} 被安排考试!", 
                                student.getName(), student.getGroup(), examDate);
                        recordConstraintExecution("HC6", true, 25000);
                        return true; // 违反约束
                    }
                    
                    recordConstraintExecution("HC6", false, 0);
                    return false; // 符合约束
                })
                .penalize(getConstraintWeight("HC6", HardSoftScore.ofHard(100000))) // HC6权重：100000 🔥 硬约束必须绝对满足
                .asConstraint("consecutiveTwoDaysExam");
    }
    
    /**
     * 🔧 修复：使用现有的DutySchedule算法判断学员在指定日期是否为白班执勤
     */
    @SuppressWarnings("unused")
    private boolean isStudentOnDayShiftDuty(Student student, String examDate) {
        try {
            String studentGroup = student.getGroup();
            if (studentGroup == null) {
                return false; // 没有班组信息，不认为违反约束
            }
            
            // 🎯 使用现有的DutySchedule算法进行准确计算
            DutySchedule dutySchedule = DutySchedule.forDate(examDate);
            boolean isOnDayShift = dutySchedule.isGroupOnDayShift(studentGroup);
            
            if (isOnDayShift) {
                logger.debug("🔍 [HC6检查] 学员 {} (班组:{}) 在 {} 为白班执勤 → 违反约束", 
                        student.getName(), studentGroup, examDate);
            } else {
                logger.debug("✅ [HC6检查] 学员 {} (班组:{}) 在 {} 非白班执勤 → 符合要求", 
                        student.getName(), studentGroup, examDate);
            }
            
            return isOnDayShift;
            
        } catch (Exception e) {
            logger.warn("⚠️ 无法判断学员班组状态: {}", e.getMessage());
            return false; // 异常时不认为违反约束
        }
    }
    
    /**
     * 🔧 新增：判断是否为法定节假日
     */
    @SuppressWarnings("unused")
    private boolean isHoliday(LocalDate date) {
        try {
            // 使用HolidayConfig来判断是否为节假日
            if (holidayConfig != null) {
                // 如果不是工作日，但是周末，则不是节假日
                if (!holidayConfig.isWorkingDay(date)) {
                    DayOfWeek dayOfWeek = date.getDayOfWeek();
                    if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                        return false; // 周末不算节假日
                    }
                    return true; // 非工作日且非周末，认为是节假日
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 🔧 新增：验证学员连续考试天数是否合理
     */
    @SuppressWarnings("unused")
    private boolean isValidConsecutiveDays(Student student, LocalDate date1, LocalDate date2) {
        logger.debug("=== isValidConsecutiveDays 开始检查 ===");
        logger.debug("学员: {}, 组别: {}", student.getName(), student.getGroup());
        logger.debug("首次考试日期: {}, 最后考试日期: {}", date1, date2);
        
        // �� 修复关键错误：同一天考试是违反约束的！
        if (date1.equals(date2)) {
            logger.warn("❌ 硬约束违反: 学员 {} 的两次考试安排在同一天 {}", student.getName(), date1);
            return false;  // 同一天考试违反连续两天约束
        }
        
        // 检查是否在连续的两天内
        long daysBetween = ChronoUnit.DAYS.between(date1, date2);
        logger.debug("间隔天数: {}", daysBetween);
        
        // 🔧 修复：连续两天考试的约束很简单，间隔必须正好是1天
        boolean isValid = (daysBetween == 1);
        
        if (isValid) {
            logger.debug("✅ 约束满足: 考试间隔正好1天");
        } else {
            logger.warn("❌ 硬约束违反: 学员 {} 考试间隔为{}天，不符合连续两天要求", 
                       student.getName(), daysBetween);
        }
        
        logger.debug("=== isValidConsecutiveDays 检查结束，结果: {} ===", isValid);
        return isValid;
    }
    
    /**
     * HC4: 每名考官每天只能监考一名考生
     * - 权重：9000（硬约束）
     * - 防止考官工作负荷过重
     * - 确保考试质量和公平性
     * - 检查所有考官角色（考官1、考官2、备份考官）
     */
    public Constraint noExaminerTimeConflict(ConstraintFactory constraintFactory) {
        logger.info("⏰ [HC4约束] 初始化: 每名考官每天只能监考一名考生约束 (权重:9000)");
        
        if (!isConstraintEnabled("HC4")) {
            logger.warn("⚠️ [HC4约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("noExaminerTimeConflict");
        }
        
        logger.info("✅ [HC4约束] 约束已启用，开始检查考官时间冲突");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .join(ExamAssignment.class,
                    // 不同的考试分配
                    Joiners.lessThan(ExamAssignment::getId),
                    // 同一天
                    Joiners.equal(ExamAssignment::getExamDate))
                .filter((assignment1, assignment2) -> {
                    // 🚀 性能优化：移除filter内的DEBUG日志，避免性能瓶颈
                    boolean hasConflict = false;
                    String conflictTeacher = null; // 记录冲突的考官姓名
                    
                    // 检查考官1是否重复
                    if (assignment1.getExaminer1() != null && assignment2.getExaminer1() != null &&
                        Objects.equals(assignment1.getExaminer1().getId(), assignment2.getExaminer1().getId())) {
                        conflictTeacher = assignment1.getExaminer1().getName();
                        logger.error("🚫 [HC4约束] 违反! 考官1时间冲突: {} 在 {} 同时监考 {} 和 {}", 
                                conflictTeacher,
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    // 检查考官2是否重复
                    if (assignment1.getExaminer2() != null && assignment2.getExaminer2() != null &&
                        Objects.equals(assignment1.getExaminer2().getId(), assignment2.getExaminer2().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 考官2时间冲突: {} 在 {} 同时监考 {} 和 {}", 
                                assignment1.getExaminer2().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    // 检查备份考官是否重复
                    if (assignment1.getBackupExaminer() != null && assignment2.getBackupExaminer() != null &&
                        Objects.equals(assignment1.getBackupExaminer().getId(), assignment2.getBackupExaminer().getId())) {
                        if (conflictTeacher == null) conflictTeacher = assignment1.getBackupExaminer().getName();
                        logger.error("🚫 [HC4约束] 违反! 备份考官时间冲突: {} 在 {} 同时监考 {} 和 {}", 
                                assignment1.getBackupExaminer().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    // 检查交叉重复（考官1与考官2、备份考官）
                    if (assignment1.getExaminer1() != null && assignment2.getExaminer2() != null &&
                        Objects.equals(assignment1.getExaminer1().getId(), assignment2.getExaminer2().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 交叉时间冲突: {} 在 {} 同时担任考官1({})和考官2({})", 
                                assignment1.getExaminer1().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    if (assignment1.getExaminer2() != null && assignment2.getExaminer1() != null &&
                        Objects.equals(assignment1.getExaminer2().getId(), assignment2.getExaminer1().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 交叉时间冲突: {} 在 {} 同时担任考官2({})和考官1({})", 
                                assignment1.getExaminer2().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    // 检查考官1与备份考官的交叉重复
                    if (assignment1.getExaminer1() != null && assignment2.getBackupExaminer() != null &&
                        Objects.equals(assignment1.getExaminer1().getId(), assignment2.getBackupExaminer().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 交叉时间冲突: {} 在 {} 同时担任考官1({})和备份考官({})", 
                                assignment1.getExaminer1().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    if (assignment1.getBackupExaminer() != null && assignment2.getExaminer1() != null &&
                        Objects.equals(assignment1.getBackupExaminer().getId(), assignment2.getExaminer1().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 交叉时间冲突: {} 在 {} 同时担任备份考官({})和考官1({})", 
                                assignment1.getBackupExaminer().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    // 检查考官2与备份考官的交叉重复
                    if (assignment1.getExaminer2() != null && assignment2.getBackupExaminer() != null &&
                        Objects.equals(assignment1.getExaminer2().getId(), assignment2.getBackupExaminer().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 交叉时间冲突: {} 在 {} 同时担任考官2({})和备份考官({})", 
                                assignment1.getExaminer2().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    if (assignment1.getBackupExaminer() != null && assignment2.getExaminer2() != null &&
                        Objects.equals(assignment1.getBackupExaminer().getId(), assignment2.getExaminer2().getId())) {
                        logger.error("🚫 [HC4约束] 违反! 交叉时间冲突: {} 在 {} 同时担任备份考官({})和考官2({})", 
                                assignment1.getBackupExaminer().getName(),
                                assignment1.getExamDate(),
                                assignment1.getStudent() != null ? assignment1.getStudent().getName() : "未知",
                                assignment2.getStudent() != null ? assignment2.getStudent().getName() : "未知");
                        hasConflict = true;
                    }
                    
                    if (hasConflict) {
                        // 增强日志：记录具体冲突的考官和原因
                        logger.warn("⚠️ [HC4约束] 考官工作负荷过重: {} 在同一天被分配多个角色", conflictTeacher);
                        recordConstraintExecution("HC4", true, 9000);
                        return true;
                    } else {
                        logger.debug("✅ [HC4约束] 无时间冲突: 考官安排正常 日期:{}", assignment1.getExamDate());
                        recordConstraintExecution("HC4", false, 0);
                        return false;
                    }
                })
                .penalize(getConstraintWeight("HC4", HardSoftScore.ofHard(100000))) // HC4权重：100000 🔥 硬约束必须绝对满足
                .asConstraint("noExaminerTimeConflict");
    }
    

    
    /**
     * HC7: 必须有考官1和考官2两名考官，且不能同科室
     */
    private Constraint mustHaveTwoDifferentDepartmentExaminers(ConstraintFactory constraintFactory) {
        if (!isConstraintEnabled("HC7")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("mustHaveTwoDifferentDepartmentExaminers");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    // 检查是否缺少考官1或考官2
                    if (assignment.getExaminer1() == null || assignment.getExaminer2() == null) {
                        return true; // 缺少考官违反约束
                    }
                    
                    // 检查考官1和考官2是否来自同一科室
                    String examiner1Dept = normalizeDepartment(assignment.getExaminer1().getDepartment());
                    String examiner2Dept = normalizeDepartment(assignment.getExaminer2().getDepartment());
                    
                    // 如果来自同一科室，违反约束
                    return Objects.equals(examiner1Dept, examiner2Dept);
                })
                .penalize(getConstraintWeight("HC7", HardSoftScore.ofHard(100000))) // HC7权重：100000 🔥 硬约束必须绝对满足
                .asConstraint("mustHaveTwoDifferentDepartmentExaminers");
    }
    
    /**
     * HC8: 备份考官不能与考官1和考官2是同一人
     */
    private Constraint backupExaminerMustBeDifferentPerson(ConstraintFactory constraintFactory) {
        if (!isConstraintEnabled("HC8")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("backupExaminerMustBeDifferentPerson");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    // 如果没有备份考官，不违反约束
                    if (assignment.getBackupExaminer() == null) {
                        return false;
                    }
                    
                    // 检查备份考官是否与考官1是同一人
                    if (assignment.getExaminer1() != null &&
                        Objects.equals(assignment.getBackupExaminer().getId(), assignment.getExaminer1().getId())) {
                        return true;
                    }
                    
                    // 检查备份考官是否与考官2是同一人
                    if (assignment.getExaminer2() != null &&
                        Objects.equals(assignment.getBackupExaminer().getId(), assignment.getExaminer2().getId())) {
                        return true;
                    }
                    
                    return false;
                })
                .penalize(getConstraintWeight("HC8", HardSoftScore.ofHard(100000))) // HC8权重：100000 🔥 硬约束必须绝对满足
                .asConstraint("backupExaminerMustBeDifferentPerson");
    }
    

    
    // ==================== 软约束实现 ====================
    

    

    

    

    
    /**
     * SC10: 考官工作量均衡考量（包含连续工作惩罚）
     */
    private Constraint balanceWorkload(ConstraintFactory constraintFactory) {
        // 🔧 修复：使用正确的约束ID SC10
        if (!isConstraintEnabled("SC10")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("balanceWorkload");
        }
        
        logger.info("🔧 执行工作量均衡约束 SC10 - 全角色统计 + 连续工作惩罚");
        
        // 🔧 使用考官1的分配来检查连续工作模式
        return constraintFactory
                .forEach(ExamAssignment.class)
                .groupBy(assignment -> assignment.getExaminer1(), 
                        ConstraintCollectors.toList())
                .filter((teacher, assignments) -> teacher != null && assignments.size() >= 1)
                .penalize(getConstraintWeight("SC10", HardSoftScore.ofSoft(500)),
                    (teacher, assignments) -> {
                        // 基础工作量惩罚
                        int totalCount = assignments.size();
                        int basePenalty = totalCount > 2 ? (int) Math.pow(totalCount - 2, 3) : 0;
                        
                        // 🔧 新增：连续工作天数惩罚
                        int consecutivePenalty = calculateConsecutiveWorkPenalty(teacher, assignments);
                        
                        int totalPenalty = basePenalty + consecutivePenalty;
                        
                        if (totalPenalty > 0) {
                            logger.warn("⚠️ [SC10] 考官1 {} 工作量分析: 总次数={}, 基础惩罚={}, 连续工作惩罚={}, 总惩罚={}", 
                                    teacher.getName(), totalCount, basePenalty, consecutivePenalty, totalPenalty);
                            recordConstraintExecution("SC10", true, totalPenalty);
                        } else {
                            recordConstraintExecution("SC10", false, 0);
                        }
                        return totalPenalty;
                    })
                .asConstraint("balanceWorkload");
    }
    
    /**
     * 计算连续工作天数的惩罚分数
     * 重点惩罚连续多天工作且跨越值班周期的情况
     */
    private int calculateConsecutiveWorkPenalty(Teacher teacher, java.util.List<ExamAssignment> assignments) {
        if (assignments.size() < 2) {
            return 0; // 少于2次工作不构成连续性问题
        }
        
        // 收集所有考试日期
        java.util.Set<String> examDates = new java.util.HashSet<>();
        for (ExamAssignment assignment : assignments) {
            if (assignment.getExamDate() != null) {
                examDates.add(assignment.getExamDate());
            }
        }
        
        // 转换为排序的日期列表
        java.util.List<java.time.LocalDate> sortedDates = examDates.stream()
                .map(dateStr -> java.time.LocalDate.parse(dateStr))
                .sorted()
                .collect(java.util.stream.Collectors.toList());
        
        if (sortedDates.size() < 2) {
            return 0;
        }
        
        int penalty = 0;
        
        // 检查连续考试日期的情况
        for (int i = 0; i < sortedDates.size() - 1; i++) {
            java.time.LocalDate currentDate = sortedDates.get(i);
            java.time.LocalDate nextDate = sortedDates.get(i + 1);
            
            // 检查是否为连续日期
            if (currentDate.plusDays(1).equals(nextDate)) {
                // 连续两天考试，检查是否跨越值班周期
                int consecutiveWorkStress = calculateWorkStressLevel(teacher, currentDate, nextDate);
                penalty += consecutiveWorkStress;
                
                logger.warn("🚨 [连续工作] 考官 {} 在 {} 和 {} 连续两天考试，压力等级惩罚: {}", 
                        teacher.getName(), currentDate, nextDate, consecutiveWorkStress);
            }
        }
        
        return penalty;
    }
    
    /**
     * 计算跨越值班周期的工作压力等级
     * 重点检查：晚班 → 考试 → 考试 → 早班 这种四天连续的情况
     */
    private int calculateWorkStressLevel(Teacher teacher, java.time.LocalDate date1, java.time.LocalDate date2) {
        String teacherGroup = teacher.getGroup();
        if (teacherGroup == null) {
            return 0;
        }
        
        // 检查date1前一天和date2后一天的值班情况
        java.time.LocalDate preDayDate = date1.minusDays(1);
        java.time.LocalDate postDayDate = date2.plusDays(1);
        
        String preDayDateStr = preDayDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String date1Str = date1.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String date2Str = date2.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String postDayDateStr = postDayDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        com.examiner.scheduler.domain.DutySchedule preDaySchedule = 
            com.examiner.scheduler.domain.DutySchedule.forDate(preDayDateStr);
        com.examiner.scheduler.domain.DutySchedule postDaySchedule = 
            com.examiner.scheduler.domain.DutySchedule.forDate(postDayDateStr);
        
        boolean isPreDayNightShift = Objects.equals(teacherGroup, preDaySchedule.getNightShift());
        boolean isPostDayDayShift = Objects.equals(teacherGroup, postDaySchedule.getDayShift());
        
        // 🚨 最严重情况：晚班 → 考试 → 考试 → 早班（四天连续）
        if (isPreDayNightShift && isPostDayDayShift) {
            logger.error("🚨 [极高压力] 考官 {} 四天连续工作: {}晚班 → {}考试 → {}考试 → {}早班", 
                    teacher.getName(), preDayDateStr, date1Str, date2Str, postDayDateStr);
            return 50; // 🔧 修复：返回倍数而不是绝对值 (50倍 × SC10权重500 = 25000分惩罚)
        }
        
        // 🔶 严重情况：晚班 → 考试 → 考试 或 考试 → 考试 → 早班（三天连续）
        if (isPreDayNightShift || isPostDayDayShift) {
            String pattern = isPreDayNightShift ? "晚班→考试→考试" : "考试→考试→早班";
            logger.warn("🔶 [高压力] 考官 {} 三天连续工作模式: {}", teacher.getName(), pattern);
            return 15; // 🔧 修复：返回倍数 (15倍 × SC10权重500 = 7500分惩罚)
        }
        
        // ⚠️ 一般情况：仅连续两天考试
        logger.info("⚠️ [中等压力] 考官 {} 连续两天考试: {} → {}", 
                teacher.getName(), date1Str, date2Str);
                 return 2; // 🔧 修复：返回倍数 (2倍 × SC10权重500 = 1000分惩罚)
     }
     
     /**
      * SC10b: 考官2连续工作惩罚
      */
     private Constraint balanceExaminer2Workload(ConstraintFactory constraintFactory) {
         if (!isConstraintEnabled("SC10")) {
             return constraintFactory.forEach(ExamAssignment.class)
                     .filter(assignment -> false)
                     .penalize(HardSoftScore.ZERO)
                     .asConstraint("balanceExaminer2Workload");
         }
         
         return constraintFactory
                 .forEach(ExamAssignment.class)
                 .groupBy(assignment -> assignment.getExaminer2(), 
                         ConstraintCollectors.toList())
                 .filter((teacher, assignments) -> teacher != null && assignments.size() >= 2)
                 .penalize(getConstraintWeight("SC10", HardSoftScore.ofSoft(500)),
                     (teacher, assignments) -> {
                         int consecutivePenalty = calculateConsecutiveWorkPenalty(teacher, assignments);
                         if (consecutivePenalty > 0) {
                             logger.warn("⚠️ [SC10b] 考官2 {} 连续工作惩罚: {}", 
                                     teacher.getName(), consecutivePenalty);
                         }
                         return consecutivePenalty;
                     })
                 .asConstraint("balanceExaminer2Workload");
     }
     
     /**
      * SC10c: 备份考官连续工作惩罚
      */
     private Constraint balanceBackupWorkload(ConstraintFactory constraintFactory) {
         if (!isConstraintEnabled("SC10")) {
             return constraintFactory.forEach(ExamAssignment.class)
                     .filter(assignment -> false)
                     .penalize(HardSoftScore.ZERO)
                     .asConstraint("balanceBackupWorkload");
         }
         
         return constraintFactory
                 .forEach(ExamAssignment.class)
                 .groupBy(assignment -> assignment.getBackupExaminer(), 
                         ConstraintCollectors.toList())
                 .filter((teacher, assignments) -> teacher != null && assignments.size() >= 2)
                 .penalize(getConstraintWeight("SC10", HardSoftScore.ofSoft(500)),
                     (teacher, assignments) -> {
                         int consecutivePenalty = calculateConsecutiveWorkPenalty(teacher, assignments);
                         if (consecutivePenalty > 0) {
                             logger.warn("⚠️ [SC10c] 备份考官 {} 连续工作惩罚: {}", 
                                     teacher.getName(), consecutivePenalty);
                         }
                         return consecutivePenalty;
                     })
                 .asConstraint("balanceBackupWorkload");
     }
     
     /**
      * SC12: 备份考官工作量均衡考量
      * 🔧 新增：确保备份考官分配均匀，避免某个考官被过度分配为备份考官
      */
    private Constraint balanceBackupExaminerWorkload(ConstraintFactory constraintFactory) {
        // 🔧 修复：使用正确的约束ID SC12 (但在约束注册中是SC12，所以保持不变)
        if (!isConstraintEnabled("SC12")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("balanceBackupExaminerWorkload");
        }
        
        logger.info("🔧 执行备份考官工作量均衡约束 SC12");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> assignment.getBackupExaminer() != null)
                .groupBy(ExamAssignment::getBackupExaminer, ConstraintCollectors.count())
                .penalize(getConstraintWeight("SC12", HardSoftScore.ofSoft(50)),  // SC12: 备份考官工作量均衡
                    (backupExaminer, count) -> {
                        // 计算惩罚分数：分配次数的平方，鼓励均匀分布
                        int penalty = (int) Math.pow(count - 1, 2);
                        logger.info("🎯 备份考官 {} 被分配 {} 次，惩罚分数: {}", 
                                backupExaminer.getName(), count, penalty);
                        return penalty;
                    })
                .asConstraint("balanceBackupExaminerWorkload");
    }
    
    /**
     * SC11: 考试日期分配均衡考量
     * 尽量将考试时间均匀分配，避免集中在某些日期
     * 
     * 🔧 修复：改进日期均衡算法
     * - 计算每天的考试数量
     * - 惩罚偏离平均值的情况
     * - 权重提升到200，确保日期分布均匀
     */
    private Constraint preferLaterDates(ConstraintFactory constraintFactory) {
        // 🔧 修复：使用正确的约束ID SC11
        if (!isConstraintEnabled("SC11")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferLaterDates");
        }
        
        // 🔧 改进的日期均衡算法
        // 惩罚每天考试数量超过4个的情况（假设理想分配是每天2-4个）
        return constraintFactory
                .forEach(ExamAssignment.class)
                .groupBy(ExamAssignment::getExamDate, ConstraintCollectors.count())
                .filter((examDate, examCount) -> examCount > 4)  // 🔧 只惩罚明显过多的情况
                .penalize(getConstraintWeight("SC11", HardSoftScore.ofSoft(200)),  // 🔧 权重从50提升到200
                    (examDate, examCount) -> {
                        // 🔧 改进的惩罚函数：超过4个后，每多一个惩罚指数增加
                        int excess = examCount - 4;  // 超出的数量
                        return excess * excess * examCount;  // 指数惩罚，越集中惩罚越重
                    })
                .asConstraint("preferLaterDates");
    }
    

    
    /**
     * SC9: 允许3室与7室考官资源互通使用
     */
    private Constraint allowDept37CrossUse(ConstraintFactory constraintFactory) {
        // 🔧 修复：使用正确的约束ID SC9
        if (!isConstraintEnabled("SC9")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("allowDept37CrossUse");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getStudent() == null || assignment.getExaminer1() == null) {
                        return false;
                    }
                    
                    String studentDept = normalizeDepartment(assignment.getStudent().getDepartment());
                    String examiner1Dept = normalizeDepartment(assignment.getExaminer1().getDepartment());
                    
                    // 奖励条件：3室7室互通使用
                    return (Objects.equals(studentDept, "三") && Objects.equals(examiner1Dept, "七")) ||
                           (Objects.equals(studentDept, "七") && Objects.equals(examiner1Dept, "三"));
                })
                .reward(getConstraintWeight("SC9", HardSoftScore.ofSoft(20)))  // SC9: 区域协作鼓励
                .asConstraint("allowDept37CrossUse");
    }
    

    
    // ==================== 辅助方法 ====================
    
    /**
     * 判断是否为行政班考官
     * 行政班考官：工作日上班的考官，不受四班组轮班制度限制
     * 特征：group = "无" 或为空值
     */
    private boolean isAdminTeacher(Teacher teacher) {
        if (teacher == null) {
            return false;
        }
        String group = teacher.getGroup();
        return group == null || "无".equals(group) || group.trim().isEmpty();
    }
    
    /**
     * 科室名称标准化
     * 🔧 增强版：检测非法科室名称（考试科目等）
     */
    private String normalizeDepartment(String department) {
        if (department == null) return null;
        
        String normalized = department.trim();
        
        // 🔧 新增：检测非法科室名称（考试科目关键词）
        String[] illegalKeywords = {"模拟机", "现场", "口试", "理论", "实操", "实践", "笔试"};
        for (String keyword : illegalKeywords) {
            if (normalized.contains(keyword)) {
                logger.error("🚨 [数据错误] 检测到非法科室名称: \"{}\" - 这可能是考试科目，不是科室！", normalized);
                // 返回null，将在HC2约束中被检测为数据错误
                return null;
            }
        }
        
        // 标准化映射（与前端保持完全一致，包括"第X科室"格式）
        if (normalized.contains("区域一室") || normalized.contains("一室") || normalized.contains("1室") || normalized.contains("第1科室")) return "一";
        if (normalized.contains("区域二室") || normalized.contains("二室") || normalized.contains("2室") || normalized.contains("第2科室")) return "二";
        if (normalized.contains("区域三室") || normalized.contains("三室") || normalized.contains("3室") || normalized.contains("第3科室")) return "三";
        if (normalized.contains("区域四室") || normalized.contains("四室") || normalized.contains("4室") || normalized.contains("第4科室")) return "四";
        if (normalized.contains("区域五室") || normalized.contains("五室") || normalized.contains("5室") || normalized.contains("第5科室")) return "五";
        if (normalized.contains("区域六室") || normalized.contains("六室") || normalized.contains("6室") || normalized.contains("第6科室")) return "六";
        if (normalized.contains("区域七室") || normalized.contains("七室") || normalized.contains("7室") || normalized.contains("第7科室")) return "七";
        if (normalized.contains("区域八室") || normalized.contains("八室") || normalized.contains("8室") || normalized.contains("第8科室")) return "八";
        if (normalized.contains("区域九室") || normalized.contains("九室") || normalized.contains("9室") || normalized.contains("第9科室")) return "九";
        if (normalized.contains("区域十室") || normalized.contains("十室") || normalized.contains("10室") || normalized.contains("第10科室")) return "十";
        
        // 🔧 新增：如果没有匹配任何已知科室，记录警告
        if (!normalized.isEmpty() && !normalized.equals("无") && !normalized.equals("未分配")) {
            logger.warn("⚠️ [数据警告] 未识别的科室名称: \"{}\" - 请检查数据是否正确", normalized);
        }
        
        return normalized;
    }
    
    /**
     * 验证考官1科室是否有效
     * 优化：增强三室七室互通机制，解决科室匹配过严问题
     */
    private boolean isValidExaminer1Department(String studentDept, String examiner1Dept) {
        if (studentDept == null || examiner1Dept == null) return false;
        
        // 同科室（优先匹配）
        if (Objects.equals(studentDept, examiner1Dept)) {
            logger.debug("✅ [HC2-MATCH] 同科室匹配: 学员{} = 考官1{}", studentDept, examiner1Dept);
            return true;
        }
        
        // 三室七室互通（特殊规则）
        if ((Objects.equals(studentDept, "三") && Objects.equals(examiner1Dept, "七")) ||
            (Objects.equals(studentDept, "七") && Objects.equals(examiner1Dept, "三"))) {
            logger.debug("✅ [HC2-CROSS] 三七室互通匹配: 学员{} ↔ 考官1{}", studentDept, examiner1Dept);
            return true;
        }
        
        logger.debug("❌ [HC2-FAIL] 科室不匹配: 学员{} vs 考官1{}", studentDept, examiner1Dept);
        return false;
    }
    
    /**
     * 🔧 优化: 考官2科室特殊匹配检查，专门针对三室七室互通
     */
    @SuppressWarnings("unused")
    private boolean isSpecialDepartmentCombination(String studentDept, String examiner2Dept) {
        if (studentDept == null || examiner2Dept == null) return false;
        
        // 考官2需要与学员不同科室，但允许三室七室特殊互通情况
        // 例如：学员在三室，考官2可以来自七室（但不能同科室）
        boolean isDifferentDept = !Objects.equals(studentDept, examiner2Dept);
        
        if (!isDifferentDept) {
            logger.debug("❌ [HC2-E2] 考官2与学员同科室，违反规则: 学员{} = 考官2{}", studentDept, examiner2Dept);
            return false; // 考官2不能与学员同科室
        }
        
        // 三室七室互通：允许更灵活的考官2分配
        if ((Objects.equals(studentDept, "三") || Objects.equals(studentDept, "七")) &&
            (Objects.equals(examiner2Dept, "三") || Objects.equals(examiner2Dept, "七"))) {
            logger.debug("✅ [HC2-E2-CROSS] 三七室区域考官2匹配: 学员{} → 考官2{}", studentDept, examiner2Dept);
            return true;
        }
        
        // 其他科室：正常的不同科室规则
        logger.debug("✅ [HC2-E2-NORMAL] 正常不同科室匹配: 学员{} → 考官2{}", studentDept, examiner2Dept);
        return true;
    }
    
    // ==================== 新增优先级约束实现 ====================
    
    /**
     * SC10: 优先安排执勤晚班的考官（第一优先级）
     * 权重等级仅次于科室推荐，优先选择晚班考官作为考官2和备份考官
     * 特殊规则：晚班考官若同时属于推荐科室，分数可叠加计算
     */
    private Constraint preferNightShiftTeachers(ConstraintFactory constraintFactory) {
        // 🔧 修复：使用正确的约束ID
        if (!isConstraintEnabled("SC1")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferNightShiftTeachers");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getExamDate() == null) {
                        return false;
                    }
                    
                    // 🔧 修复：使用domain.DutySchedule的静态方法
                    com.examiner.scheduler.domain.DutySchedule dutySchedule = 
                        com.examiner.scheduler.domain.DutySchedule.forDate(assignment.getExamDate());
                    boolean hasNightShiftTeacher = false;
                    
                    // 🔧 新增：检查考官1是否为晚班考官
                    if (assignment.getExaminer1() != null && 
                        assignment.getExaminer1().getGroup() != null &&
                        Objects.equals(assignment.getExaminer1().getGroup(), dutySchedule.getNightShift())) {
                        hasNightShiftTeacher = true;
                        // 记录晚班考官优先级决策日志
                        logger.info("优先级决策 - 晚班考官: 考官1 {} (班组: {}) 在 {} 为晚班考官，获得第一优先级", 
                                assignment.getExaminer1().getName(), 
                                assignment.getExaminer1().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 检查考官2是否为晚班考官
                    if (assignment.getExaminer2() != null && 
                        assignment.getExaminer2().getGroup() != null &&
                        Objects.equals(assignment.getExaminer2().getGroup(), dutySchedule.getNightShift())) {
                        hasNightShiftTeacher = true;
                        // 记录晚班考官优先级决策日志
                        logger.info("优先级决策 - 晚班考官: 考官2 {} (班组: {}) 在 {} 为晚班考官，获得第一优先级", 
                                assignment.getExaminer2().getName(), 
                                assignment.getExaminer2().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 检查备份考官是否为晚班考官
                    if (assignment.getBackupExaminer() != null &&
                        assignment.getBackupExaminer().getGroup() != null &&
                        Objects.equals(assignment.getBackupExaminer().getGroup(), dutySchedule.getNightShift())) {
                        hasNightShiftTeacher = true;
                        // 记录晚班考官优先级决策日志
                        logger.info("优先级决策 - 晚班考官: 备份考官 {} (班组: {}) 在 {} 为晚班考官，获得第一优先级", 
                                assignment.getBackupExaminer().getName(), 
                                assignment.getBackupExaminer().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    return hasNightShiftTeacher;
                })
                .reward(getConstraintWeight("SC1", HardSoftScore.ofSoft(150)))  // SC1: 晚班考官优先级最高权重 - 从100提高到150
                .asConstraint("preferNightShiftTeachers");
    }
    
    /**
     * SC11: 优先安排休息第一天的考官（第二优先级）
     * 在晚班考官之后，优先选择休息第一天的考官
     */
    private Constraint preferFirstRestDayTeachers(ConstraintFactory constraintFactory) {
        // 🔧 修复：使用正确的约束ID SC3
        if (!isConstraintEnabled("SC3")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferFirstRestDayTeachers");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getExamDate() == null) {
                        return false;
                    }
                    
                    // 🔧 修复：使用domain.DutySchedule的静态方法
                    com.examiner.scheduler.domain.DutySchedule dutySchedule = 
                        com.examiner.scheduler.domain.DutySchedule.forDate(assignment.getExamDate());
                    List<String> restGroups = dutySchedule.getRestGroups();
                    if (restGroups == null || restGroups.isEmpty()) {
                        return false;
                    }
                    
                    String firstRestGroup = restGroups.get(0); // 休息第一天的班组
                    boolean hasFirstRestDayTeacher = false;
                    
                    // 🔧 新增：检查考官1是否为休息第一天考官
                    if (assignment.getExaminer1() != null && 
                        assignment.getExaminer1().getGroup() != null &&
                        Objects.equals(assignment.getExaminer1().getGroup(), firstRestGroup)) {
                        hasFirstRestDayTeacher = true;
                        // 记录休息第一天考官优先级决策日志
                        logger.info("优先级决策 - 休息第一天考官: 考官1 {} (班组: {}) 在 {} 为休息第一天，获得第二优先级", 
                                assignment.getExaminer1().getName(), 
                                assignment.getExaminer1().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 检查考官2是否为休息第一天考官
                    if (assignment.getExaminer2() != null && 
                        assignment.getExaminer2().getGroup() != null &&
                        Objects.equals(assignment.getExaminer2().getGroup(), firstRestGroup)) {
                        hasFirstRestDayTeacher = true;
                        // 记录休息第一天考官优先级决策日志
                        logger.info("优先级决策 - 休息第一天考官: 考官2 {} (班组: {}) 在 {} 为休息第一天，获得第二优先级", 
                                assignment.getExaminer2().getName(), 
                                assignment.getExaminer2().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 检查备份考官是否为休息第一天考官
                    if (assignment.getBackupExaminer() != null && 
                        assignment.getBackupExaminer().getGroup() != null &&
                        Objects.equals(assignment.getBackupExaminer().getGroup(), firstRestGroup)) {
                        hasFirstRestDayTeacher = true;
                        // 记录休息第一天考官优先级决策日志
                        logger.info("优先级决策 - 休息第一天考官: 备份考官 {} (班组: {}) 在 {} 为休息第一天，获得第二优先级", 
                                assignment.getBackupExaminer().getName(), 
                                assignment.getBackupExaminer().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    return hasFirstRestDayTeacher;
                })
                .reward(getConstraintWeight("SC3", HardSoftScore.ofSoft(80)))  // SC3: 休息第一天考官优先级次高权重
                .asConstraint("preferFirstRestDayTeachers");
    }
    
    /**
     * SC5: 优先安排休息第二天的考官（第三优先级）
     * 在休息第一天考官之后，优先选择休息第二天的考官
     */
    private Constraint preferSecondRestDayTeachers(ConstraintFactory constraintFactory) {
        logger.info("💡 [SC5约束] 初始化: 休息第二天考官优先级中等权重约束 (权重:60)");
        
        // 🔧 修复：使用正确的约束ID SC5
        if (!isConstraintEnabled("SC5")) {
            logger.warn("⚠️ [SC5约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferSecondRestDayTeachers");
        }
        
        logger.info("✅ [SC5约束] 约束已启用，开始执行");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getExamDate() == null) {
                        return false;
                    }
                    
                    // 🔧 修复：使用domain.DutySchedule的静态方法
                    com.examiner.scheduler.domain.DutySchedule dutySchedule = 
                        com.examiner.scheduler.domain.DutySchedule.forDate(assignment.getExamDate());
                    logger.debug("🔍 [SC5约束] 检查学员: {} 考试日期: {}", 
                            assignment.getStudent() != null ? assignment.getStudent().getName() : "未知", 
                            assignment.getExamDate());
                    
                    List<String> restGroups = dutySchedule.getRestGroups();
                    if (restGroups == null || restGroups.size() < 2) {
                        logger.debug("❌ [SC5约束] 休息班组信息不足: {}", restGroups);
                        return false;
                    }
                    
                    String secondRestGroup = restGroups.get(1); // 休息第二天的班组
                    boolean hasSecondRestDayTeacher = false;
                    int matchCount = 0;
                    
                    logger.debug("🔍 [SC5约束] 休息第二天班组: {}", secondRestGroup);
                    
                    // 🔧 新增：检查考官1是否为休息第二天考官
                    if (assignment.getExaminer1() != null && 
                        assignment.getExaminer1().getGroup() != null &&
                        Objects.equals(assignment.getExaminer1().getGroup(), secondRestGroup)) {
                        hasSecondRestDayTeacher = true;
                        matchCount++;
                        logger.info("🎯 [SC5约束] 匹配! 考官1 {} (班组:{}) 在 {} 为休息第二天，获得40分奖励", 
                                assignment.getExaminer1().getName(), 
                                assignment.getExaminer1().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 检查考官2是否为休息第二天考官
                    if (assignment.getExaminer2() != null && 
                        assignment.getExaminer2().getGroup() != null &&
                        Objects.equals(assignment.getExaminer2().getGroup(), secondRestGroup)) {
                        hasSecondRestDayTeacher = true;
                        matchCount++;
                        logger.info("🎯 [SC5约束] 匹配! 考官2 {} (班组:{}) 在 {} 为休息第二天，获得40分奖励", 
                                assignment.getExaminer2().getName(), 
                                assignment.getExaminer2().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 检查备份考官是否为休息第二天考官
                    if (assignment.getBackupExaminer() != null && 
                        assignment.getBackupExaminer().getGroup() != null &&
                        Objects.equals(assignment.getBackupExaminer().getGroup(), secondRestGroup)) {
                        hasSecondRestDayTeacher = true;
                        matchCount++;
                        logger.info("🎯 [SC5约束] 匹配! 备份考官 {} (班组:{}) 在 {} 为休息第二天，获得40分奖励", 
                                assignment.getBackupExaminer().getName(), 
                                assignment.getBackupExaminer().getGroup(), 
                                assignment.getExamDate());
                    }
                    
                    // 记录统计信息
                    recordConstraintExecution("SC5", hasSecondRestDayTeacher, hasSecondRestDayTeacher ? 40 : 0);
                    
                    if (!hasSecondRestDayTeacher) {
                        logger.debug("❌ [SC5约束] 无休息第二天考官匹配: 学员{} 日期{}", 
                                assignment.getStudent() != null ? assignment.getStudent().getName() : "未知",
                                assignment.getExamDate());
                    } else {
                        logger.info("✅ [SC5约束] 成功匹配{}个休息第二天考官", matchCount);
                    }
                    
                    return hasSecondRestDayTeacher;
                })
                .reward(getConstraintWeight("SC5", HardSoftScore.ofSoft(60)))  // SC5: 休息第二天考官优先级中等权重
                .asConstraint("preferSecondRestDayTeachers");
    }
    
    /**
     * SC7: 行政班考官作为备份考官优先级
     * 🎯 新规则: 行政班考官优先担任备份考官，推荐科室的行政班考官优先
     */
    private Constraint preferAdminTeachers(ConstraintFactory constraintFactory) {
        if (!isConstraintEnabled("SC7")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferAdminTeachers");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getBackupExaminer() == null) {
                        return false;
                    }
                    
                    // 只检查备份考官是否为行政班考官
                    boolean isAdminBackup = isAdminTeacher(assignment.getBackupExaminer());
                    if (isAdminBackup) {
                        // 检查是否在推荐科室池中
                        boolean isRecommended = isInRecommendedDepartments(assignment.getBackupExaminer(), assignment.getStudent());
                        int score = isRecommended ? 80 : 40; // 推荐科室的行政班考官获得更高分
                        
                        logger.info("✅ [SC7约束] 行政班备份考官: {} (科室: {}) 推荐: {} 分数: {}", 
                                assignment.getBackupExaminer().getName(), 
                                assignment.getBackupExaminer().getDepartment(),
                                isRecommended ? "是" : "否", score);
                        return true;
                    }
                    
                    return false;
                })
                .reward(getConstraintWeight("SC7", HardSoftScore.ofSoft(60))) // 提高行政班备份考官的奖励
                .asConstraint("preferAdminTeachers");
    }
    
    /**
     * 检查考官是否在学员的推荐科室池中
     */
    private boolean isInRecommendedDepartments(Teacher teacher, Student student) {
        if (teacher == null || student == null) {
            return false;
        }
        
        String teacherDept = normalizeDepartment(teacher.getDepartment());
        
        // 检查备份考官推荐科室
        if (student.getRecommendedBackupDept() != null) {
            String backupDept = normalizeDepartment(student.getRecommendedBackupDept());
            if (Objects.equals(teacherDept, backupDept)) {
                return true;
            }
        }
        
        // 检查考官2推荐科室（行政班也可能作为考官2）
        if (student.getRecommendedExaminer2Dept() != null) {
            String examiner2Dept = normalizeDepartment(student.getRecommendedExaminer2Dept());
            if (Objects.equals(teacherDept, examiner2Dept)) {
                return true;
            }
        }
        
        // 检查考官1推荐科室（资源紧张时可能用到）
        if (student.getRecommendedExaminer1Dept() != null) {
            String examiner1Dept = normalizeDepartment(student.getRecommendedExaminer1Dept());
            if (Objects.equals(teacherDept, examiner1Dept)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * SC13: 限制行政班考官担任考官一和考官二
     * 🎯 新规则: 行政班考官优先担任备份考官，只有资源紧张时才担任主考官
     */
    private Constraint limitAdminAsMainExaminers(ConstraintFactory constraintFactory) {
        // 这是一个软约束，用于引导算法优先让行政班考官担任备份考官
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    boolean hasAdminAsMain = false;
                    
                    // 检查考官1是否为行政班考官
                    if (assignment.getExaminer1() != null && isAdminTeacher(assignment.getExaminer1())) {
                        logger.warn("⚠️ [SC13约束] 行政班考官 {} 担任考官1，建议优先安排为备份考官", 
                                assignment.getExaminer1().getName());
                        hasAdminAsMain = true;
                    }
                    
                    // 检查考官2是否为行政班考官
                    if (assignment.getExaminer2() != null && isAdminTeacher(assignment.getExaminer2())) {
                        logger.warn("⚠️ [SC13约束] 行政班考官 {} 担任考官2，建议优先安排为备份考官", 
                                assignment.getExaminer2().getName());
                        hasAdminAsMain = true;
                    }
                    
                    return hasAdminAsMain;
                })
                .penalize(getConstraintWeight("SC13", HardSoftScore.ofSoft(30))) // 适中的惩罚，允许资源紧张时使用
                .asConstraint("limitAdminAsMainExaminers");
    }
    
    /**
     * SC7: 优先安排推荐科室池内的备份考官
     * 备份考官专业匹配 + SC1-SC4优先级分数叠加
     * 🆕 新规则：备份考官也按day1/day2区分推荐科室
     */
    private Constraint preferRecommendedBackupExaminer(ConstraintFactory constraintFactory) {
        // 🔧 修复：添加约束启用检查，使用正确的约束ID SC4
        if (!isConstraintEnabled("SC4")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferRecommendedBackupExaminer");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getStudent() == null || assignment.getBackupExaminer() == null) {
                        return false;
                    }
                    
                    // 🔍 添加详细调试日志
                    logger.info("🔍 [SC4调试] 检查备份考官候选: {} 科室:{} 班组:{} 考试类型:{} 日期:{}", 
                        assignment.getBackupExaminer().getName(), 
                        assignment.getBackupExaminer().getDepartment(),
                        assignment.getBackupExaminer().getGroup(),
                        assignment.getExamType(),
                        assignment.getExamDate());
                    
                    String backupDept = normalizeDepartment(assignment.getBackupExaminer().getDepartment());
                    
                    // 🆕 新逻辑：备份考官也按考试类型选择推荐科室（与SC2考官二规则一致）
                    String recommendedDept = assignment.getStudent()
                        .getExaminer2RecommendedDepartmentByExamType(assignment.getExamType());
                    String normalizedRecommendedDept = recommendedDept != null 
                        ? normalizeDepartment(recommendedDept) : null;
                    
                    if (normalizedRecommendedDept != null) {
                        boolean isRecommended = Objects.equals(normalizedRecommendedDept, backupDept);
                        
                        if (isRecommended) {
                            logger.info("✅ [SC4匹配] 备份考官 {} (科室: {}) 匹配 {} 推荐科室 {}", 
                                    assignment.getBackupExaminer().getName(), 
                                    backupDept,
                                    assignment.getExamType(),
                                    normalizedRecommendedDept);
                            return true;
                        } else {
                            logger.debug("❌ [SC4不匹配] 备份考官 {} (科室: {}) 不匹配 {} 推荐科室 {}", 
                                    assignment.getBackupExaminer().getName(), 
                                    backupDept,
                                    assignment.getExamType(),
                                    normalizedRecommendedDept);
                        }
                    }
                    
                    return false;
                })
                .reward(getConstraintWeight("SC4", HardSoftScore.ofSoft(70)), assignment -> {
                    // SC4: 备份考官专业匹配 基础分数70 + SC1-SC4优先级分数
                    int baseScore = 70;
                    LocalDate examDate = LocalDate.parse(assignment.getExamDate());
                    int priorityScore = calculatePriorityScore(assignment.getBackupExaminer(), examDate);
                    int totalScore = baseScore + priorityScore;
                    
                    logger.info("SC4约束计分: 备份考官 {} 基础分数={}, 优先级分数={}, 总分数={}", 
                            assignment.getBackupExaminer().getName(), baseScore, priorityScore, totalScore);
                    
                    return totalScore;
                })
                .asConstraint("preferRecommendedBackupExaminer");
    }
    
    /**
     * SC8: 其次安排非推荐科室池的备份考官
     * 备份考官备选方案 + SC1-SC4优先级分数叠加
     * 🆕 新规则：备份考官也按day1/day2区分推荐科室
     */
    private Constraint preferNonRecommendedBackupExaminer(ConstraintFactory constraintFactory) {
        // 🔧 修复：添加约束启用检查，使用正确的约束ID SC8
        if (!isConstraintEnabled("SC8")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferNonRecommendedBackupExaminer");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getStudent() == null || assignment.getBackupExaminer() == null) {
                        return false;
                    }
                    
                    String backupDept = normalizeDepartment(assignment.getBackupExaminer().getDepartment());
                    
                    // 🆕 新逻辑：备份考官也按考试类型选择推荐科室（与SC2考官二规则一致）
                    String recommendedDept = assignment.getStudent()
                        .getExaminer2RecommendedDepartmentByExamType(assignment.getExamType());
                    String normalizedRecommendedDept = recommendedDept != null 
                        ? normalizeDepartment(recommendedDept) : null;
                    
                    if (normalizedRecommendedDept != null) {
                        boolean isRecommended = Objects.equals(normalizedRecommendedDept, backupDept);
                        
                        if (!isRecommended) {
                            logger.info("专业匹配 - 非推荐科室: 备份考官 {} (科室: {}) 不匹配 {} 推荐科室 {}", 
                                    assignment.getBackupExaminer().getName(), 
                                    backupDept,
                                    assignment.getExamType(),
                                    normalizedRecommendedDept);
                            return true;
                        }
                    }
                    
                    return false;
                })
                .reward(getConstraintWeight("SC8", HardSoftScore.ofSoft(30)), assignment -> {
                    // SC3_NON_RECOMMENDED: 备份考官备选方案 基础分数30 + SC1-SC4优先级分数
                    int baseScore = 30;
                    LocalDate examDate = LocalDate.parse(assignment.getExamDate());
                    int priorityScore = calculatePriorityScore(assignment.getBackupExaminer(), examDate);
                    int totalScore = baseScore + priorityScore;
                    
                    logger.info("SC8约束计分: 备份考官 {} 基础分数={}, 优先级分数={}, 总分数={}", 
                            assignment.getBackupExaminer().getName(), baseScore, priorityScore, totalScore);
                    
                    return totalScore;
                })
                .asConstraint("preferNonRecommendedBackupExaminer");
    }
    
    /**
     * SC1: 备份考官不能与考官1和考官2是同一人（软约束版本）
     * 注意：此方法已被硬约束版本 backupExaminerMustBeDifferentPerson 替代
     * 保留此方法以备将来可能的软约束需求
     */
    @SuppressWarnings("unused")
    private Constraint backupExaminerMustBeDifferentPersonSoft(ConstraintFactory constraintFactory) {
        // 🔧 修复：这个约束似乎没有在注册中使用，暂时保持SC1
        if (!isConstraintEnabled("SC1")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("backupExaminerMustBeDifferentPersonSoft");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    // 如果没有备份考官，不违反约束
                    if (assignment.getBackupExaminer() == null) {
                        return false;
                    }
                    
                    // 检查备份考官是否与考官1是同一人
                    if (assignment.getExaminer1() != null &&
                        Objects.equals(assignment.getBackupExaminer().getId(), assignment.getExaminer1().getId())) {
                        return true;
                    }
                    
                    // 检查备份考官是否与考官2是同一人
                    if (assignment.getExaminer2() != null &&
                        Objects.equals(assignment.getBackupExaminer().getId(), assignment.getExaminer2().getId())) {
                        return true;
                    }
                    
                    return false;
                })
                .penalize(getConstraintWeight("SC1", HardSoftScore.ofSoft(100))) // SC1权重：100（这是一个错误的约束，应该删除）
                .asConstraint("backupExaminerMustBeDifferentPersonSoft");
    }
    
    /**
     * 计算考官的SC1-SC4优先级分数
     * @param examiner 考官
     * @param examDate 考试日期
     * @return 优先级分数
     */
    private int calculatePriorityScore(Teacher examiner, LocalDate examDate) {
        if (examiner == null || examDate == null) {
            return 0;
        }
        
        // 🔧 修复：使用DutySchedule动态计算班组轮换状态
        String examDateStr = examDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        com.examiner.scheduler.domain.DutySchedule dutySchedule = 
            com.examiner.scheduler.domain.DutySchedule.forDate(examDateStr);
        
        String examinerGroup = examiner.getGroup();
        if (examinerGroup == null) {
            return 0;
        }
        
        // SC1: 晚班考官 (+100) - 最高优先级
        if (Objects.equals(examinerGroup, dutySchedule.getNightShift())) {
            logger.debug("🌙 [优先级] 考官 {} (班组: {}) 在 {} 为晚班，获得SC1加分: +100", 
                    examiner.getName(), examinerGroup, examDateStr);
            return 100;
        }
        
        // SC3: 休息第一天 (+80) - 次高优先级
        List<String> restGroups = dutySchedule.getRestGroups();
        if (restGroups != null && restGroups.size() >= 1 && Objects.equals(examinerGroup, restGroups.get(0))) {
            logger.debug("😴 [优先级] 考官 {} (班组: {}) 在 {} 为休息第一天，获得SC3加分: +80", 
                    examiner.getName(), examinerGroup, examDateStr);
            return 80;
        }
        
        // SC5: 休息第二天 (+60) - 中等优先级
        if (restGroups != null && restGroups.size() >= 2 && Objects.equals(examinerGroup, restGroups.get(1))) {
            logger.debug("😴 [优先级] 考官 {} (班组: {}) 在 {} 为休息第二天，获得SC5加分: +60", 
                    examiner.getName(), examinerGroup, examDateStr);
            return 60;
        }
        
        // SC7: 行政班 (+40)
        // 🔧 修复：使用isAdminTeacher方法判断，而不是硬编码"行政班"
        if (isAdminTeacher(examiner)) {
            logger.debug("🏢 [优先级] 考官 {} (班组: {}) 为行政班（无班组/group为空），获得SC7加分: +40", 
                    examiner.getName(), examinerGroup != null ? examinerGroup : "无");
            return 40;
        }
        
        // 白班考官不获得额外加分
        if (Objects.equals(examinerGroup, dutySchedule.getDayShift())) {
            logger.debug("☀️ [优先级] 考官 {} (班组: {}) 在 {} 为白班，无额外加分", 
                    examiner.getName(), examinerGroup, examDateStr);
        }
        
        return 0;
    }

    /**
     * SC5: 优先安排推荐科室池内的考官2
     * 考官2专业匹配 + SC1-SC4优先级分数叠加
     * 
     * 🆕 新规则（2025-10-07）：
     * - 第一天（day1）：考官2应该来自考官1推荐科室
     * - 第二天（day2）：考官2应该来自考官2推荐科室
     */
    private Constraint preferRecommendedExaminer2(ConstraintFactory constraintFactory) {
        // 🔧 修复：添加约束启用检查，使用正确的约束ID SC2
        if (!isConstraintEnabled("SC2")) {
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferRecommendedExaminer2");
        }
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    if (assignment.getStudent() == null || assignment.getExaminer2() == null) {
                        return false;
                    }
                    
                    // 🔍 添加详细调试日志
                    logger.info("🔍 [SC2调试] 检查考官2候选: {} 科室:{} 班组:{} 学员:{} 考试类型:{} 日期:{}", 
                        assignment.getExaminer2().getName(), 
                        assignment.getExaminer2().getDepartment(),
                        assignment.getExaminer2().getGroup(),
                        assignment.getStudent().getName(),
                        assignment.getExamType(),
                        assignment.getExamDate());
                    
                    String examiner2Dept = normalizeDepartment(assignment.getExaminer2().getDepartment());
                    
                    // 🆕 新逻辑：根据考试类型获取对应的推荐科室
                    String recommendedDept = assignment.getStudent().getExaminer2RecommendedDepartmentByExamType(assignment.getExamType());
                    String normalizedRecommendedDept = recommendedDept != null ? normalizeDepartment(recommendedDept) : null;
                    
                    logger.info("🔍 [SC2调试] 学员 {} 考试类型: {} 推荐科室: {}, 考官2科室: {}, 考官2班组: {}", 
                            assignment.getStudent().getName(),
                            assignment.getExamType(),
                            normalizedRecommendedDept,
                            examiner2Dept,
                            assignment.getExaminer2().getGroup());
                    
                    if (normalizedRecommendedDept != null) {
                        boolean isRecommended = Objects.equals(normalizedRecommendedDept, examiner2Dept);
                        
                        if (isRecommended) {
                            logger.info("✅ [SC2匹配] 考官2 {} (科室: {}, 班组: {}) 匹配 {} 推荐科室 {}", 
                                    assignment.getExaminer2().getName(), 
                                    assignment.getExaminer2().getDepartment(),
                                    assignment.getExaminer2().getGroup(),
                                    assignment.getExamType(),
                                    normalizedRecommendedDept);
                            return true;
                        } else {
                            logger.debug("❌ [SC2不匹配] 考官2 {} (科室: {}) 不匹配 {} 推荐科室 {}", 
                                    assignment.getExaminer2().getName(), 
                                    examiner2Dept,
                                    assignment.getExamType(),
                                    normalizedRecommendedDept);
                        }
                    }
                    
                    return false;
                })
                .reward(getConstraintWeight("SC2", HardSoftScore.ofSoft(90)), assignment -> {
                    // SC2: 考官2专业匹配 基础分数90 + SC1-SC4优先级分数
                    int baseScore = 90;
                    LocalDate examDate = LocalDate.parse(assignment.getExamDate());
                    int priorityScore = calculatePriorityScore(assignment.getExaminer2(), examDate);
                    int totalScore = baseScore + priorityScore;
                    
                    logger.info("SC2约束计分: 考官2 {} 基础分数={}, 优先级分数={}, 总分数={}", 
                            assignment.getExaminer2().getName(), baseScore, priorityScore, totalScore);
                    
                    return totalScore;
                })
                .asConstraint("preferRecommendedExaminer2");
    }
    
    /**
     * SC14: 同一学员Day1和Day2考官二应来自推荐科室池中的不同科室 🆕
     * 
     * 业务规则：
     * - 如果Day1考官二选择了推荐科室池中的某一个科室（考官1推荐科室）
     * - 那么Day2考官二应该选择推荐科室池中的另一个科室（考官2推荐科室）
     * - 这样可以让学员体验到不同科室考官的评审风格
     * 
     * 实现方式：
     * - 使用join将同一学员的Day1和Day2 assignment配对
     * - 检查两个考官二的科室是否都在推荐科室池中且不同
     * - 如果满足条件，给予奖励
     * 
     * 权重：110（高优先级，仅次于SC1晚班考官和SC3休息第一天）
     * 
     * 注意：备份考官不受此约束影响，可以来自推荐科室池中的任意科室
     */
    private Constraint preferDifferentRecommendedDeptsForDay1Day2(ConstraintFactory constraintFactory) {
        logger.info("💡 [SC14约束] 初始化: Day1/Day2考官二科室互斥约束 (权重:110)");
        
        // 检查约束是否启用
        if (!isConstraintEnabled("SC14")) {
            logger.warn("⚠️ [SC14约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferDifferentRecommendedDeptsForDay1Day2");
        }
        
        logger.info("✅ [SC14约束] 约束已启用，开始执行");
        
        // 使用join将同一学员的Day1和Day2 assignment配对
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> "day1".equals(assignment.getExamType()))  // 只处理Day1
                .join(ExamAssignment.class,
                      // 连接条件：同一个学员，但考试类型为day2
                      Joiners.equal(assignment -> assignment.getStudent().getId(), 
                                   assignment -> assignment.getStudent().getId()),
                      Joiners.filtering((day1, day2) -> "day2".equals(day2.getExamType())))
                .filter((day1Assignment, day2Assignment) -> {
                    // 检查两个assignment的考官二是否都存在
                    if (day1Assignment.getExaminer2() == null || day2Assignment.getExaminer2() == null) {
                        return false;
                    }
                    
                    Student student = day1Assignment.getStudent();
                    String day1Examiner2Dept = normalizeDepartment(day1Assignment.getExaminer2().getDepartment());
                    String day2Examiner2Dept = normalizeDepartment(day2Assignment.getExaminer2().getDepartment());
                    
                    // 获取推荐科室
                    String examiner1RecommendedDept = student.getRecommendedExaminer1Dept() != null 
                        ? normalizeDepartment(student.getRecommendedExaminer1Dept()) : null;
                    String examiner2RecommendedDept = student.getRecommendedExaminer2Dept() != null 
                        ? normalizeDepartment(student.getRecommendedExaminer2Dept()) : null;
                    
                    // 检查两个推荐科室是否都存在且不同
                    if (examiner1RecommendedDept == null || examiner2RecommendedDept == null) {
                        logger.debug("❌ [SC14约束] 学员 {} 推荐科室不完整：考官1推荐={}, 考官2推荐={}", 
                                    student.getName(), examiner1RecommendedDept, examiner2RecommendedDept);
                        return false;
                    }
                    
                    // 检查Day1考官二是否来自推荐科室池
                    boolean day1InRecommendedPool = Objects.equals(day1Examiner2Dept, examiner1RecommendedDept) || 
                                                   Objects.equals(day1Examiner2Dept, examiner2RecommendedDept);
                    
                    // 检查Day2考官二是否来自推荐科室池
                    boolean day2InRecommendedPool = Objects.equals(day2Examiner2Dept, examiner1RecommendedDept) || 
                                                   Objects.equals(day2Examiner2Dept, examiner2RecommendedDept);
                    
                    // 检查两个考官二的科室是否不同
                    boolean differentDepts = !Objects.equals(day1Examiner2Dept, day2Examiner2Dept);
                    
                    // 只有当两个考官二都来自推荐科室池，且科室不同时才奖励
                    boolean matched = day1InRecommendedPool && day2InRecommendedPool && differentDepts;
                    
                    if (matched) {
                        logger.info("✅ [SC14匹配] 学员 {} Day1考官二:{} vs Day2考官二:{} (来自不同推荐科室) | 推荐池:[{}, {}]", 
                                   student.getName(),
                                   day1Assignment.getExaminer2().getName() + "(" + day1Examiner2Dept + ")",
                                   day2Assignment.getExaminer2().getName() + "(" + day2Examiner2Dept + ")",
                                   examiner1RecommendedDept,
                                   examiner2RecommendedDept);
                        recordConstraintExecution("SC14", true, 110);
                    } else {
                        logger.debug("❌ [SC14不匹配] 学员 {} Day1考官二:{} Day2考官二:{} | " +
                                   "Day1在池中:{} Day2在池中:{} 科室不同:{}", 
                                   student.getName(),
                                   day1Examiner2Dept, day2Examiner2Dept,
                                   day1InRecommendedPool, day2InRecommendedPool, differentDepts);
                        recordConstraintExecution("SC14", false, 0);
                    }
                    
                    return matched;
                })
                .reward(getConstraintWeight("SC14", HardSoftScore.ofSoft(110)))
                .asConstraint("preferDifferentRecommendedDeptsForDay1Day2");
    }

    /**
     * SC15: 鼓励同一学员两天考试使用不同考官1 🆕
     *
     * 业务规则：
     * - 如果资源充足，优先为同一学员的两天考试分配不同的考官1
     * - 这样可以减少单个考官的连续工作压力
     * - 同时让学员体验不同考官的评审风格
     *
     * 实现方式：
     * - 使用join将同一学员的Day1和Day2 assignment配对
     * - 检查两个考官1是否为同一人
     * - 如果是同一人，给予惩罚（鼓励使用不同考官）
     *
     * 权重：60（中等优先级，低于工作量均衡但高于日期分配）
     *
     * 注意：
     * - 这是软约束，不会强制要求使用不同考官1
     * - 如果资源不足或会导致硬约束违反，OptaPlanner会保持使用同一个考官1
     * - 考官1仍然必须满足HC2约束（与学员同科室）
     */
    private Constraint encourageDifferentExaminer1ForTwoDays(ConstraintFactory constraintFactory) {
        logger.info("💡 [SC15约束] 初始化: 鼓励考官1多样性约束 (权重:60)");

        // 检查约束是否启用
        if (!isConstraintEnabled("SC15")) {
            logger.warn("⚠️ [SC15约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("encourageDifferentExaminer1ForTwoDays");
        }

        logger.info("✅ [SC15约束] 约束已启用，开始执行");

        // 使用join将同一学员的Day1和Day2 assignment配对
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> "day1".equals(assignment.getExamType()))  // 只处理Day1
                .join(ExamAssignment.class,
                      // 连接条件：同一个学员，但考试类型为day2
                      Joiners.equal(assignment -> assignment.getStudent().getId(),
                                   assignment -> assignment.getStudent().getId()),
                      Joiners.filtering((day1, day2) -> "day2".equals(day2.getExamType())))
                .filter((day1Assignment, day2Assignment) -> {
                    // 检查两个assignment的考官1是否都存在
                    if (day1Assignment.getExaminer1() == null || day2Assignment.getExaminer1() == null) {
                        logger.debug("⚠️ [SC15约束] 学员 {} 考官1未完全分配，跳过检查",
                                    day1Assignment.getStudent().getName());
                        return false;
                    }

                    Student student = day1Assignment.getStudent();
                    Teacher day1Examiner1 = day1Assignment.getExaminer1();
                    Teacher day2Examiner1 = day2Assignment.getExaminer1();

                    // 检查两天的考官1是否为同一人
                    boolean sameExaminer = Objects.equals(day1Examiner1.getId(), day2Examiner1.getId());

                    if (sameExaminer) {
                        logger.info("⚠️ [SC15检测] 学员 {} 两天考试使用同一考官1: {} (科室:{})",
                                   student.getName(),
                                   day1Examiner1.getName(),
                                   day1Examiner1.getDepartment());
                        recordConstraintExecution("SC15", true, 60);
                    } else {
                        logger.info("✅ [SC15满足] 学员 {} 两天考试使用不同考官1: Day1={} vs Day2={}",
                                   student.getName(),
                                   day1Examiner1.getName(),
                                   day2Examiner1.getName());
                        recordConstraintExecution("SC15", false, 0);
                    }

                    return sameExaminer;
                })
                .penalize(getConstraintWeight("SC15", HardSoftScore.ofSoft(60)))
                .asConstraint("encourageDifferentExaminer1ForTwoDays");
    }

    /**
     * SC6: 其次安排非推荐科室池的考官2
     * 考官2备选方案 + SC1-SC4优先级分数叠加
     *
     * 🆕 新规则（2025-10-07）：
     * - 第一天（day1）：如果不是考官1推荐科室，则作为备选方案
     * - 第二天（day2）：如果不是考官2推荐科室，则作为备选方案
     */
    private Constraint preferNonRecommendedExaminer2(ConstraintFactory constraintFactory) {
        logger.info("💡 [SC6约束] 初始化: 考官2备选方案约束 (权重:50+优先级分)");
        
        // 🔧 修复：使用正确的约束ID SC6
        if (!isConstraintEnabled("SC6")) {
            logger.warn("⚠️ [SC6约束] 约束已禁用，跳过执行");
            return constraintFactory.forEach(ExamAssignment.class)
                    .filter(assignment -> false)
                    .penalize(HardSoftScore.ZERO)
                    .asConstraint("preferNonRecommendedExaminer2");
        }
        
        logger.info("✅ [SC6约束] 约束已启用，开始执行");
        
        return constraintFactory
                .forEach(ExamAssignment.class)
                .filter(assignment -> {
                    logger.debug("🔍 [SC6约束] 检查学员: {} 考官2: {} 考试类型: {}", 
                            assignment.getStudent() != null ? assignment.getStudent().getName() : "未知",
                            assignment.getExaminer2() != null ? assignment.getExaminer2().getName() : "未分配",
                            assignment.getExamType());
                    
                    if (assignment.getStudent() == null || assignment.getExaminer2() == null) {
                        logger.debug("❌ [SC6约束] 学员或考官2为空，跳过");
                        return false;
                    }
                    
                    String examiner2Dept = normalizeDepartment(assignment.getExaminer2().getDepartment());
                    
                    // 🆕 新逻辑：根据考试类型获取对应的推荐科室
                    String recommendedDept = assignment.getStudent().getExaminer2RecommendedDepartmentByExamType(assignment.getExamType());
                    String normalizedRecommendedDept = recommendedDept != null ? normalizeDepartment(recommendedDept) : null;
                    
                    logger.debug("🔍 [SC6约束] 考试类型: {} 推荐科室: {} 考官2科室: {}", 
                            assignment.getExamType(), normalizedRecommendedDept, examiner2Dept);
                    
                    if (normalizedRecommendedDept != null) {
                        boolean isRecommended = Objects.equals(normalizedRecommendedDept, examiner2Dept);
                        
                        if (!isRecommended) {
                            logger.info("🎯 [SC6约束] 匹配! 考官2 {} (科室:{}) 不匹配 {} 推荐科室 {}，提供备选方案", 
                                    assignment.getExaminer2().getName(), 
                                    assignment.getExaminer2().getDepartment(),
                                    assignment.getExamType(),
                                    normalizedRecommendedDept);
                            
                            // 记录匹配的统计信息
                            recordConstraintExecution("SC6", true, 50); // 基础分数，实际分数在reward中计算
                            return true;
                        } else {
                            logger.debug("❌ [SC6约束] 考官2匹配推荐科室，不符合备选方案条件");
                            recordConstraintExecution("SC6", false, 0);
                        }
                    } else {
                        logger.debug("⚠️ [SC6约束] 学员无推荐科室信息");
                        recordConstraintExecution("SC6", false, 0);
                    }
                    
                    return false;
                })
                .reward(getConstraintWeight("SC6", HardSoftScore.ofSoft(50)), assignment -> {
                    // SC6: 考官2备选方案 基础分数50 + SC1-SC5优先级分数
                    int baseScore = 50;
                    LocalDate examDate = LocalDate.parse(assignment.getExamDate());
                    int priorityScore = calculatePriorityScore(assignment.getExaminer2(), examDate);
                    int totalScore = baseScore + priorityScore;
                    
                    logger.info("📊 [SC6约束] 计分详情: 考官2 {} | 基础分数={} | 优先级分数={} | 总分数={} | 日期={}", 
                            assignment.getExaminer2().getName(), baseScore, priorityScore, totalScore, assignment.getExamDate());
                    
                    return totalScore;
                })
                .asConstraint("preferNonRecommendedExaminer2");
    }
    
    /**
     * 记录约束执行统计信息
     */
    private static void recordConstraintExecution(String constraintId, boolean matched, int score) {
        constraintExecutionCount.get(constraintId).incrementAndGet();
        if (matched) {
            constraintMatchCount.get(constraintId).incrementAndGet();
            constraintTotalScore.get(constraintId).addAndGet(score);
        }
    }
    
    /**
     * 获取约束统计汇总
     */
    public static void logConstraintStatistics() {
        logger.info("📊 [约束统计] =================== 约束执行统计汇总 ===================");
        
        String[] hardConstraints = {"HC1", "HC2", "HC3", "HC4", "HC5", "HC6", "HC7", "HC8"};
        String[] softConstraints = {"SC1", "SC2", "SC3", "SC4", "SC5", "SC6", "SC7", "SC8", "SC9", "SC10", "SC11", "SC14"};
        
        int totalExecutions = 0;
        int totalScore = 0;
        
        // 硬约束统计
        logger.info("🚫 [硬约束统计] --------------------------------");
        int hardViolations = 0;
        for (String constraint : hardConstraints) {
            int executions = constraintExecutionCount.get(constraint).get();
            int matches = constraintMatchCount.get(constraint).get();
            int score = constraintTotalScore.get(constraint).get();
            
            totalExecutions += executions;
            totalScore += score;
            hardViolations += matches;
            
            double matchRate = executions > 0 ? (double) matches / executions * 100 : 0;
            double avgScore = matches > 0 ? (double) score / matches : 0;
            
            logger.info("📊 [{}] 执行:{} 次 | 违反:{} 次 | 违反率:{:.1f}% | 总分:{} | 平均分:{:.1f}", 
                    constraint, executions, matches, matchRate, score, avgScore);
        }
        logger.info("🚫 [硬约束汇总] 总违反:{} 次", hardViolations);
        
        // 软约束统计
        logger.info("🎯 [软约束统计] --------------------------------");
        int softMatches = 0;
        for (String constraint : softConstraints) {
            int executions = constraintExecutionCount.get(constraint).get();
            int matches = constraintMatchCount.get(constraint).get();
            int score = constraintTotalScore.get(constraint).get();
            
            totalExecutions += executions;
            totalScore += score;
            softMatches += matches;
            
            double matchRate = executions > 0 ? (double) matches / executions * 100 : 0;
            double avgScore = matches > 0 ? (double) score / matches : 0;
            
            logger.info("📊 [{}] 执行:{} 次 | 匹配:{} 次 | 匹配率:{:.1f}% | 总分:{} | 平均分:{:.1f}", 
                    constraint, executions, matches, matchRate, score, avgScore);
        }
        logger.info("🎯 [软约束汇总] 总匹配:{} 次", softMatches);
        
        logger.info("📊 [总计] 约束总执行:{} 次 | 硬约束违反:{} 次 | 软约束匹配:{} 次 | 总分数:{}", 
                totalExecutions, hardViolations, softMatches, totalScore);
        logger.info("📊 [约束统计] ============================================");
        
        // 🔗 同步约束违反信息到前端
        syncConstraintViolationsToFrontend(hardViolations, softMatches, totalScore);
    }
    
    /**
     * 重置约束统计
     */
    public static void resetConstraintStatistics() {
        logger.info("🔄 [约束统计] 重置所有约束统计数据");
        String[] hardConstraints = {"HC1", "HC2", "HC3", "HC4", "HC5", "HC6", "HC7", "HC8"};
        String[] softConstraints = {"SC1", "SC2", "SC3", "SC4", "SC5", "SC6", "SC7", "SC8", "SC9", "SC10", "SC11"};
        
        // 重置硬约束统计
        for (String constraint : hardConstraints) {
            constraintExecutionCount.get(constraint).set(0);
            constraintMatchCount.get(constraint).set(0);
            constraintTotalScore.get(constraint).set(0);
        }
        
        // 重置软约束统计
        for (String constraint : softConstraints) {
            constraintExecutionCount.get(constraint).set(0);
            constraintMatchCount.get(constraint).set(0);
            constraintTotalScore.get(constraint).set(0);
        }
    }
    
    /**
     * 🔗 同步约束违反信息到前端
     */
    private static void syncConstraintViolationsToFrontend(int hardViolations, int softMatches, int totalScore) {
        try {
            // 构建约束违反统计
            Map<String, Integer> violationCounts = new HashMap<>();
            String[] hardConstraints = {"HC1", "HC2", "HC3", "HC4", "HC6", "HC7", "HC8"};
            
            for (String constraint : hardConstraints) {
                int violations = constraintMatchCount.get(constraint).get();
                if (violations > 0) {
                    violationCounts.put(constraint, violations);
                }
            }
            
            // 创建模拟的HardSoftScore（实际应用中从算法结果获取）
            HardSoftScore score = HardSoftScore.of(-Math.abs(totalScore), Math.abs(softMatches));
            
            // 构建详细违反信息（这里简化为汇总，实际可以收集具体违反案例）
            List<ConstraintViolationSyncResource.ConstraintViolationDetail> details = new ArrayList<>();
            
            // 同步到前端API
            ConstraintViolationSyncResource.updateViolationSummary(score, violationCounts, details);
            
            logger.info("🔗 [约束同步] 已同步约束违反信息到前端API");
            
        } catch (Exception e) {
            logger.warn("🔗 [约束同步] 同步约束违反信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 约束执行开始标记
     */
    public void markConstraintExecutionStart() {
        logger.info("⏱️ [算法执行] OptaPlanner约束求解开始执行");
        logger.info("🔧 [算法配置] 当前启用的约束配置:");
        
        // 记录当前启用的约束
        String[] hardConstraints = {"HC1", "HC2", "HC3", "HC4", "HC5", "HC6", "HC7", "HC8"};
        String[] softConstraints = {"SC1", "SC2", "SC3", "SC4", "SC5", "SC6", "SC7", "SC8", "SC9", "SC10", "SC11"};
        
        logger.info("📋 [硬约束配置]:");
        for (String constraint : hardConstraints) {
            boolean enabled = isConstraintEnabled(constraint);
            String status = enabled ? "✅ 启用" : "❌ 禁用";
            logger.info("  {} {}", constraint, status);
        }
        
        logger.info("📋 [软约束配置]:");
        for (String constraint : softConstraints) {
            boolean enabled = isConstraintEnabled(constraint);
            String status = enabled ? "✅ 启用" : "❌ 禁用";
            logger.info("  {} {}", constraint, status);
        }
        
        resetConstraintStatistics();
    }
    
    /**
     * 约束执行结束标记
     */
    public void markConstraintExecutionEnd() {
        logger.info("✅ [算法执行] OptaPlanner约束求解执行完毕");
        logConstraintStatistics();
    }
}