<template>
  <div class="app-container responsive-container">
    <!-- 移动端遮罩层 -->
    <div 
      v-if="isMobile && mobileMenuOpen" 
      class="mobile-overlay"
      @click="closeMobileMenu"
    ></div>
    
    <!-- 侧边栏 -->
    <aside 
      class="sidebar" 
      :class="{ 
        'sidebar-collapsed': sidebarCollapsed,
        'mobile-open': isMobile && mobileMenuOpen
      }"
    >
      <div class="sidebar-header">
        <div class="logo-container">
          <div class="logo-icon">
            <img src="/icon.png" alt="系统图标" class="logo-img" />
          </div>
          <div class="logo-text" v-show="!sidebarCollapsed">
            <h1 class="system-title">考试自动排班助手</h1>
            <p class="system-subtitle">Examiner Assignment Assistant</p>
          </div>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-items">
          <router-link to="/" class="nav-item">
            <Home class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">首页</span>
          </router-link>
          <router-link to="/teachers" class="nav-item">
            <Users class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">考官管理</span>
          </router-link>
          <router-link to="/schedules" class="nav-item nav-item-active">
            <Calendar class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">排班管理</span>
          </router-link>
          <!-- 隐藏数据统计页面 -->
          <!-- <router-link to="/statistics" class="nav-item">
            <BarChart class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">数据统计</span>
          </router-link> -->
        </div>
      </nav>
      
      <!-- 侧边栏收缩按钮 -->
      <div class="sidebar-toggle" @click="toggleSidebar">
        <ChevronLeft class="toggle-icon" :class="{ rotated: sidebarCollapsed }" />
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div 
      class="main-content padding-responsive"
      :class="{
        'sidebar-open': isMobile && mobileMenuOpen,
        'mobile-layout': isMobile,
        'tablet-layout': isTablet,
        'desktop-layout': isDesktop
      }"
    >


      <!-- 右侧内容区域 -->
      <div class="right-content full-width">
        <!-- 实时进度监控（后端 WebSocket） -->
        <div v-if="realtimeProgressVisible" class="realtime-monitor-wrapper" style="margin-bottom: 12px;">
          <RealTimeStatusMonitor :sessionId="wsSessionId as any" />
        </div>
        <!-- 页面标题栏 -->
        <div class="page-header">
          <h1 class="page-title">排班管理</h1>
          <div class="flex items-center space-x-4 mb-2">
            <div class="text-sm text-gray-500">
              状态: <span :class="[
                'px-2 py-1 rounded text-xs font-medium',
                isTableUpdating ? 'bg-blue-100 text-blue-800 status-indicator updating' :
                isScheduling ? 'bg-yellow-100 text-yellow-800 status-indicator solving' :
                scheduleResults.length > 0 ? 'bg-green-100 text-green-800 status-indicator completed' :
                'bg-gray-100 text-gray-800 status-indicator ready'
              ]">{{ getTableStatusText() }}</span>
            </div>
            <div v-if="lastTableUpdate" class="text-sm text-gray-400">
              最后更新: {{ lastTableUpdate }}
            </div>
          </div>
          <div class="header-actions">
            <!-- 实时计算流程按钮 -->
            <button 
              v-if="isScheduling || realtimeLogs.length > 0"
              class="action-btn action-btn-info" 
              @click="toggleRealtimeProgressWindow"
              :title="realtimeProgressVisible ? '隐藏实时计算流程' : '查看实时计算流程详情'"
            >
              <Activity class="btn-icon" :class="{ 'animate-pulse': isScheduling && !realtimeProgressVisible }" />
              <span>{{ realtimeProgressVisible ? '隐藏流程' : '实时计算流程' }}</span>
              <span v-if="isScheduling" class="ml-1 text-xs bg-white/20 px-1 rounded">{{ schedulingProgress }}%</span>
            </button>
            
            <button 
              class="action-btn action-btn-secondary" 
              @click="recalculateSchedule"
              :disabled="isScheduling"
              :class="{ loading: isScheduling }"
              title="使用最新约束配置重新计算排班（已优化晚班考官优先级）"
            >
              <RefreshCw class="btn-icon" :class="{ 'spinning': isScheduling }" />
              <span v-if="!isScheduling">重新排班</span>
              <span v-else>重新排班中..</span>
            </button>
            <button class="action-btn action-btn-secondary" @click="showConstraintsPanel = false" v-if="showConstraintsPanel">
              <span>退出</span>
            </button>
            <button class="action-btn action-btn-secondary" @click="saveChanges" :disabled="!isModified">
              <Upload class="btn-icon" />
              <span>保存</span>
            </button>
            <button class="action-btn action-btn-secondary" @click="exportToExcel">
              <FileText class="btn-icon" />
              <span>导出</span>
            </button>
            <button class="action-btn action-btn-warning" @click="forceRefreshDisplay" v-if="scheduleResults.length > 0 && needsRefresh" title="如果排班结果不显示，点击此按钮强制刷新">
              <RefreshCw class="btn-icon" />
              <span>刷新显示</span>
            </button>
            <button class="action-btn action-btn-primary" @click="showCreateModal = true" v-if="!showConstraintsPanel">
              <Plus class="btn-icon" />
              <span>新建排班</span>
            </button>
          </div>
        </div>

        <!-- 约束违反提示 - 智能显示最重要的违反 -->
        <!-- 约束违反提醒弹窗 - 已替换为统一弹窗 -->
        <!-- <ConstraintViolationAlert 
          v-if="constraintViolations.length > 0 && shouldShowViolationAlert"
          :violations="constraintViolations"
          @fix-violation="handleFixViolation"
          @fix-all="handleFixAllViolations"
          @dismiss="dismissViolationAlert"
        /> -->
        
        <!-- 🎬 实时更新提示横幅 -->
        <div v-if="isTableUpdating" class="realtime-update-banner">
          <div class="update-indicator">
            <div class="loading-dots"></div>
            <span>🔄 正在实时更新排班结果...</span>
            <span class="update-count">当前显示: {{ scheduleResults.length }} 条记录</span>
          </div>
        </div>
        
        <!-- 排班表格 -->
        <div class="table-container" :class="{ 'updating': isTableUpdating }">
          <table class="schedule-table">
            <thead>
              <tr>
                <th>所在科室</th>
                <th>学员</th>
                <th>考试日期</th>
                <th>考试类型</th>
                <th>考官一</th>
                <th>考官二</th>
                <th>备份考官</th>
                <th>考试日期</th>
                <th>考试类型</th>
                <th>考官一</th>
                <th>考官二</th>
                <th>备份考官</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
               <!-- 显示排班结果数据 -->
               <template v-for="(result, index) in scheduleResults" :key="result?.id || Math.random()">
                 <!-- 第一行：第一天考试（现场） -->
                 <tr :class="{ 'animating-row': getCellAnimationState(index, 'any', 1) }">
                   <td rowspan="2" class="department-cell">{{ result?.department || '-' }}</td>
                   <td rowspan="2" class="student-cell">{{ result?.student || '-' }}</td>
                   <td rowspan="2" 
                       :class="getCellAnimationState(index, 'date', 1) ? `table-cell-${getCellAnimationState(index, 'date', 1)?.animationType}` : ''">
                     {{ result?.date1 || '-' }}
                   </td>
                   <td>现场</td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner1', 1) ? `table-cell-${getCellAnimationState(index, 'examiner1', 1)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner1_1')">
                     {{ result?.examiner1_1 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner2', 1) ? `table-cell-${getCellAnimationState(index, 'examiner2', 1)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner1_2')">
                     {{ result?.examiner1_2 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'backup', 1) ? `table-cell-${getCellAnimationState(index, 'backup', 1)?.animationType}` : ''"
                       @click="editExaminer(result, 'backup1')">
                     {{ result?.backup1 || '-' }}
                   </td>
                   <td rowspan="2" 
                       :class="getCellAnimationState(index, 'date', 2) ? `table-cell-${getCellAnimationState(index, 'date', 2)?.animationType}` : ''">
                     {{ result?.date2 || '-' }}
                   </td>
                   <td>模拟机</td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner1', 2) ? `table-cell-${getCellAnimationState(index, 'examiner1', 2)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner2_1')">
                     {{ result?.examiner2_1 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner2', 2) ? `table-cell-${getCellAnimationState(index, 'examiner2', 2)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner2_2')">
                     {{ result?.examiner2_2 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'backup', 2) ? `table-cell-${getCellAnimationState(index, 'backup', 2)?.animationType}` : ''"
                       @click="editExaminer(result, 'backup2')">
                     {{ result?.backup2 || '-' }}
                   </td>
                   <td rowspan="2" class="action-cell">
                     <div class="action-buttons">
                       <button class="action-btn delete-btn" @click="deleteScheduleRecord(result)" title="删除">
                         <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                           <polyline points="3,6 5,6 21,6"></polyline>
                           <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                           <line x1="10" y1="11" x2="10" y2="17"></line>
                           <line x1="14" y1="11" x2="14" y2="17"></line>
                         </svg>
                       </button>
                     </div>
                   </td>
                 </tr>
                 <!-- 第二行：第一天考试（模拟机1）和第二天考试（口试） -->
                 <tr :class="{ 'animating-row': getCellAnimationState(index, 'any', 2) }">
                   <td>模拟机</td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner1', 1) ? `table-cell-${getCellAnimationState(index, 'examiner1', 1)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner1_1')">
                     {{ result?.examiner1_1 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner2', 1) ? `table-cell-${getCellAnimationState(index, 'examiner2', 1)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner1_2')">
                     {{ result?.examiner1_2 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'backup', 1) ? `table-cell-${getCellAnimationState(index, 'backup', 1)?.animationType}` : ''"
                       @click="editExaminer(result, 'backup1')">
                     {{ result?.backup1 || '-' }}
                   </td>
                   <td>口试</td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner1', 2) ? `table-cell-${getCellAnimationState(index, 'examiner1', 2)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner2_1')">
                     {{ result?.examiner2_1 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'examiner2', 2) ? `table-cell-${getCellAnimationState(index, 'examiner2', 2)?.animationType}` : ''"
                       @click="editExaminer(result, 'examiner2_2')">
                     {{ result?.examiner2_2 || '-' }}
                   </td>
                   <td class="editable-cell" 
                       :class="getCellAnimationState(index, 'backup', 2) ? `table-cell-${getCellAnimationState(index, 'backup', 2)?.animationType}` : ''"
                       @click="editExaminer(result, 'backup2')">
                     {{ result?.backup2 || '-' }}
                   </td>
                 </tr>
               </template>
              <!-- 空表格，等待数据填充 -->
              <tr v-for="i in 20" :key="i">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
        

            </div>
    </div>
    
    <!-- 🎯 实时计算流程窗口 - 可拖拽悬浮显示 -->
    <div 
      v-if="realtimeProgressVisible" 
      ref="realtimeWindowRef"
      class="realtime-progress-window draggable-realtime-window" 
      :class="{ 'minimized': realtimeProgressMinimized, 'dragging': realtimeWindowDragging }"
      :style="{ 
        transform: `translate(${realtimeWindowPosition.x}px, ${realtimeWindowPosition.y}px)`,
        position: 'fixed',
        top: '0',
        left: '0'
      }"
    >
      <div 
        class="window-header draggable-header" 
        @mousedown="startRealtimeWindowDrag"
        :style="{ cursor: realtimeWindowDragging ? 'grabbing' : 'grab' }"
      >
        <div class="window-title">
          <span class="icon">🔄</span>
          <span>实时计算流程</span>
          <span class="drag-hint">📱 可拖拽</span>
        </div>
        <div class="window-controls">
          <button @click="resetRealtimeWindowPosition" class="reset-btn" title="重置位置">
            🏠
          </button>
          <button @click="realtimeProgressMinimized = !realtimeProgressMinimized" class="minimize-btn">
            {{ realtimeProgressMinimized ? '📈' : '📉' }}
          </button>
          <button @click="realtimeProgressVisible = false" class="close-btn">✕</button>
        </div>
      </div>
      
      <div v-if="!realtimeProgressMinimized" class="window-content">
                   <!-- OptaPlanner求解进度 -->
           <div class="realtime-section">
             <h5>🔍 OptaPlanner求解器</h5>
             <div class="progress-bar-mini">
               <div class="progress-fill-mini" :style="{ width: schedulingProgress + '%' }"></div>
             </div>
             <div class="progress-info">{{ schedulingProgress }}% - {{ currentProgressMessage }}</div>
             <div v-if="latestSoftScore !== null" class="progress-softscore">
               <span>当前软约束: {{ formatSoftScore(latestSoftScore ?? undefined) }}</span>
               <span
                 v-if="bestSoftScore !== null && bestSoftScore !== latestSoftScore"
                 class="progress-softscore-best"
               >
                 峰值: {{ formatSoftScore(bestSoftScore ?? undefined) }}
               </span>
             </div>
           </div>
           
           <!-- 约束求解统计 -->
           <div class="realtime-section">
             <h5>⚖️ 约束求解状态</h5>
             <div class="stats-grid">
               <div class="stat-item">
                 <span class="stat-label">变量总数</span>
                 <span class="stat-value">{{ totalStudents * 6 }}</span>
               </div>
               <div class="stat-item">
                 <span class="stat-label">硬约束</span>
                 <span class="stat-value constraint">8项</span>
               </div>
               <div class="stat-item">
                 <span class="stat-label">软约束</span>
                 <span class="stat-value constraint">11项</span>
               </div>
               <div class="stat-item">
                 <span class="stat-label">求解进度</span>
                 <span class="stat-value progress">{{ Math.round((currentAssignmentCount / (totalStudents * 2)) * 100) }}%</span>
               </div>
             </div>
           </div>
        
        <!-- 实时日志 -->
        <div class="realtime-section">
          <h5>📝 实时日志</h5>
          <div class="log-container" ref="logContainer">
            <div v-for="(log, index) in realtimeLogs" :key="index" class="log-entry" :class="log.type">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
        
                 <!-- 表格更新状态 -->
         <div class="realtime-section">
           <h5>📋 表格状态</h5>
           <div class="table-status">
             <div class="status-indicator" :class="{ 
               'active': isTableUpdating || isTableAnimating,
               'animating': isTableAnimating,
               'updating': isTableUpdating,
               'completed': !isScheduling && scheduleResults.length > 0
             }">
               <span class="indicator-dot"></span>
               <span>
                 {{ getTableStatusText() }}
               </span>
             </div>
             <div class="last-update">
               最后更新: {{ lastTableUpdate || '未更新' }}
             </div>
                         <!-- OptaPlanner风格的实时状态指示器 -->
            <div class="flex items-center gap-4">
              <div v-if="isTableUpdating" class="realtime-update-indicator">
                <div class="pulse-dot"></div>
                <span>⚡ OptaPlanner增量更新中</span>
              </div>
              <div v-else-if="isScheduling" class="realtime-update-indicator">
                <div class="pulse-dot"></div>
                <span>🚀 OptaPlanner求解进行中</span>
              </div>
              <div v-else-if="!isScheduling && scheduleResults.length > 0" class="flex items-center gap-2 text-blue-600 font-medium text-sm">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>📋 排班完成 ({{ scheduleResults.length }} 条记录)</span>
              </div>
              <div v-else class="text-gray-500 text-sm">
                📋 等待排班数据...
              </div>
            </div>
           </div>
         </div>
      </div>
    </div>

    <!-- 编辑考官弹窗 -->
    <!-- 智能人工修改模态框 -->
    <SmartManualEditModal
      :show="showEditModal"
      :editing-record="editingRecord"
      :editing-field="editingField"
      :available-teachers="availableTeachers as any"
      :current-value="currentEditValue"
      :all-schedule-records="scheduleResults"
      @close="closeEditModal"
      @confirm="handleSmartEditConfirm"
    />

    <!-- 文件预览弹窗 -->
    <div v-if="showPreviewModal" class="modal-overlay preview-modal-overlay" @click="closePreviewModal">
      <div class="preview-modal-content" @click.stop>
        <div class="preview-header">
          <h2 class="preview-title">文件预览</h2>
          <button class="close-btn" @click="closePreviewModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"/>
              <path d="M6 6l12 12"/>
            </svg>
          </button>
        </div>
        <div class="preview-body">
          <div class="preview-info">
            <p class="file-info-text">文件名：{{ uploadedFile?.name }}</p>
            <p class="data-info-text">显示前10行数据</p>
          </div>
          <div class="preview-table-container">
            <table class="preview-table">
              <thead>
                <tr>
                  <th v-for="header in previewHeaders" :key="header">{{ header }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(row, index) in previewData" :key="index">
                  <td v-for="header in previewHeaders" :key="header">{{ row[header] }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建排班弹窗 -->
    <div v-if="showCreateModal" class="modal-overlay" @click="closeModal">
      <div 
        ref="modalRef"
        class="modal-content draggable-modal step-modal" 
        @click.stop
        :style="{
          transform: `translate(${modalPosition.x}px, ${modalPosition.y}px)`,
          cursor: isDragging ? 'grabbing' : 'default'
        }"
      >
        <!-- 可拖拽的标题栏 -->
        <div 
          class="modal-header draggable-header" 
          @mousedown="startDrag"
          :style="{ cursor: isDragging ? 'grabbing' : 'grab' }"
        >
          <h2 class="modal-title">新建排班</h2>
          <button class="close-btn" @click="closeModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"/>
              <path d="M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
          <div class="step-item" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
            <div class="step-number">1</div>
            <div class="step-label">学员导入</div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item" :class="{ active: currentStep === 2, completed: currentStep > 2 }">
            <div class="step-number">2</div>
            <div class="step-label">日期选择</div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item" :class="{ active: currentStep === 3, completed: currentStep > 3 }">
            <div class="step-number">3</div>
            <div class="step-label">硬约束</div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item" :class="{ active: currentStep === 4, completed: currentStep > 4 }">
            <div class="step-number">4</div>
            <div class="step-label">软约束</div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item" :class="{ active: currentStep === 5, completed: currentStep > 5 }">
            <div class="step-number">5</div>
            <div class="step-label">高级配置</div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item" :class="{ active: currentStep === 6 }">
            <div class="step-number">6</div>
            <div class="step-label">确认执行</div>
          </div>
        </div>
        
        <!-- 步骤1: 学员导入 -->
        <div v-if="currentStep === 1" class="step-content">
          <div class="step-title">
            <h3>请导入学员名单</h3>
            <p class="step-description">支持Excel、CSV等格式的学员名单文件</p>
          </div>
          
          <div class="file-upload-area">
            <input 
              ref="fileInput" 
              type="file" 
              accept=".xlsx,.xls,.csv" 
              @change="handleFileUpload" 
              style="display: none;"
            />
            
            <div v-if="!uploadedFile" class="upload-placeholder" @click="triggerFileUpload">
              <div class="upload-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
              </div>
              <p class="upload-text">请将文件拖入框内</p>
              <p class="upload-subtext">支持Excel、CSV等格式的导入</p>
            </div>
            
            <div v-else class="file-info">
              <div class="file-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14,2H6A2,2,0,0,0,4,4V20a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V8Z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <polyline points="10,9 9,9 8,9"/>
                </svg>
              </div>
              <div class="file-details">
                <p class="file-name">{{ uploadedFile.name }}</p>
                <p class="file-size">{{ formatFileSize(uploadedFile.size) }}</p>
              </div>
              <button class="change-file-btn" @click="triggerFileUpload">
                更换文件
              </button>
            </div>
          </div>

          <!-- 学员数据预览 -->
          <div v-if="studentList.length > 0" class="student-preview">
            <div class="preview-header-section">
              <h4>学员数据预览 (共{{ studentList.length }}名学员)</h4>
              <div class="preview-controls">
                <button 
                  v-if="!showAllStudents && studentList.length > 10" 
                  @click="showAllStudents = true"
                  class="show-more-btn"
                >
                  显示全部
                </button>
                <button 
                  v-if="showAllStudents" 
                  @click="showAllStudents = false"
                  class="show-less-btn"
                >
                  收起
                </button>
              </div>
            </div>
            <div class="preview-table">
              <div class="preview-header">
                <span>序号</span>
                <span>姓名</span>
                <span>科室</span>
                <span>班组</span>
                <span v-if="hasRecommendedExaminers">推荐考官</span>
              </div>
              <div class="preview-rows">
                <div 
                  v-for="(student, index) in displayedStudents" 
                  :key="student.id" 
                  class="preview-row"
                >
                  <span>{{ index + 1 }}</span>
                  <span>{{ student.name }}</span>
                  <span>{{ student.department }}</span>
                  <span :title="`原始数据: ${JSON.stringify(student.group)}, 类型: ${typeof student.group}`">
                    {{ student.group || '未知班组' }}
                  </span>
                  <span v-if="hasRecommendedExaminers" class="recommended-examiners">
                    <span v-if="student.recommendedExaminer1Dept">{{ student.recommendedExaminer1Dept }}</span>
                    <span v-if="student.recommendedExaminer2Dept">, {{ student.recommendedExaminer2Dept }}</span>
                    <span v-if="student.recommendedBackupDept"> (推荐 {{ student.recommendedBackupDept }})</span>
                  </span>
                </div>
                <div v-if="!showAllStudents && studentList.length > 10" class="preview-more">
                  还有 {{ studentList.length - 10 }} 名学员未显示...
                </div>
              </div>
            </div>
            
            <!-- 数据统计信息 -->
            <div class="data-summary">
              <div class="summary-item">
                <span class="summary-label">总学员数:</span>  
                <span class="summary-value">{{ studentList.length }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">科室分布</span>
                <span class="summary-value">{{ departmentStats }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">班组分布</span>
                <span class="summary-value">{{ groupStats }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2: 日期选择 -->
        <div v-if="currentStep === 2" class="step-content">
          <div class="step-title">
            <h3>📅 选择考试日期</h3>
            <p class="step-description">选择考试的开始和结束日期范围，支持快速选择常用时间段</p>
          </div>

          <!-- 快速日期选择按钮 -->
          <div class="quick-date-selection">
            <h4 class="quick-date-title">⚡ 快速选择</h4>
            <div class="quick-date-buttons">
              <button 
                class="quick-date-btn"
                @click="setQuickDateRange(14)"
                :class="{ active: isQuickDateActive(14) }"
              >
                📅 未来两周
              </button>
              <button 
                class="quick-date-btn"
                @click="setQuickDateRange(30)"
                :class="{ active: isQuickDateActive(30) }"
              >
                📅 未来一个月
              </button>
              <button 
                class="quick-date-btn"
                @click="setQuickDateRange(45)"
                :class="{ active: isQuickDateActive(45) }"
              >
                📅 未来六周
              </button>
              <button 
                class="quick-date-btn"
                @click="setQuickDateRange(60)"
                :class="{ active: isQuickDateActive(60) }"
              >
                📅 未来两个月
              </button>
              <button 
                class="quick-date-btn"
                @click="setThisMonth()"
                :class="{ active: isThisMonthActive() }"
              >
                📅 本月剩余
              </button>
              <button 
                class="quick-date-btn"
                @click="setNextMonth()"
                :class="{ active: isNextMonthActive() }"
              >
                📅 下个月
              </button>
            </div>
          </div>
          
          <div class="date-selection">
            <div class="date-group">
              <label class="date-label">
                🗓️ 考试开始日期
                <span class="date-label-tip">选择第一天考试日期</span>
              </label>
              <div class="date-input-wrapper">
              <input 
                type="date" 
                v-model="examStartDateStr"
                :min="minExamDateStr"
                class="date-input"
                  @change="onStartDateChange"
              />
                <div class="date-input-icon">📅</div>
              </div>
            </div>
            
            <div class="date-group">
              <label class="date-label">
                🗓️ 考试结束日期
                <span class="date-label-tip">选择最后一天考试日期</span>
              </label>
              <div class="date-input-wrapper">
              <input 
                type="date" 
                v-model="examEndDateStr"
                :min="examStartDateStr || minExamDateStr"
                class="date-input"
                  @change="onEndDateChange"
              />
                <div class="date-input-icon">📅</div>
              </div>
            </div>
          </div>

          <!-- 智能日期建议 - 暂时隐藏，待排班功能稳定后重新启用 -->
          <!--
          <div v-if="dateRangeSuggestion" class="date-suggestion">
            <div class="suggestion-header">
              <span class="suggestion-icon">💡</span>
              <h4 class="suggestion-title">智能建议</h4>
            </div>
            <p class="suggestion-text">{{ dateRangeSuggestion }}</p>
            <button v-if="suggestedDateRange" class="suggestion-btn" @click="applySuggestion">
              ✨ 应用建议
            </button>
            </div>
          -->

          <!-- 日期范围详细信息 -->
          <div v-if="examStartDateStr && examEndDateStr" class="date-info-enhanced">
            <div class="date-info-header">
              <span class="info-header-icon">📊</span>
              <h4 class="info-header-title">日期范围分析</h4>
            </div>
            
            <div class="info-grid">
              <div class="info-card">
                <div class="info-card-icon">📅</div>
                <div class="info-card-content">
                  <span class="info-card-label">总天数</span>
                  <span class="info-card-value">{{ getTotalDays() }} 天</span>
                </div>
              </div>
              
              <div class="info-card success">
                <div class="info-card-icon">💼</div>
                <div class="info-card-content">
                  <span class="info-card-label">工作日</span>
                  <span class="info-card-value">{{ calculateWorkdays() }} 天</span>
                </div>
              </div>
              
              <div class="info-card" :class="{ warning: hasWeekends() }">
                <div class="info-card-icon">🏖️</div>
                <div class="info-card-content">
                  <span class="info-card-label">周末</span>
                  <span class="info-card-value">{{ getWeekendDays() }} 天</span>
                </div>
              </div>
              
              <div class="info-card" :class="{ warning: getHolidayDays() > 0 }">
                <div class="info-card-icon">🎉</div>
                <div class="info-card-content">
                  <span class="info-card-label">节假日</span>
                  <span class="info-card-value">{{ getHolidayDays() }} 天</span>
                </div>
              </div>
            </div>

            <!-- 详细日期列表 -->
            <div class="date-details" v-if="examStartDateStr && examEndDateStr">
              <div class="date-details-header">
                <span class="details-icon">📋</span>
                <span class="details-title">可用考试日期</span>
                <span class="details-count">({{ getAvailableDates().length }} 天)</span>
              </div>
              <div class="date-list">
                <span 
                  v-for="date in getAvailableDates().slice(0, 10)" 
                  :key="date"
                  class="date-tag"
                  :class="getDateTypeClass(date)"
                >
                  {{ formatDateDisplay(date) }}
                </span>
                <span v-if="getAvailableDates().length > 10" class="date-more">
                  ... 还有 {{ getAvailableDates().length - 10 }} 天
                </span>
              </div>
            </div>

            <!-- 容量评估 - 暂时隐藏，待排班功能稳定后重新启用 -->
            <!-- 
            <div v-if="studentList.length > 0" class="capacity-assessment">
              <div class="capacity-header">
                <span class="capacity-icon">⚖️</span>
                <h4 class="capacity-title">智能容量评估</h4>
                <span class="capacity-badge">基于约束条件</span>
              </div>
              <div class="capacity-content">
                <div class="capacity-grid">
                  <div class="capacity-metric">
                    <span class="metric-icon">👥</span>
                    <div class="metric-info">
                      <span class="metric-label">学员总数</span>
                      <span class="metric-value">{{ studentList.length }} 人</span>
                    </div>
                  </div>
                  
                  <div class="capacity-metric">
                    <span class="metric-icon">📋</span>
                    <div class="metric-info">
                      <span class="metric-label">需要考试场次</span>
                      <span class="metric-value">{{ studentList.length * 2 }} 场</span>
                    </div>
                  </div>
                  
                  <div class="capacity-metric">
                    <span class="metric-icon">📊</span>
                    <div class="metric-info">
                      <span class="metric-label">平均每日场次</span>
                      <span class="metric-value" :class="getCapacityStatusClass()">
                        {{ getAverageExamsPerDay() }} 场/天
                      </span>
                    </div>
                  </div>
                  
                  <div class="capacity-metric">
                    <span class="metric-icon">🎯</span>
                    <div class="metric-info">
                      <span class="metric-label">理论容量上限</span>
                      <span class="metric-value theoretical">
                        {{ getTheoreticalMaxExamsPerDay() }} 场/天
                      </span>
                    </div>
                  </div>
                </div>
                
                <div class="capacity-utilization">
                  <div class="utilization-header">
                    <span class="utilization-label">容量利用率</span>
                    <span class="utilization-value" :class="getCapacityStatusClass()">
                      {{ getCapacityUtilization() }}%
                    </span>
                  </div>
                  <div class="utilization-bar">
                    <div 
                      class="utilization-fill" 
                      :class="getCapacityStatusClass()"
                      :style="{ width: Math.min(getCapacityUtilization(), 100) + '%' }"
                    ></div>
                  </div>
                </div>
                
                <div class="constraint-analysis">
                  <div class="analysis-header">
                    <span class="analysis-icon">🔍</span>
                    <span class="analysis-title">约束条件分析</span>
                  </div>
                  <div class="constraint-details">
                    <div class="constraint-item">
                      <span class="constraint-label">HC4 - 考官数量限制：</span>
                      <span class="constraint-value">
                        {{ calculateConstraintBasedCapacity().details.teacherCount || '未知' }} 名考官
                      </span>
                    </div>
                    <div class="constraint-item">
                      <span class="constraint-label">HC7 - 科室配对要求：</span>
                      <span class="constraint-value">
                        {{ calculateConstraintBasedCapacity().details.departmentCount || '未知' }} 个科室
                      </span>
                    </div>
                    <div class="constraint-item">
                      <span class="constraint-label">主要限制因素：</span>
                      <span class="constraint-bottleneck">
                        {{ calculateConstraintBasedCapacity().bottleneck }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div class="capacity-status" :class="getCapacityStatusClass()">
                  {{ getCapacityStatusText() }}
                </div>
              </div>
            </div>
            -->
          </div>

          <!-- 日期选择提示 -->
          <div class="date-tips">
            <div class="tip-header">
              <span class="tip-icon">💡</span>
              <span class="tip-title">选择提示</span>
            </div>
            <ul class="tip-list">
              <li class="tip-item">
                <span class="tip-bullet">•</span>
                <span class="tip-text">建议选择至少包含 <strong>{{ Math.ceil(studentList.length / 10) }}</strong> 个工作日的日期范围</span>
              </li>
              <li class="tip-item">
                <span class="tip-bullet">•</span>
                <span class="tip-text">周末可以安排考试，但行政班考官不参与周末考试</span>
              </li>
              <li class="tip-item">
                <span class="tip-bullet">•</span>
                <span class="tip-text">法定节假日不能安排考试，系统会自动跳过</span>
              </li>
              <li class="tip-item">
                <span class="tip-bullet">•</span>
                <span class="tip-text">使用快速选择按钮可以快速设置常用日期范围</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 步骤3: 硬约束配置-->
        <div v-if="currentStep === 3" class="step-content">
          <div class="step-title">
            <h3>硬约束配置</h3>
              <p class="step-description">设置必须满足的约束条件，违反将导致排班失败</p>
          </div>
          
          <div class="constraint-list">
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC1: 法定节假日不安排考试（周六周日可以考试，但行政班考官周末不参加考试）</h4>
                <p>法定节假日（如春节、国庆节等）不能安排考试，周六、周日可以安排考试，但行政班考官在周末不参加考试安排，夜班考官可以在周末参加考试</p>
                <span class="constraint-weight">权重: 5000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC2: 考官1与学员同科室</h4>
                <p>考官1必须与考生属于同一科室，这是考试公平性和专业性的基本要求</p>
                <span class="constraint-weight">权重: 8000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC3: 考官执勤白班不能安排考试（行政班考官除外）</h4>
                <p>考官在执勤白班期间不能安排考试任务，但行政班考官不受此限制。检查考官1、考官2、备份考官的工作安排，确保考官有足够的时间和精力进行考试监督</p>
                <span class="constraint-weight">权重: 7000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC4: 每名考官每天只能监考一名考生</h4>
                <p>同一考官在同一天只能参与一场考试，防止考官工作负荷过重，确保考试质量和公平性</p>
                <span class="constraint-weight">权重: 9000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC5: 考生执勤白班不能安排考试</h4>
                <p>考生在执勤白班期间不能安排考试，考生需要专注于白班工作，无法参加考试，确保考生有充分的准备时间</p>
                <span class="constraint-weight">权重: 6000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC6: 考生需要在连续两天完成考试</h4>
                <p>考生的考试必须安排在连续的两天内完成。两种有效情况：1.除去执勤日的两个连续休息日 2.第一天执勤晚班、第二天休息日</p>
                <span class="constraint-weight">权重: 4000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC7: 必须有考官1和考官2两名考官，且不能同科室</h4>
                <p>每场考试必须配备两名不同科室的考官，确保考试的公正性和客观性，不同科室的考官提供多角度评估，防止科室内部偏见影响考试结果</p>
                <span class="constraint-weight">权重: 10000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            
            <div class="constraint-item readonly">
              <div class="constraint-info">
                <h4>HC8: 备份考官不能与考官1和考官2是同一人</h4>
                <p>备份考官必须是独立的第三人，确保在主考官无法履职时有替代方案，维护考试的连续性和稳定性，避免人员冲突和角色混淆</p>
                <span class="constraint-weight">权重: 3000</span>
              </div>
              <div class="constraint-status enabled">已启用</div>
            </div>
            

          </div>
        </div>

        <!-- 步骤4: 软约束配置-->
        <div v-if="currentStep === 4" class="step-content">
          <div class="step-title">
            <h3>软约束配置</h3>
            <p class="step-description">设置优先考虑的约束条件，可以调整权重</p>
          </div>
          
          <div class="constraint-list">
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC1: 晚班考官优先级最高权重</h4>
                <p>优先安排执勤晚班的考官参与考试，权重等级仅次于科室推荐，优先选择晚班考官作为考官2和备份考官</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.nightShiftTeacherPriority }" @click="toggleConstraint('nightShiftTeacherPriority')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.nightShiftTeacherPriority" class="weight-control">
                  <label>权重: 100</label>
                  <input 
                    type="range" 
                    min="80" 
                    max="150" 
                    value="100"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC2: 考官2专业匹配</h4>
                <p>考官2优先来自推荐科室，提高专业匹配度和考试质量</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.examiner2ProfessionalMatch }" @click="toggleConstraint('examiner2ProfessionalMatch')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.examiner2ProfessionalMatch" class="weight-control">
                  <label>权重: 90</label>
                  <input 
                    type="range" 
                    min="70" 
                    max="120" 
                    value="90"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC3: 休息第一天考官优先级次高权重</h4>
                <p>在晚班考官之后，优先选择休息第一天的考官参与考试</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.firstRestDayTeacherPriority }" @click="toggleConstraint('firstRestDayTeacherPriority')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.firstRestDayTeacherPriority" class="weight-control">
                  <label>权重: 80</label>
                  <input 
                    type="range" 
                    min="60" 
                    max="100" 
                    value="80"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC4: 备份考官专业匹配</h4>
                <p>备份考官优先来自推荐科室，与SC1-SC3优先级分数可叠加</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.backupExaminerProfessionalMatch }" @click="toggleConstraint('backupExaminerProfessionalMatch')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.backupExaminerProfessionalMatch" class="weight-control">
                  <label>权重: 70</label>
                  <input 
                    type="range" 
                    min="50" 
                    max="100" 
                    value="70"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC5: 休息第二天考官优先级中等权重</h4>
                <p>在休息第一天考官之后，优先选择休息第二天的考官参与考试</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.secondRestDayTeacherPriority }" @click="toggleConstraint('secondRestDayTeacherPriority')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.secondRestDayTeacherPriority" class="weight-control">
                  <label>权重: 60</label>
                  <input 
                    type="range" 
                    min="40" 
                    max="80" 
                    value="60"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC6: 考官2备选方案</h4>
                <p>当推荐科室考官2不可用时的备选方案，与SC1-SC5优先级分数可叠加</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.examiner2AlternativeOption }" @click="toggleConstraint('examiner2AlternativeOption')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.examiner2AlternativeOption" class="weight-control">
                  <label>权重: 50</label>
                  <input 
                    type="range" 
                    min="30" 
                    max="70" 
                    value="50"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC7: 行政班考官优先级最低权重</h4>
                <p>在所有班组考官之后，最后考虑行政班考官参与考试</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.adminTeacherPriority }" @click="toggleConstraint('adminTeacherPriority')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.adminTeacherPriority" class="weight-control">
                  <label>权重: 40</label>
                  <input 
                    type="range" 
                    min="20" 
                    max="60" 
                    value="40"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC8: 备份考官备选方案</h4>
                <p>当推荐科室备份考官不可用时的备选方案，与SC1-SC7优先级分数可叠加</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.backupExaminerAlternativeOption }" @click="toggleConstraint('backupExaminerAlternativeOption')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.backupExaminerAlternativeOption" class="weight-control">
                  <label>权重: 30</label>
                  <input 
                    type="range" 
                    min="20" 
                    max="50" 
                    value="30"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC9: 区域协作鼓励</h4>
                <p>允许区域三室和区域七室的考官互相使用，提高资源利用率和协作效率</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.allowDept37CrossUse }" @click="toggleConstraint('allowDept37CrossUse')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.allowDept37CrossUse" class="weight-control">
                  <label>权重: 20</label>
                  <input 
                    type="range" 
                    min="10" 
                    max="40" 
                    value="20"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC10: 工作量均衡</h4>
                <p>尽量平衡各考官的工作负载，避免个别考官过度繁忙或空闲</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.balanceWorkload }" @click="toggleConstraint('balanceWorkload')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.balanceWorkload" class="weight-control">
                  <label>权重: 10</label>
                  <input 
                    type="range" 
                    min="5" 
                    max="20" 
                    value="10"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>SC11: 日期分配均衡</h4>
                <p>尽量将考试时间均匀分配，避免集中在某些日期，促进日期分配的平衡性</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.preferLaterDates }" @click="toggleConstraint('preferLaterDates')">
                  <div class="toggle-handle"></div>
                </div>
                <div v-if="constraints.preferLaterDates" class="weight-control">
                  <label>权重: 5</label>
                  <input 
                    type="range" 
                    min="1" 
                    max="15" 
                    value="5"
                    class="weight-slider"
                    disabled
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤5: 高级配置 -->
        <div v-if="currentStep === 5" class="step-content">
          <div class="step-title">
            <h3>高级配置</h3>
            <p class="step-description">智能约束和特殊配置选项</p>
          </div>
          
          <!-- 算法选择 -->
          <div class="algorithm-selection">
            <h4>排班算法选择</h4>
            <div class="algorithm-options">
              <div 
                v-for="option in algorithmOptions" 
                :key="option.value"
                class="algorithm-option"
                :class="{ active: selectedAlgorithm === option.value }"
                @click="selectedAlgorithm = option.value as 'optaplanner'"
              >
                <div class="algorithm-icon">{{ (option as any).icon }}</div>
                <div class="algorithm-info">
                  <h5>{{ (option as any).name }}</h5>
                  <p>{{ option.description }}</p>
                  <div class="algorithm-features">
                    <span v-for="feature in (option as any).features" :key="feature" class="feature-tag">{{ feature }}</span>
                  </div>
                </div>
                <div class="algorithm-status">
                  <span v-if="(option as any).recommended" class="recommended-badge">推荐</span>
                  <span v-if="(option as any).experimental" class="experimental-badge">实验</span> 
                </div>
              </div>
            </div>
          </div>
          
          <!-- 高级算法配置 -->
          <div class="constraint-list">
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>智能时间分散优化</h4>
                <p>启用智能算法优化考试时间分布，避免时间过于集中，提高排班合理性</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.enableTimeSpreadOptimization }" @click="toggleConstraint('enableTimeSpreadOptimization')">
                  <div class="toggle-handle"></div>
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>动态权重调整</h4>
                <p>根据实际排班情况动态调整约束权重，提高算法适应性和排班成功率</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.enableDynamicWeightAdjustment }" @click="toggleConstraint('enableDynamicWeightAdjustment')">
                  <div class="toggle-handle"></div>
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>智能冲突解决</h4>
                <p>当出现约束冲突时，启用智能算法自动寻找最优解决方案</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.enableIntelligentConflictResolution }" @click="toggleConstraint('enableIntelligentConflictResolution')">
                  <div class="toggle-handle"></div>
                </div>
              </div>
            </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>预警系统</h4>
                <p>启用排班预警系统，提前识别潜在的排班问题和资源冲突</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.enableEarlyWarningSystem }" @click="toggleConstraint('enableEarlyWarningSystem')">
                  <div class="toggle-handle"></div>
                </div>
                </div>
              </div>
            
            <div class="constraint-item">
              <div class="constraint-info">
                <h4>历史数据优化</h4>
                <p>基于历史排班数据优化算法参数，提高排班质量和效率</p>
              </div>
              <div class="constraint-controls">
                <div class="constraint-toggle" :class="{ active: constraints.enableHistoricalDataOptimization }" @click="toggleConstraint('enableHistoricalDataOptimization')">
                  <div class="toggle-handle"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤6: 确认执行 -->
        <div v-if="currentStep === 6" class="step-content">
          <div class="step-title">
            <h3>确认并执行排班</h3>
            <p class="step-description">检查配置信息，确认无误后开始排班</p> 
          </div>
          
          <div class="summary-section">
            <div class="summary-item">
              <h4>学员信息</h4>
              <p>共{{ studentList.length }} 名学员</p>
              <p v-if="uploadedFile">文件: {{ uploadedFile.name }}</p>
            </div>
            
            <div class="summary-item">
              <h4>考试日期</h4>
              <p v-if="examStartDateStr && examEndDateStr">
                {{ examStartDateStr }} 到 {{ examEndDateStr }}
              </p>
              <div class="date-statistics">
                <p>共{{ getDateRangeStatistics().totalDays }} 天</p>
                <p class="workday-detail">
                  工作日：{{ getDateRangeStatistics().workdays }} 天
                  <span v-if="getDateRangeStatistics().adjustedWorkdays > 0" class="adjusted-workday">
                    (含调休{{ getDateRangeStatistics().adjustedWorkdays }} 天)
                  </span>
                </p>
                <p v-if="getDateRangeStatistics().holidays > 0" class="holiday-warning">
                  节假日：{{ getDateRangeStatistics().holidays }} 天⚠️
                </p>
                <p v-if="getDateRangeStatistics().weekends > 0" class="weekend-info">
                  周末：{{ getDateRangeStatistics().weekends }} 天
                </p>
              </div>
            </div>
            
            <div class="summary-item">
              <h4>算法选择</h4>
              <p>{{ algorithmOptions.find((opt: any) => opt.value === selectedAlgorithm)?.label || '未选择' }}</p>
              <p class="algorithm-desc">{{ algorithmOptions.find((opt: any) => opt.value === selectedAlgorithm)?.description || '' }}</p>
            </div>
            
            <div class="summary-item">
              <h4>约束配置</h4>
              <div class="constraint-summary">
                <div class="summary-group">
                  <h5>硬约束 (8/8)</h5>
                  <ul>
                    <li>HC1: 法定节假日不安排考试 (权重: 5000)</li>
                    <li>HC2: 考官1与学员同科室 (权重: 8000)</li>
                    <li>HC3: 考官执勤白班不能安排考试 (权重: 7000)</li>
                    <li>HC4: 每名考官每天只能监考一名考生 (权重: 9000)</li>
                    <li>HC5: 考生执勤白班不能安排考试 (权重: 6000)</li>
                    <li>HC6: 考生需要在连续两天完成考试 (权重: 4000)</li>
                    <li>HC7: 必须有考官1和考官2两名考官，且不能同科室 (权重: 10000)</li>
                    <li>HC8: 备份考官不能与考官1和考官2是同一人 (权重: 3000)</li>
                  </ul>
                </div>
                
                <div class="summary-group">
                  <h5>软约束 ({{ getActiveSoftConstraintsCount() }}/11)</h5>
                  <ul>
                    <li v-if="constraints.nightShiftTeacherPriority">SC1: 晚班考官优先级最高权重 (权重: 100)</li>
                    <li v-if="constraints.examiner2ProfessionalMatch">SC2: 考官2专业匹配 (权重: 90)</li>
                    <li v-if="constraints.firstRestDayTeacherPriority">SC3: 休息第一天考官优先级次高权重 (权重: 80)</li>
                    <li v-if="constraints.backupExaminerProfessionalMatch">SC4: 备份考官专业匹配 (权重: 70)</li>
                    <li v-if="constraints.secondRestDayTeacherPriority">SC5: 休息第二天考官优先级中等权重 (权重: 60)</li>
                    <li v-if="constraints.examiner2AlternativeOption">SC6: 考官2备选方案 (权重: 50)</li>
                    <li v-if="constraints.adminTeacherPriority">SC7: 行政班考官优先级最低权重 (权重: 40)</li>
                    <li v-if="constraints.backupExaminerAlternativeOption">SC8: 备份考官备选方案 (权重: 30)</li>
                    <li v-if="constraints.allowDept37CrossUse">SC9: 区域协作鼓励 (权重: 20)</li>
                    <li v-if="constraints.balanceWorkload">SC10: 工作量均衡 (权重: 10)</li>
                    <li v-if="constraints.preferLaterDates">SC11: 日期分配均衡 (权重: 5)</li>
                  </ul>
                </div>
                
                <div class="summary-group">
                  <h5>高级配置</h5>
                  <ul>
                    <li>算法: OptaPlanner 经典算法</li>
                    <li>智能时间分散优化: {{ constraints.enableTimeSpreadOptimization ? '已启用' : '已禁用' }}</li>
                    <li>动态权重调整: {{ constraints.enableDynamicWeightAdjustment ? '已启用' : '已禁用' }}</li>
                    <li>智能冲突解决: {{ constraints.enableIntelligentConflictResolution ? '已启用' : '已禁用' }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 排班进度 -->
          <div v-if="isScheduling" class="scheduling-progress">
            <div class="progress-header">
              <h4>正在执行排班...</h4>
              <span class="progress-percentage">{{ schedulingProgress }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: schedulingProgress + '%' }"></div>
            </div>
            <div class="progress-details">
              <p class="progress-text">{{ currentProgressMessage || '请稍候，系统正在为您生成最优排班方案' }}</p>
              <div v-if="currentAssignmentCount > 0" class="assignment-counter">
                已分配: {{ currentAssignmentCount }} / {{ totalStudents * 2 }} 个考试安排
              </div>
            </div>
          </div>
          
          <!-- 错误提示 -->
          <div v-if="schedulingError" class="error-section">
            <div class="error-icon">⚠️</div>
            <div class="error-content">
              <h4>排班失败</h4>
              <p>{{ schedulingError }}</p>
            </div>
          </div>
        </div>

        <!-- 步骤导航按钮 -->
        <div class="step-navigation">
          <button 
            v-if="currentStep > 1" 
            class="nav-btn nav-btn-secondary" 
            @click="previousStep"
          >
            上一步         </button>
          
          <div class="nav-spacer"></div>
          
          <button 
            v-if="currentStep < 6" 
            class="nav-btn nav-btn-primary" 
            @click="nextStep"
            :disabled="!canProceedToNextStep()"
          >
            下一步         </button>
          
          <button 
            v-if="currentStep === 6" 
            class="nav-btn nav-btn-success" 
            @click="startScheduling"
            :disabled="isScheduling || !canProceedToNextStep()"
          >
            <span v-if="!isScheduling">开始排班</span>
            <span v-else>排班中...</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 增强错误反馈模态框 -->
  <EnhancedErrorFeedbackModal
    :visible="enhancedErrorFeedbackService.getState().isVisible"
    :error-type="enhancedErrorFeedbackService.getState().errorType"
    :error-message="enhancedErrorFeedbackService.getState().errorMessage"
    :conflicts="enhancedErrorFeedbackService.getState().conflicts"
    @close="enhancedErrorFeedbackService.hideErrorFeedback()"
    @auto-resolve="handleAutoResolveConflict"
    @execute-action="handleExecuteAction"
    @export-report="handleExportReport"
  />

  <!-- 🎯 统一结果弹窗 -->
  <div v-if="showUnifiedResultModal" class="unified-modal-overlay" @click="closeUnifiedModal">
    <div class="unified-modal" @click.stop>
      <!-- 标题栏 -->
      <div class="modal-header">
        <div class="header-icon">
          <CheckCircle v-if="unifiedResultData?.success && constraintViolations.length === 0" class="success-icon" />
          <AlertTriangle v-else-if="unifiedResultData?.success && constraintViolations.length > 0" class="warning-icon" />
          <XCircle v-else class="error-icon" />
        </div>
        <div class="header-content">
          <h3 class="modal-title">{{ getUnifiedResultTitle() }}</h3>
          <p class="modal-subtitle">{{ getUnifiedResultSubtitle() }}</p>
        </div>
        <button @click="closeUnifiedModal" class="close-button">
          <X class="close-icon" />
        </button>
      </div>

      <!-- 主要内容 -->
      <div class="modal-body">
        <!-- 排班统计 -->
        <div class="stats-section">
          <h4 class="section-title">📊 排班统计</h4>
          <div class="stats-grid">
            <div class="stat-item success">
              <span class="stat-label">完成率</span>
              <span class="stat-value">{{ getUnifiedCompletionRate() }}%</span>
            </div>
            <div class="stat-item info">
              <span class="stat-label">分配学员</span>
              <span class="stat-value">{{ getUnifiedAssignedStudents() }}/{{ getUnifiedTotalStudents() }}</span>
            </div>
            <div class="stat-item info">
              <span class="stat-label">考试任务</span>
              <span class="stat-value">{{ getUnifiedTotalStudents() * 2 }}场</span>
            </div>
            <div class="stat-item" :class="getUnifiedHardConstraintClass()">
              <span class="stat-label">硬约束违反</span>
              <span class="stat-value">{{ constraintViolations.filter(v => v.severity === 'error').length }}个</span>
            </div>
            <div class="stat-item softscore-stat" :class="getSoftScoreClass()">
              <span class="stat-label">软约束得分</span>
              <span class="stat-value">{{ formatSoftScore(unifiedResultData?.statistics?.softConstraintsScore) }}</span>
              <span v-if="unifiedResultData?.statistics?.bestSoftConstraintsScore != null" class="stat-hint">
                峰值: {{ formatSoftScore(unifiedResultData?.statistics?.bestSoftConstraintsScore ?? undefined) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 约束违反详情 -->
        <div v-if="constraintViolations.length > 0" class="violations-section">
          <h4 class="section-title">⚠️ 约束违反详情</h4>
          <div class="violations-summary">
            <span class="violations-count">发现 {{ constraintViolations.length }} 个约束违反</span>
            <span class="severity-breakdown">
              严重: {{ constraintViolations.filter(v => v.severity === 'error').length }}个，
              轻微: {{ constraintViolations.filter(v => v.severity === 'warning').length }}个
            </span>
          </div>
          
          <div class="violations-list">
            <div 
              v-for="(violation, index) in constraintViolations.slice(0, 5)" 
              :key="violation.id"
              class="violation-item"
              :class="violation.severity"
            >
              <div class="violation-header">
                <div class="violation-icon">
                  <AlertTriangle v-if="violation.severity === 'error'" class="error-icon-small" />
                  <AlertCircle v-else class="warning-icon-small" />
                </div>
                <div class="violation-title">{{ violation.title }}</div>
                <div class="violation-count">{{ violation.count || 1 }}个</div>
              </div>
              
              <div class="violation-details">
                <p class="violation-description">{{ violation.description }}</p>
              </div>
            </div>
            
            <div v-if="constraintViolations.length > 5" class="more-violations">
              还有 {{ constraintViolations.length - 5 }} 个约束违反未显示...
            </div>
          </div>
        </div>

        <!-- 成功信息 -->
        <div v-if="unifiedResultData?.success && constraintViolations.length === 0" class="success-section">
          <div class="success-message">
            <CheckCircle class="success-icon-large" />
            <div class="success-content">
              <h4>🎉 排班完成！</h4>
              <p>成功为 {{ getUnifiedTotalStudents() }} 位学员安排了 {{ getUnifiedTotalStudents() * 2 }} 场考试，所有约束条件均已满足。</p>
              <p class="success-detail">✅ 已分配主考官和副考官 &nbsp;&nbsp; ✅ 日期和时间安排合理 &nbsp;&nbsp; ✅ 所有约束验证通过</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="modal-footer">
        <div class="footer-info">
          <span class="engine-info">🚀 使用 OptaPlanner 约束求解引擎</span>
        </div>
        <div class="footer-actions">
          <button @click="closeUnifiedModal" class="action-button primary">
            我知道了
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ConstraintViolationChecker, logViolationReport } from '../utils/constraintViolationChecker'
import { 
  Home, 
  Users, 
  Calendar, 
  Settings,
  ChevronLeft,
  Trash2,
  Download,
  Upload,
  FileText,
  Plus,
  Eye,
  Edit,
  AlertCircle,
  Clock,
  RefreshCw,
  BarChart,
  Activity,
  CheckCircle,
  AlertTriangle,
  XCircle,
  X
} from 'lucide-vue-next'
import SimpleDateRangePicker from '../components/SimpleDateRangePicker.vue'
import RealTimeStatusMonitor from '../components/RealTimeStatusMonitor.vue'

// 移除增强排班服务V2导入
// 保留OptaPlanner作为备用
import { 
  optaPlannerService,
  type OptaPlannerRequest,
  type OptaPlannerResponse
} from '../services/optaplanner-service'
// 移除AI相关服务导入，只保留OptaPlanner
// 保留原有类型定义以兼容现有代码
import { 
  type StudentInfo,
  type TeacherInfo,
  type SchedulingResult
} from '../utils/types'
import { storageService, type ScheduleResultRecord } from '../utils/storageService'
import { unifiedStorageService } from '../services/unifiedStorageService'
import { FrontendDisplayFixer } from '../utils/frontendDisplayFixer'
// 移除不存在的cacheManager导入，相关功能已集成到unifiedStorageService
import { DataValidationService } from '../services/dataValidationService'
import ConstraintViolationAlert, { createHolidayViolation, createInsufficientExaminersViolation, filterAndMergeViolations, type ConstraintViolation } from '../components/ConstraintViolationAlert.vue'
import { holidayService } from '../services/holidayService'
import { dutyRotationService } from '../services/dutyRotationService'
import SmartManualEditModal from '../components/SmartManualEditModal.vue'
import { smartRecommendationService } from '../services/smartRecommendationService'
import { dataManagementApi } from '../services/dataManagementApi'
import EnhancedErrorFeedbackModal from '../components/EnhancedErrorFeedbackModal.vue'
import { enhancedErrorFeedbackService } from '../services/enhancedErrorFeedbackService'
import type { ConflictInfo } from '../types/errorFeedback'
import { checkScheduleConstraints, printConstraintCheckResult } from '../utils/scheduleConstraintChecker'

// 路由实例
const route = useRoute()

// 响应式数组
const sidebarCollapsed = ref(false)
// WebSocket 会话ID（用于实时进度监控）
const wsSessionId = ref<string | null>(null)
// 实时进度服务缓存，避免重复连接与事件堆积
let realtimeProgressServiceInstance: any | null = null
let realtimeProgressUnsubscribe: (() => void) | null = null
let activeRealtimeSessionId: string | null = null
const showCreateModal = ref(false)
const showConstraintsPanel = ref(false)
// 移除约束条件面板

// 分步骤相关状态
const currentStep = ref(1)
    const examStartDateStr = ref('')
    const examEndDateStr = ref('')
    
    // 添加日期变化监听器用于调试
    watch(examStartDateStr, (newVal, oldVal) => {
      console.log('🔍 examStartDateStr 变化:', oldVal, '→', newVal)
    })
    
    watch(examEndDateStr, (newVal, oldVal) => {
      console.log('🔍 examEndDateStr 变化:', oldVal, '→', newVal)
    })
    
    // 算法选择 - 移除智能约束传播算法选项
    const selectedAlgorithm = ref<'optaplanner'>('optaplanner')
    const algorithmOptions = [
      { 
        value: 'optaplanner', 
        label: 'OptaPlanner 经典算法', 
        description: '稳定可靠的传统算法',
        icon: '🛡️',
        features: ['高稳定性', '成熟可靠', '企业级'],
        recommended: true
      }
    ]
    
    // 约束配置 - 更新为与后端一致的字段名称
    const constraints = ref({
      // 硬约束（只读）
      workdaysOnlyExam: true,
      examinerDepartmentRules: true,
      twoMainExaminersRequired: true,
      noDayShiftExaminer: true,
      consecutiveTwoDaysExamEnabled: true,
      noExaminerTimeConflict: true,
      mustHaveTwoDifferentDepartmentExaminers: true,
      backupExaminerMustBeDifferentPerson: true,
      
      // 软约束（可配置）- 按照SC1-SC11统一命名，全部默认开启
      nightShiftTeacherPriority: true,           // SC1: 晚班考官优先级最高权重
      examiner2ProfessionalMatch: true,          // SC2: 考官2专业匹配
      firstRestDayTeacherPriority: true,         // SC3: 休息第一天考官优先级次高权重
      backupExaminerProfessionalMatch: true,     // SC4: 备份考官专业匹配
      secondRestDayTeacherPriority: true,        // SC5: 休息第二天考官优先级中等权重
      examiner2AlternativeOption: true,          // SC6: 考官2备选方案
      adminTeacherPriority: true,                // SC7: 行政班考官优先级最低权重
      backupExaminerAlternativeOption: true,     // SC8: 备份考官备选方案
      allowDept37CrossUse: true,                 // SC9: 区域协作鼓励
      balanceWorkload: true,                     // SC10: 工作量均衡
      preferLaterDates: true,                    // SC11: 日期分配均衡
      
      // 高级配置选项
      enableTimeSpreadOptimization: true,        // 智能时间分散优化
      enableDynamicWeightAdjustment: true,       // 动态权重调整
      enableIntelligentConflictResolution: true, // 智能冲突解决
      enableEarlyWarningSystem: true,            // 预警系统
      enableHistoricalDataOptimization: true,    // 历史数据优化
    })
    
    // 约束权重 - 严格按照文档权重设置
    const constraintWeights = ref({
      // SC1: 晚班考官优先级最高权重（权重：100）
      preferNightShiftTeachers: 100,
      // SC2: 考官2专业匹配（权重：90）
      preferRecommendedExaminer2: 90,
      // SC3: 休息第一天考官优先级次高权重（权重：80）
      preferFirstRestDayTeachers: 80,
      // SC4: 备份考官专业匹配（权重：70）
      preferRecommendedBackup: 70,
      // SC5: 休息第二天考官优先级中等权重（权重：60）
      preferSecondRestDayTeachers: 60,
      // SC6: 考官2备选方案（权重：50）
      preferNonRecommendedExaminer2: 50,
      // SC7: 行政班考官优先级最低权重（权重：40）
      preferAdminTeachers: 40,
      // SC8: 备份考官备选方案（权重：30）
      preferNonRecommendedBackup: 30,
      // SC9: 区域协作鼓励（权重：20）
      allowDept37CrossUse: 20,
      // SC10: 工作量均衡（权重：10）
      balanceWorkload: 10,
      // SC11: 日期分配均衡（权重：5）
      preferLaterDates: 5,
    })

// 实时进度显示相关状态
const currentProgressMessage = ref('')

// 🎯 实时计算流程窗口状态
const realtimeProgressVisible = ref(false)
const realtimeProgressMinimized = ref(false)
const isTableUpdating = ref(false)
const lastTableUpdate = ref('')
const realtimeLogs = ref<Array<{
  time: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
}>>([])

// 🎯 实时进度窗口拖拽状态
const realtimeWindowPosition = ref({ x: 20, y: 20 }) // 初始位置
const realtimeWindowDragging = ref(false)
const realtimeWindowRef = ref<HTMLElement | null>(null)

// 🎬 表格动画状态
const isTableAnimating = ref(false)
const animationCells = ref<Array<{
  rowIndex: number;
  cellType: 'examiner1' | 'examiner2' | 'backup' | 'date';
  day: 1 | 2;
  animationType: 'typing' | 'selecting' | 'confirming' | 'sliding';
  content: string;
  progress: number;
}>>([])

// 🎬 OptaPlanner风格的排班动画效果
const startTableAnimation = () => {
  if (!isScheduling.value) return
  
  // 创建基于真实学员数据的初始动画表格
  const animationResults = []
  const animationStudents = Math.min(studentList.value.length, 12) // 最多12个学员显示
  
  for (let i = 0; i < animationStudents; i++) {
    const student = studentList.value[i]
    animationResults.push({
      id: `solving-${i}`,
      student: student?.name || `学员${i + 1}`,
      department: student?.department || '待分配',
      date1: '计算中...',
      type1: '现场',
      date2: '计算中...',
      type2: '口试', 
      examiner1_1: '分配中...',
      examiner1_2: '分配中...',
      backup1: '分配中...',
      examiner2_1: '分配中...',
      examiner2_2: '分配中...',
      backup2: '分配中...'
    } as any)
  }
  
  scheduleResults.value = animationResults
  isTableAnimating.value = true
  animationCells.value = []
  
  console.log('🎬 启动基于真实数据的华容道动画', {
    学员数量: animationStudents,
    考官池大小: teacherList.value.length,
    初始表格数据: animationResults.length
  })
  
  // 华容道式排班动画 - 考官名字移动，日期变化
  startHuaRongDaoAnimation()
}

// 🎬 基于真实考官数据的华容道动画 - 显示真实求解过程
const startHuaRongDaoAnimation = () => {
  // 获取真实考官数据
  const realTeacherNames = teacherList.value.map(teacher => teacher.name || '未知考官')
  if (realTeacherNames.length === 0) {
    addRealtimeLog('⚠️ 考官数据为空，使用默认考官池', 'warning')
    realTeacherNames.push('张考官', '李考官', '王考官', '刘考官', '陈考官')
  }
  
  console.log('🎲 使用真实考官数据:', realTeacherNames)
  
  // 生成考试日期池（基于用户选择的日期范围）
  const generateRealDatePool = () => {
    const dates = []
    if (examStartDate.value && examEndDate.value) {
      const current = new Date(examStartDate.value)
      const end = new Date(examEndDate.value)
      
      while (current <= end) {
        const month = String(current.getMonth() + 1).padStart(2, '0')
        const day = String(current.getDate()).padStart(2, '0')
        dates.push(`${month}.${day}`)
        current.setDate(current.getDate() + 1)
      }
    }
    return dates.length > 0 ? dates : ['10.15', '10.16', '10.17', '10.18', '10.19']
  }
  
  const realDatePool = generateRealDatePool()
  let animationInterval: NodeJS.Timeout
  let isAnimationStopped = false
  
  addRealtimeLog('🎲 启动基于真实数据的华容道动画', 'info')
  addRealtimeLog(`📊 考官池: ${realTeacherNames.length}名, 日期池: ${realDatePool.length}天`, 'info')
  
  // 智能华容道动画 - 模拟真实求解过程
  const runIntelligentAnimation = () => {
    if (!isTableAnimating.value || isAnimationStopped) {
      if (animationInterval) clearInterval(animationInterval)
      return
    }
    
    scheduleResults.value.forEach((row: any, rowIndex: number) => {
      // 智能日期调整（基于真实日期范围）
      if (Math.random() < 0.25) { // 25% 概率调整日期
        row.date1 = realDatePool[Math.floor(Math.random() * realDatePool.length)]
        row.date2 = realDatePool[Math.floor(Math.random() * realDatePool.length)]
        
        // 确保两个日期不同
        while (row.date1 === row.date2 && realDatePool.length > 1) {
          row.date2 = realDatePool[Math.floor(Math.random() * realDatePool.length)]
        }
      }
      
      // 智能考官分配移动（使用真实考官名字）
      if (Math.random() < 0.35) { // 35% 概率移动考官
        const positions = ['examiner1_1', 'examiner1_2', 'backup1', 'examiner2_1', 'examiner2_2', 'backup2']
        const targetPosition = positions[Math.floor(Math.random() * positions.length)]
        
        // 选择真实考官
        const selectedTeacher = realTeacherNames[Math.floor(Math.random() * realTeacherNames.length)]
        const oldValue = row[targetPosition]
        row[targetPosition] = selectedTeacher
        
        // 创建移动效果动画
        const cellType = targetPosition.includes('examiner1') ? 'examiner1' : 
                        targetPosition.includes('examiner2') ? 'examiner2' : 'backup'
        const day = targetPosition.includes('1') ? 1 : 2
        
        // 清除旧的动画状态
        animationCells.value = animationCells.value.filter(cell => 
          !(cell.rowIndex === rowIndex && cell.cellType === cellType && cell.day === day)
        )
        
        // 添加移动动画效果
        animationCells.value.push({
          rowIndex,
          cellType: cellType as any,
          day: day as any,
          animationType: 'sliding' as const,
          content: selectedTeacher,
          progress: 100
        })
        
        // 记录移动日志
        if (oldValue !== selectedTeacher) {
          addRealtimeLog(`🔄 ${row.student}: ${oldValue} → ${selectedTeacher}`, 'info')
        }
      }
    })
    
    // 随机记录算法状态
    if (Math.random() < 0.15) {
      const algorithmActions = [
        '评估约束冲突', '优化考官分配', '调整考试日期', 
        '平衡工作负载', '检查科室匹配', '验证时间冲突'
      ]
      const action = algorithmActions[Math.floor(Math.random() * algorithmActions.length)]
      addRealtimeLog(`🔍 ${action}...`, 'info')
    }
  }
  
  // 启动智能动画循环
  animationInterval = setInterval(runIntelligentAnimation, 600) // 每600ms更新一次
  
  // 监听真实结果，及时停止动画
  const stopAnimationOnResult = () => {
    isAnimationStopped = true
    if (animationInterval) {
      clearInterval(animationInterval)
      addRealtimeLog('✅ 检测到真实结果，停止华容道动画', 'success')
      
      // 更新表格状态
      const now = new Date()
      lastTableUpdate.value = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
    }
  }
  
  // 设置结果监听器（将在updateScheduleResults中调用）
  ;(window as any).stopHuaRongDaoAnimation = stopAnimationOnResult
  
  // 备用停止机制
  setTimeout(() => {
    if (!isAnimationStopped) {
      stopAnimationOnResult()
    }
  }, 30000) // 30秒后强制停止
}

// 🎬 停止表格动画
const stopTableAnimation = () => {
  isTableAnimating.value = false
  animationCells.value = []
  
  // 清理华容道演示数据，为真实数据做准备
  const isAnimationData = scheduleResults.value.some((result: any) => 
    typeof result.id === 'string' && result.id.startsWith('solving-')
  )
  
  if (isAnimationData) {
    console.log('🧹 清理华容道演示数据，为最终结果做准备')
    scheduleResults.value = []
  }
  
  // 清理全局动画停止函数
  if ((window as any).stopHuaRongDaoAnimation) {
    delete (window as any).stopHuaRongDaoAnimation
  }
  
  console.log('🎬 华容道动画已停止')
}

// 🎬 打字动画效果
const startTypingAnimation = (cellIndex: number) => {
  const cell = animationCells.value[cellIndex]
  if (!cell) return
  
  const fullText = cell.content
  const typingSpeed = 50 // 毫秒
  let currentIndex = 0
  
  const typeInterval = setInterval(() => {
    if (!isTableAnimating.value || currentIndex >= fullText.length) {
      clearInterval(typeInterval)
      if (cell) {
        cell.progress = 100
        cell.animationType = 'confirming'
      }
      return
    }
    
    currentIndex++
    if (cell) {
      cell.progress = (currentIndex / fullText.length) * 100
    }
  }, typingSpeed)
}

// 🎬 获取单元格动画状态
const getCellAnimationState = (rowIndex: number, cellType: string, day: number) => {
  return animationCells.value.find(cell => 
    cell.rowIndex === rowIndex && 
    cell.cellType === cellType && 
    cell.day === day
  )
}

// 🔄 OptaPlanner风格的增量更新机制已移除

// 🔄 转换OptaPlanner结果为表格格式（复用现有逻辑）
const convertOptaPlannerResultToTableFormat = async (result: SchedulingResult) => {
  console.log('🔄 开始转换OptaPlanner结果为表格格式')
  console.log('📊 输入数据详情:', {
    hasResult: !!result,
    hasAssignments: !!(result?.assignments),
    assignmentsLength: result?.assignments?.length || 0,
    firstAssignment: result?.assignments?.[0]
  })
  
  // 复用updateScheduleResults中的转换逻辑，但不更新DOM
  const newResults: any[] = []
  
  if (!result.assignments || result.assignments.length === 0) {
    console.warn('⚠️ 转换失败: assignments为空或长度为0')
    return newResults
  }
  
  console.log('✅ assignments验证通过，开始处理学员分组')
  
  // 按学员分组处理排班数据
  const studentAssignments = new Map<string, any[]>()
  
  result.assignments.forEach((assignment: any, index: number) => {
    console.log(`📝 处理第${index + 1}个assignment:`, {
      studentId: assignment.studentId,
      studentName: assignment.studentName,
      studentDepartment: assignment.studentDepartment,
      examDate: assignment.examDate,
      examType: assignment.examType,
      student: assignment.student
    })
    
    const studentKey = `${assignment.studentId || assignment.student?.id}_${assignment.studentDepartment || assignment.student?.department}`
    
    if (!studentAssignments.has(studentKey)) {
      studentAssignments.set(studentKey, [])
    }
    
    studentAssignments.get(studentKey)!.push(assignment)
  })
  
  console.log(`👥 学员分组完成，共${studentAssignments.size}个学员`)
  
  // 为每个学员创建表格行数据
  studentAssignments.forEach((assignments, studentKey) => {
    console.log(`🎯 处理学员: ${studentKey}，assignments数量: ${assignments.length}`)
    
    const firstAssignment = assignments[0]
    const studentName = firstAssignment.studentName || firstAssignment.student?.name || '未知学员'
    const studentDept = firstAssignment.studentDepartment || firstAssignment.student?.department || '未知科室'
    
    console.log(`👤 学员信息: ${studentName} (${studentDept})`)
    
    // 按日期和考试类型分组
    const examsByDate = new Map<string, Map<string, any>>()
    
    assignments.forEach(assignment => {
      const dateKey = assignment.examDate || '未定日期'
      if (!examsByDate.has(dateKey)) {
        examsByDate.set(dateKey, new Map())
      }
      examsByDate.get(dateKey)!.set(assignment.examType || '未知类型', assignment)
    })
    
    // 提取两天的考试数据
    const dates = Array.from(examsByDate.keys()).sort()
    const date1 = dates[0] || '未安排'
    const date2 = dates[1] || '未安排'
    
    console.log(`📅 考试日期: ${date1}, ${date2}`)
    
    const day1Exams = examsByDate.get(date1) || new Map()
    const day2Exams = examsByDate.get(date2) || new Map()
    
    // 获取考官ID
    const day1Examiner1Id = day1Exams.get('practical')?.examiner1 || day1Exams.get('现场')?.examiner1
    const day1Examiner2Id = day1Exams.get('practical')?.examiner2 || day1Exams.get('现场')?.examiner2
    const day1BackupId = day1Exams.get('practical')?.backupExaminer || day1Exams.get('现场')?.backupExaminer
    const day2Examiner1Id = day2Exams.get('oral')?.examiner1 || day2Exams.get('口试')?.examiner1
    const day2Examiner2Id = day2Exams.get('oral')?.examiner2 || day2Exams.get('口试')?.examiner2
    const day2BackupId = day2Exams.get('oral')?.backupExaminer || day2Exams.get('口试')?.backupExaminer
    
    // 获取考官完整信息（包括科室）
    const getTeacherById = (teacherId: string | undefined) => {
      if (!teacherId) return null
      if (typeof teacherId === 'object' && (teacherId as any).name) return teacherId as any
      return (cachedTeacherData || teacherList.value || []).find((t: any) => t.id === teacherId || t.name === teacherId)
    }
    
    // 构建表格行数据（包含考官科室信息）
    const tableRow = {
      id: firstAssignment.id || `${studentKey}_row`,
      student: studentName,
      department: studentDept,
      // 📊 学员推荐科室信息（用于精细化推荐）
      recommendedExaminer1Dept: firstAssignment.student?.recommendedExaminer1Dept || 
                               firstAssignment.recommendedExaminer1Dept,
      recommendedExaminer2Dept: firstAssignment.student?.recommendedExaminer2Dept || 
                               firstAssignment.recommendedExaminer2Dept,
      // 🗓️ 考试日期
      date1: date1 !== '未安排' ? formatDateForDisplay(date1) : '未安排',
      type1: '现场',
      date2: date2 !== '未安排' ? formatDateForDisplay(date2) : '未安排', 
      type2: '口试',
      // 👨‍🏫 考官姓名
      examiner1_1: getTeacherNameById(day1Examiner1Id) || '未分配',
      examiner1_2: getTeacherNameById(day1Examiner2Id) || '未分配',
      backup1: getTeacherNameById(day1BackupId) || '未分配',
      examiner2_1: getTeacherNameById(day2Examiner1Id) || '未分配',
      examiner2_2: getTeacherNameById(day2Examiner2Id) || '未分配',
      backup2: getTeacherNameById(day2BackupId) || '未分配',
      // 🏢 考官科室信息（用于精细化推荐的SC14约束）
      examiner1_1_dept: getTeacherById(day1Examiner1Id)?.department || '',
      examiner1_2_dept: getTeacherById(day1Examiner2Id)?.department || '',
      backup1_dept: getTeacherById(day1BackupId)?.department || '',
      examiner2_1_dept: getTeacherById(day2Examiner1Id)?.department || '',
      examiner2_2_dept: getTeacherById(day2Examiner2Id)?.department || '',
      backup2_dept: getTeacherById(day2BackupId)?.department || ''
    }
    
    console.log(`✅ 生成表格行:`, tableRow)
    newResults.push(tableRow)
  })
  
  console.log(`🎉 转换完成，生成${newResults.length}条表格记录`)
  return newResults
}


// 🔄 格式化日期显示
const formatDateForDisplay = (dateStr: string) => {
  if (!dateStr || dateStr === '未定日期') return '未安排'
  
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      // 如果是MM.DD格式，直接返回
      if (/^\d{2}\.\d{2}$/.test(dateStr)) {
        return dateStr
      }
      return '日期格式错误'
    }
    
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${month}.${day}`
  } catch (error) {
    console.warn('日期格式化失败:', dateStr, error)
    return '日期错误'
  }
}

// 🎯 获取表格状态文本
const getTableStatusText = () => {
  if (isTableUpdating.value) {
    return 'OptaPlanner增量更新中'
  } else if (isScheduling.value) {
    return 'OptaPlanner求解进行中'
  } else if (!isScheduling.value && scheduleResults.value.length > 0) {
    return '排班已完成'
  } else {
    return '准备就绪'
  }
}

// 🎯 实时进度窗口拖拽功能
const startRealtimeWindowDrag = (event: MouseEvent) => {
  realtimeWindowDragging.value = true
  
  const startX = event.clientX - realtimeWindowPosition.value.x
  const startY = event.clientY - realtimeWindowPosition.value.y
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!realtimeWindowDragging.value) return
    
    const newX = e.clientX - startX
    const newY = e.clientY - startY
    
    // 限制拖拽范围，确保窗口不会移出视图
    const maxX = window.innerWidth - 400 // 窗口宽度
    const maxY = window.innerHeight - 100 // 预留底部空间
    
    realtimeWindowPosition.value = {
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    }
  }
  
  const handleMouseUp = () => {
    realtimeWindowDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 阻止默认行为
  event.preventDefault()
}

// 🎯 重置实时进度窗口位置
const resetRealtimeWindowPosition = () => {
  realtimeWindowPosition.value = { x: 20, y: 20 }
}

// 添加实时日志
const addRealtimeLog = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  const now = new Date()
  const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  realtimeLogs.value.push({
    time: timeStr,
    message,
    type
  })
  
  // 限制日志数量，保持最新的50条
  if (realtimeLogs.value.length > 50) {
    realtimeLogs.value = realtimeLogs.value.slice(-50)
  }
  
  // 自动滚动到最新日志
  nextTick(() => {
    const logContainer = document.querySelector('.log-container')
    if (logContainer) {
      logContainer.scrollTop = logContainer.scrollHeight
    }
  })
}
const currentAssignmentCount = ref(0)
const totalStudents = ref(0)
const realTimeAssignments = ref<any[]>([])
const latestSoftScore = ref<number | null>(null)
const bestSoftScore = ref<number | null>(null)

// 响应式设计相关状态
const isMobile = ref(false)
const isTablet = ref(false)
const isDesktop = ref(false)
const screenWidth = ref(0)
const screenHeight = ref(0)
const mobileMenuOpen = ref(false)
const needsRefresh = ref(false)
const constraintViolations = ref<ConstraintViolation[]>([])
const showUnifiedResultModal = ref(false)
const unifiedResultData = ref<any>(null)
const shouldShowViolationAlert = ref(true)
const violationAlertDismissedAt = ref<number>(0)

// 学员数据预览相关状态
const showAllStudents = ref(false)

// 计算属性：显示的学员数
const displayedStudents = computed(() => {
  if (showAllStudents.value) {
    return studentList.value
  }
  return studentList.value.slice(0, 10)
})

// 计算属性：是否有推荐考官信息
const hasRecommendedExaminers = computed(() => {
  return studentList.value.some((student: any) => 
    student.recommendedExaminer1Dept || 
    student.recommendedExaminer2Dept || 
    student.recommendedBackupDept
  )
})

// 计算属性：科室分布统计
const departmentStats = computed(() => {
  const deptCount = {}
  studentList.value.forEach((student: any) => {
    const dept = student.department || '未知'
    ;(deptCount as any)[dept] = ((deptCount as any)[dept] || 0) + 1
  })
  return Object.entries(deptCount)
    .map(([dept, count]) => `${dept}(${count})`)
    .join(', ')
})

// 计算属性：班组分布统计
const groupStats = computed(() => {
  const groupCount = {}
  studentList.value.forEach((student: any) => {
    const group = student.group || '未知'
    ;(groupCount as any)[group] = ((groupCount as any)[group] || 0) + 1
  })
  return Object.entries(groupCount)
    .map(([group, count]) => `${group}(${count})`)
    .join(', ')
})

// 计算最小考试日期字符串
const minExamDateStr = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})
const fileInput = ref<HTMLInputElement | null>(null)
const uploadedFile = ref<{
  name: string
  size: number
  type: string
  file: File
} | null>(null)

// 拖拽相关数据
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const modalPosition = ref({ x: 0, y: 0 })
const modalRef = ref<HTMLElement | null>(null)

// 排班相关数据
const isScheduling = ref(false)
const schedulingProgress = ref(0)
const schedulingResult = ref<SchedulingResult | null>(null)
const schedulingError = ref('')
const solvingModeRef = ref('auto')

// 考试日期数据
const examStartDate = ref<Date | null>(null)
const examEndDate = ref<Date | null>(null)

// 最小考试日期（设置为当前日期，允许用户选择未来的任意日期范围）
const minExamDate = computed(() => {
  // 设置为当前日期，允许用户选择未来的任意日期进行排班
  const today = new Date()
  today.setHours(0, 0, 0, 0) // 重置时间为当天开始
  return today
})

// 判断是否为周末
const isWeekend = (date: Date): boolean => {
  const day = date.getDay()
  return day === 0 || day === 6 // 0是周日，6是周六
}

// 学员数据
const studentList = ref<StudentInfo[]>([])

// 考官数据（用于显示和缓存）
const teacherList = ref<TeacherInfo[]>([])

// 移除重复的约束条件定义，使用前面已定义的constraints

// 移除重复的约束权重配置定义，使用前面已定义的constraintWeights

// 切换约束条件
const toggleConstraint = (key: string) => {
  (constraints.value as any)[key] = !(constraints.value as any)[key]
}

// 处理约束配置应用
const handleConstraintConfigApply = async (config: { constraints: Record<string, any>, weights: Record<string, number> }) => {
  console.log('🎯 应用新的约束配置:', config)
  
  try {
    // 导入约束配置服务
    const { updateConstraintConfig } = await import('../services/constraintConfigService')
    
    // 构建完整的约束配置
    const fullConfig = {
      hardConstraints: {
        workdaysOnlyExam: true,
        examinerDepartmentRules: true,
        twoMainExaminersRequired: true,
        noDayShiftExaminer: true
      },
      softConstraints: {
        backupExaminerDiffDept: config.constraints.backupExaminerDiffDept ?? true,
        avoidStudentDayShift: config.constraints.avoidStudentDayShift ?? true,
        preferRecommendedDepts: config.constraints.preferRecommendedDepts ?? true,
        allowDept37CrossUse: config.constraints.allowDept37CrossUse ?? true,
        ensureConsecutiveDays: config.constraints.ensureConsecutiveDays ?? true,
        preferNoGroupTeachers: config.constraints.preferNoGroupTeachers ?? false,
        balanceWorkload: config.constraints.balanceWorkload ?? true,
        preferLaterDates: config.constraints.preferLaterDates ?? false,
        nightShiftTeacherPriority: config.constraints.preferNightShiftTeachers ?? true,
        firstRestDayTeacherPriority: config.constraints.preferFirstRestDayTeachers ?? true,
        secondRestDayTeacherPriority: config.constraints.preferSecondRestDayTeachers ?? false,
        adminTeacherPriority: config.constraints.adminTeacherPriority ?? false,
        nightShiftTeacherRecommendedDepartmentBonus: config.constraints.nightShiftTeacherRecommendedDepartmentBonus ?? true
      },
      weights: {
        backupExaminerDiffDept: config.weights.backupExaminerDiffDept ?? 60,
        avoidStudentDayShift: config.weights.avoidStudentDayShift ?? 40,
        preferRecommendedDepts: config.weights.preferRecommendedDepts ?? 80,
        allowDept37CrossUse: config.weights.allowDept37CrossUse ?? 30,
        ensureConsecutiveDays: config.weights.ensureConsecutiveDays ?? 70,
        preferNoGroupTeachers: config.weights.preferNoGroupTeachers ?? 50,
        balanceWorkload: config.weights.balanceWorkload ?? 60,
        preferLaterDates: config.weights.preferLaterDates ?? 20,
        nightShiftTeacherPriority: config.weights.preferNightShiftTeachers ?? 90,
        firstRestDayTeacherPriority: config.weights.preferFirstRestDayTeachers ?? 70,
        secondRestDayTeacherPriority: config.weights.preferSecondRestDayTeachers ?? 40,
        adminTeacherPriority: config.weights.adminTeacherPriority ?? 30,
        nightShiftTeacherRecommendedDepartmentBonus: config.weights.nightShiftTeacherRecommendedDepartmentBonus ?? 50
      }
    } as any
    
    // 同步到后端
    const success = await updateConstraintConfig(fullConfig);
    
    if (success) {
      // 更新本地状态
      constraints.value = { ...constraints.value, ...config.constraints }
      constraintWeights.value = { ...constraintWeights.value, ...config.weights }
      
      // 显示成功提示
      const successMsg = document.createElement('div')
      successMsg.textContent = '约束配置已同步到后端'
      successMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 12px 20px; border-radius: 6px; z-index: 9999; font-weight: 500;'
      document.body.appendChild(successMsg)
      setTimeout(() => document.body.removeChild(successMsg), 3000)
    } else {
      // 显示失败提示
      const errorMsg = document.createElement('div')
      errorMsg.textContent = '约束配置同步失败，请检查网络连接'
      errorMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 12px 20px; border-radius: 6px; z-index: 9999; font-weight: 500;'
      document.body.appendChild(errorMsg)
      setTimeout(() => document.body.removeChild(errorMsg), 5000)
    }
  } catch (error) {
    console.error('约束配置同步失败:', error)
    
    // 至少更新本地状态
    constraints.value = { ...constraints.value, ...config.constraints }
    constraintWeights.value = { ...constraintWeights.value, ...config.weights }
    
    // 显示警告提示
    const warningMsg = document.createElement('div')
    warningMsg.textContent = '⚠️ 约束配置已本地更新，但后端同步失败'
    warningMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #f59e0b; color: white; padding: 12px 20px; border-radius: 6px; z-index: 9999; font-weight: 500;'
    document.body.appendChild(warningMsg)
    setTimeout(() => document.body.removeChild(warningMsg), 5000)
  }
}

// 移除硬约束和软约束数组
// 排班结果类型定义
interface ScheduleResultRow {
  id: number
  department: string
  student: string
  date1: string
  type1: string
  examiner1_1: string
  examiner1_2: string
  backup1: string
  date2: string
  type2: string
  examiner2_1: string
  examiner2_2: string
  backup2: string
}

// 排班结果
  // 排班结果数据 - 动态加载，不再使用硬编码示例数据
const scheduleResults = ref<ScheduleResultRow[]>([])

// 方法：toggleSidebar已移至响应式设计部分
const closeModal = () => {
  showCreateModal.value = false
  // 重置弹窗位置
  modalPosition.value = { x: 0, y: 0 }
}

// 拖拽相关方法
const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  const rect = modalRef.value?.getBoundingClientRect()
  if (rect) {
    dragOffset.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
  }
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const newX = event.clientX - dragOffset.value.x
  const newY = event.clientY - dragOffset.value.y
  
  // 限制拖拽范围，确保弹窗不会完全移出视图
  const maxX = window.innerWidth - 300 // 最小保持300px可见
  const maxY = window.innerHeight - 100 // 最小保持100px可见
  
  modalPosition.value = {
    x: Math.max(-200, Math.min(newX, maxX)), // 允许部分移出左边
    y: Math.max(0, Math.min(newY, maxY))
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    // 保存上传的文件信息
    uploadedFile.value = {
      name: file.name,
      size: file.size,
      type: file.type,
      file: file
    }
    console.log('文件已上传:', file.name)
    
    // 自动解析文件并更新学员数量
    try {
      if (file.type.includes('csv')) {
        await parseCSVFile(file)
      } else if (file.type.includes('excel') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        await parseExcelFile(file)
      } else {
        console.warn('不支持的文件格式，使用示例数据')
      }
    } catch (error) {
      console.error('文件解析失败:', error)
      alert('文件解析失败，请检查文件格式')
    }
  }
}

// 预览文件
const showPreviewModal = ref(false)
const previewData = ref<any[]>([])
const previewHeaders = ref<string[]>([])

const previewFile = () => {
  if (uploadedFile.value) {
    console.log('预览文件:', uploadedFile.value.name)
    const file = uploadedFile.value.file
    
    if (file.type.includes('csv')) {
      previewCSVFile(file)
    } else if (file.type.includes('excel') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
      previewExcelFile(file)
    } else {
      alert('不支持的文件格式，请上传Excel或CSV文件')
    }
  }
}

// 解析CSV文件并更新学员数
const parseCSVFile = async (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string
        const lines = text.split('\n').filter(line => line.trim())
        
        if (lines.length < 2) {
          reject(new Error('文件内容不足，至少需要标题行和一行数据'))
          return
        }
        
        const headers = lines[0].split(',').map(h => h.trim())
        const students: StudentInfo[] = []
        
        console.log('CSV标题:', headers)
        
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',').map(v => v.trim())
          console.log(`解析第${i}行数据`, values)
          
          if (values.length >= 2) {
            const student = {
             id: i.toString(),
             name: values[0] || `学员${i}`,
             department: values[1] || '一', // 保持简写格式用于算法匹配
             group: values[2] || '一组',
             // 支持推荐考官字段（第4列）
             recommendedExaminer1Dept: values[3] || undefined,
             recommendedExaminer2Dept: values[4] || undefined,
             recommendedBackupDept: values[5] || undefined
           }
           
           console.log(`学员${student.name}解析结果:`, {
             科室: student.department,
             班组: student.group,
             推荐考官1科室: student.recommendedExaminer1Dept,
             推荐考官2科室: student.recommendedExaminer2Dept,
             推荐备份考官科室: student.recommendedBackupDept
           })
           
           students.push(student)
          }
        }
        
        if (students.length > 0) {
          studentList.value = students
          console.log('CSV文件解析成功，学员数:', students.length)
        }
        
        resolve(students)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'UTF-8')
  })
}

  // 解析Excel文件并更新学员数
const parseExcelFile = async (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        // 动态导入xlsx
        const XLSX = await import('xlsx')
        
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        
        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        
        // 将工作表转换为JSON数组
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        if (jsonData.length < 2) {
          reject(new Error('Excel文件内容不足，至少需要标题行和一行数据'))
          return
        }
        
        const students: StudentInfo[] = []
        const headers = (jsonData as any[][])[0] as string[]
        
        // 查找关键列的索引 - 支持多种格式
        console.log('📋 Excel表头:', headers)
        
        // 姓名列：支持"姓名"学员"名字"
        const nameIndex = headers.findIndex(h => h && (
          h.includes('姓名') || h.includes('学员') || h.includes('名字') || 
          h === '姓名' || h === '学员' || h === '名字'
        ))
        
        // 科室列：支持"科室"部门"或直接是科室名称
        let deptIndex = headers.findIndex(h => h && (
          h.includes('科室') || h.includes('部门') || h.includes('区域')
        ))
        
        // 如果没找到科室列，尝试查找包含"区域"的列
        if (deptIndex === -1) {
          deptIndex = headers.findIndex(h => h && (
            h.includes('区域') || h.includes('一室') || h.includes('二室') || 
            h.includes('三室') || h.includes('四室') || h.includes('五室') ||
            h.includes('六室') || h.includes('七室')
          ))
        }
        
        // 班组列：支持"班组"组别"班组"班次"
        const groupIndex = headers.findIndex(h => h && (
          h.includes('班组') || h.includes('组别') || h.includes('班组') || h.includes('班次') ||
          h === '班组' || h === '组别' || h === '班组' || h === '班次'
        ))
        
        // 推荐考官科室列：支持"考官一"考官1第一考官"备份考官"
        const examiner1DeptIndex = headers.findIndex(h => h && (
          h.includes('考官一') || h.includes('考官1') || h.includes('第一考官') ||
          h === '考官一' || h === '考官1' || h === '第一考官'
        ))
        
        const examiner2DeptIndex = headers.findIndex(h => h && (
          h.includes('考官二') || h.includes('考官2') || h.includes('第二考官') ||
          h === '考官二' || h === '考官2' || h === '第二考官'
        ))
        
        const backupDeptIndex = headers.findIndex(h => h && (
          h.includes('备份考官') || h.includes('备份') || h.includes('候补考官') ||
          h === '备份考官' || h === '备份' || h === '候补考官'
        ))
        
        console.log(`📍 列索引 姓名=${nameIndex}, 科室=${deptIndex}, 班组=${groupIndex}`)  
        console.log(`📍 推荐考官列索引 考官一=${examiner1DeptIndex}, 考官2=${examiner2DeptIndex}, 备份=${backupDeptIndex}`)
        console.log(`📋 Excel表头信息:`, headers)
        console.log(`🔍 班组列查找结果 索引=${groupIndex}, 表头="${headers[groupIndex] || '未找到'}"`)
        
        for (let i = 1; i < (jsonData as any[][]).length; i++) {
          const row = (jsonData as any[][])[i]
          if (row && row.length > 0 && row[nameIndex]) {
            const studentName = row[nameIndex] || `学员${i}`
            let studentDept = row[deptIndex] || '一'
            let studentGroup = groupIndex >= 0 ? (row[groupIndex] || '') : ''
            
            // 智能处理科室数据
            if (studentDept) {
              studentDept = studentDept.toString().trim()
              // 如果科室数据包含完整科室名称，进行标准化处理
              if (studentDept.includes('区域一室') || studentDept === '区域一室') {
                studentDept = '一'
              } else if (studentDept.includes('区域二室') || studentDept === '区域二室') {
                studentDept = '二'
              } else if (studentDept.includes('区域三室') || studentDept === '区域三室') {
                studentDept = '三'
              } else if (studentDept.includes('区域四室') || studentDept === '区域四室') {
                studentDept = '四'
              } else if (studentDept.includes('区域五室') || studentDept === '区域五室') {
                studentDept = '五'
              } else if (studentDept.includes('区域六室') || studentDept === '区域六室') {
                studentDept = '六'
              } else if (studentDept.includes('区域七室') || studentDept === '区域七室') {
                studentDept = '七'
              }
              // 如果是数字，保持原样
              else if (/^[一二三四五六七]$/.test(studentDept)) {
                // 已经是标准格式，保持不变
              }
              // 如果是阿拉伯数字，转换为中文数字
              else if (/^[1-7]$/.test(studentDept)) {
                const numMap = {'1': '一', '2': '二', '3': '三', '4': '四', '5': '五', '6': '六', '7': '七'}
                studentDept = numMap[studentDept as keyof typeof numMap] || studentDept
              }
            }
            
            // 智能处理班组数据
            if (studentGroup) {
              studentGroup = studentGroup.toString().trim()
              // 标准化班组格式
              if (/^[一二三四]组$/.test(studentGroup)) {
                // 已经是标准格式
              } else if (/^[1-4]组$/.test(studentGroup)) {
                // 阿拉伯数字转中文
                const numMap = {'1': '一', '2': '二', '3': '三', '4': '四'}
                studentGroup = studentGroup.replace(/^[1-4]/, (match: string) => numMap[match as keyof typeof numMap])
              } else if (/^[一二三四]组$/.test(studentGroup)) {
                // 只有数字，添加组
                studentGroup += '组'
              } else if (/^[1-4]组$/.test(studentGroup)) {
                // 阿拉伯数字，转换并添加组
                const numMap = {'1': '一', '2': '二', '3': '三', '4': '四'}
                studentGroup = numMap[studentGroup as keyof typeof numMap] + '组'
              }
            } else {
              // 如果班组数据为空，设置为默认组
              studentGroup = '一组'
            }
            
            console.log(`📝 解析学员数据: ${studentName}, 科室: "${studentDept}", 班组: "${studentGroup}"`)
            console.log(`📍 班组索引: ${groupIndex}, 原始班组数据: "${row[groupIndex]}", 数据类型: ${typeof row[groupIndex]}, 处理后班组: "${studentGroup}"`)
            console.log(`🔍 完整行数据:`, row)
            
            // 特别检查唐志骏的数据
            if (studentName === '唐志骏') {
              console.log(`🔍 特别检查唐志骏数据:`)
              console.log(`  - 原始科室数据: "${row[deptIndex]}"`)
              console.log(`  - 处理后科室: "${studentDept}"`)
              console.log(`  - 原始班组数据: "${row[groupIndex]}"`)
              console.log(`  - 处理后班组: "${studentGroup}"`)
              console.log(`  - 班组类型: ${typeof studentGroup}`)
            }
            
            // 提取推荐考官科室信息
            let recommendedExaminer1Dept = examiner1DeptIndex >= 0 ? (row[examiner1DeptIndex] || '').toString().trim() : undefined
            let recommendedExaminer2Dept = examiner2DeptIndex >= 0 ? (row[examiner2DeptIndex] || '').toString().trim() : undefined
            let recommendedBackupDept = backupDeptIndex >= 0 ? (row[backupDeptIndex] || '').toString().trim() : undefined
            
            // 🔧 如果没有找到分列的推荐考官数据，尝试从单列中解析
            if (!recommendedExaminer1Dept && !recommendedExaminer2Dept) {
              // 查找可能包含推荐考官信息的列
              const recommendationIndex = headers.findIndex(h => h && (
                h.includes('推荐考官') || h.includes('推荐科室') || h.includes('考官安排') ||
                h === '推荐考官' || h === '推荐科室' || h === '考官安排'
              ))
              
              if (recommendationIndex >= 0) {
                const recommendationText = (row[recommendationIndex] || '').toString().trim()
                console.log(`📝 发现推荐考官合并数据: "${recommendationText}"`)
                
                // 解析格式：考官一：区域三室，考官二：区域七室
                const pattern1 = /考官一[：:]\s*([^，,]+)[，,]?\s*考官二[：:]\s*([^，,]+)/
                const pattern2 = /考官1[：:]\s*([^，,]+)[，,]?\s*考官2[：:]\s*([^，,]+)/
                
                let match = recommendationText.match(pattern1) || recommendationText.match(pattern2)
                if (match) {
                  recommendedExaminer1Dept = match[1].trim()
                  recommendedExaminer2Dept = match[2].trim()
                  console.log(`✅ 解析成功: 考官一=${recommendedExaminer1Dept}, 考官二=${recommendedExaminer2Dept}`)
                } else {
                  console.log(`⚠️ 无法解析推荐考官格式: "${recommendationText}"`)
                }
              }
            }
            
            console.log(`📝 推荐考官科室: 考官一=${recommendedExaminer1Dept}, 考官2=${recommendedExaminer2Dept}, 备份=${recommendedBackupDept}`)
            
            // 特别调试顾杨的数据
            if (studentName === '顾杨') {
              console.log(`🔍 顾杨数据详细信息:`)
              console.log(`  - 考官一列索引: ${examiner1DeptIndex}, 原始数据: "${row[examiner1DeptIndex]}"`)
              console.log(`  - 考官二列索引: ${examiner2DeptIndex}, 原始数据: "${row[examiner2DeptIndex]}"`) 
              console.log(`  - 备份考官列索引: ${backupDeptIndex}, 原始数据: "${row[backupDeptIndex]}"`)
              console.log(`  - 完整行数据:`, row)
            }
            
            students.push({
             id: i.toString(), // 确保ID是字符串
             name: studentName,
             department: studentDept, // 已经处理过的科室数据
             group: studentGroup, // 已经处理过的班组数据
             // 支持推荐考官字段
             recommendedExaminer1Dept: recommendedExaminer1Dept || undefined,
             recommendedExaminer2Dept: recommendedExaminer2Dept || undefined,
             recommendedBackupDept: recommendedBackupDept || undefined
           })
          }
        }
        
        if (students.length > 0) {
          studentList.value = students
          console.log('Excel文件解析成功，学员数:', students.length)
        }
        
        resolve(students)
      } catch (error) {
        console.error('Excel文件解析失败:', error)
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

// 预览CSV文件
const previewCSVFile = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const text = e.target?.result as string
    const lines = text.split('\n').filter(line => line.trim())
    if (lines.length > 0) {
      previewHeaders.value = lines[0].split(',').map(header => header.trim())
      previewData.value = lines.slice(1, 11).map(line => {
        const values = line.split(',').map(value => value.trim())
        const row = {}
        previewHeaders.value.forEach((header, index) => {
          (row as any)[header] = values[index] || ''
        })
        return row
      })
      showPreviewModal.value = true
    }
  }
  reader.readAsText(file, 'UTF-8')
}

// 预览Excel文件（使用xlsx库正确解析）
const previewExcelFile = async (file: File) => {
  const reader = new FileReader()
  reader.onload = async (e) => {
    try {
      // 动态导入xlsx
      const XLSX = await import('xlsx')
      
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })
      
      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      
      // 将工作表转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      if (jsonData.length > 0) {
        // 设置表头
        previewHeaders.value = (jsonData as any[][])[0].map(header => header || '未命名列')
        
          // 设置预览数据（最多显示10行）
        previewData.value = jsonData.slice(1, 11).map((row: any) => {
          const rowData: any = {}
          previewHeaders.value.forEach((header, index) => {
            rowData[header] = row[index] || ''
          })
          return rowData
        })
      } else {
        previewHeaders.value = ['提示']
        previewData.value = [{ '提示': 'Excel文件为空或无数据' }]
      }
      
      showPreviewModal.value = true
      console.log('Excel文件预览成功:', file.name)
    } catch (error) {
      console.error('Excel文件预览失败:', error)
      previewHeaders.value = ['错误']
      previewData.value = [{ '错误': `Excel文件解析失败: ${(error as Error).message || '未知错误'}` }]
      showPreviewModal.value = true
    }
  }
  reader.onerror = () => {
    previewHeaders.value = ['错误']
    previewData.value = [{ '错误': '文件读取失败' }]
    showPreviewModal.value = true
  }
  reader.readAsArrayBuffer(file)
  console.log('正在预览Excel文件:', file.name)
}

// 关闭预览弹窗
const closePreviewModal = () => {
  showPreviewModal.value = false
  previewData.value = []
  previewHeaders.value = []
}

// 删除文件
const deleteFile = () => {
  if (uploadedFile.value) {
    console.log('删除文件:', uploadedFile.value.name)
    // 清空文件
    uploadedFile.value = null
  }
}

// 
// 步骤导航方法
const nextStep = () => {
  console.log('🔄 nextStep 被调用')
  console.log('当前步骤:', currentStep.value)
  const canProceed = canProceedToNextStep()
  console.log('canProceedToNextStep:', canProceed)
  
  if (canProceed) {
    currentStep.value++
    console.log('新步骤已更新:', currentStep.value)
  } else {
    console.log('无法进入下一步，条件不满足')
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 检查是否可以进入下一步
// canProceedToNextStep函数在后面定义
// 日期相关状态
const dateRangeSuggestion = ref('')
const suggestedDateRange = ref<{start: string, end: string} | null>(null)

// 日期相关计算方法
const calculateExamDays = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return 0
  
  const startDate = new Date(examStartDateStr.value)
  const endDate = new Date(examEndDateStr.value)
  
  if (startDate > endDate) return 0
  
  // 使用与generateExamDateRange相同的逻辑计算工作日
  const examDates = generateExamDateRange(startDate, endDate)
  return examDates.length
}

const calculateWorkdays = () => {
  return calculateExamDays() // 现在工作日就是考试日
}

const calculateWeekends = () => {
  return calculateExamDays() - calculateWorkdays()
}

const hasWeekends = () => {
  return calculateWeekends() > 0
}

// 新增的日期计算方法
const getTotalDays = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return 0
  
  const startDate = new Date(examStartDateStr.value)
  const endDate = new Date(examEndDateStr.value)
  
  if (startDate > endDate) return 0
  
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
}

const getWeekendDays = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return 0
  
  const startDate = new Date(examStartDateStr.value)
  const endDate = new Date(examEndDateStr.value)
  
  let weekendCount = 0
  const current = new Date(startDate)
  
  while (current <= endDate) {
    const dayOfWeek = current.getDay()
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      weekendCount++
    }
    current.setDate(current.getDate() + 1)
  }
  
  return weekendCount
}

const getHolidayDays = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return 0
  
  const startDate = new Date(examStartDateStr.value)
  const endDate = new Date(examEndDateStr.value)
  let holidayCount = 0
  const current = new Date(startDate)
  
  while (current <= endDate) {
    const dateStr = current.toISOString().split('T')[0]
    if (holidayService.isHoliday(dateStr)) {
      holidayCount++
    }
    current.setDate(current.getDate() + 1)
  }
  
  return holidayCount
}

const getAvailableDates = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return []
  
  const startDate = new Date(examStartDateStr.value)
  const endDate = new Date(examEndDateStr.value)
  
  return generateExamDateRange(startDate, endDate)
}

const formatDateDisplay = (dateStr: string) => {
  const date = new Date(dateStr)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekday = weekdays[date.getDay()]
  
  return `${month}/${day} ${weekday}`
}

const getDateTypeClass = (dateStr: string) => {
  const date = new Date(dateStr)
  const dayOfWeek = date.getDay()
  
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return 'weekend'
  }
  return 'workday'
}

// 快速日期选择方法
const setQuickDateRange = (days: number) => {
  const today = new Date()
  const startDate = new Date(today)
  startDate.setDate(today.getDate() + 1) // 从明天开始
  
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + days - 1)
  
  examStartDateStr.value = startDate.toISOString().split('T')[0]
  examEndDateStr.value = endDate.toISOString().split('T')[0]
  
  updateDateSuggestion()
}

const isQuickDateActive = (days: number) => {
  if (!examStartDateStr.value || !examEndDateStr.value) return false
  
  const today = new Date()
  const expectedStart = new Date(today)
  expectedStart.setDate(today.getDate() + 1)
  
  const expectedEnd = new Date(expectedStart)
  expectedEnd.setDate(expectedStart.getDate() + days - 1)
  
  return examStartDateStr.value === expectedStart.toISOString().split('T')[0] &&
         examEndDateStr.value === expectedEnd.toISOString().split('T')[0]
}

const setThisMonth = () => {
  const today = new Date()
  const startDate = new Date(today)
  startDate.setDate(today.getDate() + 1)
  
  const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0) // 本月最后一天
  
  examStartDateStr.value = startDate.toISOString().split('T')[0]
  examEndDateStr.value = endDate.toISOString().split('T')[0]
  
  updateDateSuggestion()
}

const isThisMonthActive = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return false
  
  const today = new Date()
  const expectedStart = new Date(today)
  expectedStart.setDate(today.getDate() + 1)
  
  const expectedEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0)
  
  return examStartDateStr.value === expectedStart.toISOString().split('T')[0] &&
         examEndDateStr.value === expectedEnd.toISOString().split('T')[0]
}

const setNextMonth = () => {
  const today = new Date()
  const nextMonth = today.getMonth() + 1
  const year = nextMonth > 11 ? today.getFullYear() + 1 : today.getFullYear()
  const month = nextMonth > 11 ? 0 : nextMonth
  
  const startDate = new Date(year, month, 1)
  const endDate = new Date(year, month + 1, 0)
  
  examStartDateStr.value = startDate.toISOString().split('T')[0]
  examEndDateStr.value = endDate.toISOString().split('T')[0]
  
  updateDateSuggestion()
}

const isNextMonthActive = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) return false
  
  const today = new Date()
  const nextMonth = today.getMonth() + 1
  const year = nextMonth > 11 ? today.getFullYear() + 1 : today.getFullYear()
  const month = nextMonth > 11 ? 0 : nextMonth
  
  const expectedStart = new Date(year, month, 1)
  const expectedEnd = new Date(year, month + 1, 0)
  
  return examStartDateStr.value === expectedStart.toISOString().split('T')[0] &&
         examEndDateStr.value === expectedEnd.toISOString().split('T')[0]
}

// 日期变化处理
const onStartDateChange = () => {
  updateDateSuggestion()
}

const onEndDateChange = () => {
  updateDateSuggestion()
}

// 智能建议相关方法 - 暂时注释，待排班功能稳定后重新启用
/*
const updateDateSuggestion = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) {
    dateRangeSuggestion.value = ''
    suggestedDateRange.value = null
    return
  }

  const workdays = calculateWorkdays()
  const studentCount = studentList.value.length
  const requiredExams = studentCount * 2
  const averagePerDay = workdays > 0 ? Math.ceil(requiredExams / workdays) : 0

  // 简化的日期建议逻辑
  if (workdays < 2) {
    dateRangeSuggestion.value = '建议选择至少包含2个工作日的日期范围，以确保有足够时间完成所有考试。'
    const today = new Date()
    const suggestedStart = new Date(today)
    suggestedStart.setDate(today.getDate() + 1)
    const suggestedEnd = new Date(suggestedStart)
    suggestedEnd.setDate(suggestedStart.getDate() + 6) // 一周

    suggestedDateRange.value = {
      start: suggestedStart.toISOString().split('T')[0],
      end: suggestedEnd.toISOString().split('T')[0]
    }
  } else if (averagePerDay > 15) {
    // 平均每天超过15场考试，建议延长日期范围
    dateRangeSuggestion.value = `当前平均每日安排${averagePerDay}场考试，负荷较重。建议延长日期范围以降低每日工作强度。`
    const today = new Date()
    const suggestedStart = new Date(today)
    suggestedStart.setDate(today.getDate() + 1)
    const suggestedEnd = new Date(suggestedStart)
    // 建议将每日场次控制在10场以内
    const recommendedDays = Math.ceil(requiredExams / 10)
    suggestedEnd.setDate(suggestedStart.getDate() + recommendedDays + 3)

    suggestedDateRange.value = {
      start: suggestedStart.toISOString().split('T')[0],
      end: suggestedEnd.toISOString().split('T')[0]
    }
  } else if (averagePerDay < 3 && workdays > 10) {
    // 平均每天少于3场考试且工作日超过10天，建议缩短日期范围
    dateRangeSuggestion.value = `当前平均每日仅安排${averagePerDay}场考试，日期范围较长。可以适当缩短日期范围提高效率。`
    const today = new Date()
    const suggestedStart = new Date(today)
    suggestedStart.setDate(today.getDate() + 1)
    const suggestedEnd = new Date(suggestedStart)
    const recommendedDays = Math.ceil(requiredExams / 6) // 建议每日6场左右
    suggestedEnd.setDate(suggestedStart.getDate() + recommendedDays + 1)

    suggestedDateRange.value = {
      start: suggestedStart.toISOString().split('T')[0],
      end: suggestedEnd.toISOString().split('T')[0]
    }
  } else {
    // 日期范围合理，不需要建议
    dateRangeSuggestion.value = ''
    suggestedDateRange.value = null
  }
}

const applySuggestion = () => {
  if (suggestedDateRange.value) {
    examStartDateStr.value = suggestedDateRange.value.start
    examEndDateStr.value = suggestedDateRange.value.end
    dateRangeSuggestion.value = ''
    suggestedDateRange.value = null
  }
}
*/

// 简化版的更新函数，不提供建议
const updateDateSuggestion = () => {
  // 暂时不提供智能建议，仅清空相关状态
  // dateRangeSuggestion.value = ''
  // suggestedDateRange.value = null
}

const applySuggestion = () => {
  // 暂时禁用建议应用功能
}

// 基于约束条件的理论容量计算
// 暂时注释约束条件容量计算，待排班功能稳定后重新启用
/*
const calculateConstraintBasedCapacity = () => {
  const workdays = calculateWorkdays()
  if (workdays === 0) return { maxExamsPerDay: 0, bottleneck: '无可用工作日', details: {} }

  // 获取考官数据进行容量分析
  let teacherCount = 0
  let departmentStats: Record<string, number> = {}

  try {
    // 尝试从缓存获取考官数据
    if (cachedTeacherData && Array.isArray(cachedTeacherData)) {
      teacherCount = cachedTeacherData.length
      cachedTeacherData.forEach(teacher => {
        if (teacher.department) {
          departmentStats[teacher.department] = (departmentStats[teacher.department] || 0) + 1
        }
      })
    } else {
      // 从localStorage获取考官数据进行估算
      const teacherKeys = ['teachers', 'examiner_teachers', 'unified_teachers', 'teacher_data', 'teacherList']
      for (const key of teacherKeys) {
        try {
          const data = localStorage.getItem(key)
          if (data) {
            const parsed = JSON.parse(data)
            if (Array.isArray(parsed) && parsed.length > 0) {
              teacherCount = parsed.length
              parsed.forEach((teacher: any) => {
                if (teacher.department) {
                  departmentStats[teacher.department] = (departmentStats[teacher.department] || 0) + 1
                }
              })
              break
            }
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
  } catch (error) {
    console.warn('获取考官数据用于容量计算时出错:', error)
  }

  const departmentCount = Object.keys(departmentStats).length
  const details = {
    teacherCount,
    departmentCount,
    departmentStats,
    workdays
  }

  // 约束条件分析
  let maxExamsPerDay = Infinity
  let bottleneck = ''

  // HC4约束：每名考官每天只能监考一名考生
  if (teacherCount > 0) {
    const hc4Limit = teacherCount // 每个考官每天最多1场
    if (hc4Limit < maxExamsPerDay) {
      maxExamsPerDay = hc4Limit
      bottleneck = `HC4约束：考官数量限制（${teacherCount}名考官）`
    }
  }

  // HC7约束：每场考试需要2名不同科室的考官
  if (teacherCount > 0) {
    const hc7Limit = Math.floor(teacherCount / 2) // 每场考试需要2个考官
    if (hc7Limit < maxExamsPerDay) {
      maxExamsPerDay = hc7Limit
      bottleneck = `HC7约束：每场考试需要2名考官（${teacherCount}名考官最多${hc7Limit}场）`
    }
  }

  // HC2+HC7约束：科室匹配限制
  if (departmentCount > 0) {
    // 简化的科室匹配计算：假设科室间可以相互配对
    const crossDeptCombinations = departmentCount >= 2 ?
      Math.floor(teacherCount / 2) : // 有多个科室时，主要受考官总数限制
      0 // 只有一个科室时，无法满足不同科室要求

    if (crossDeptCombinations < maxExamsPerDay) {
      maxExamsPerDay = crossDeptCombinations
      bottleneck = departmentCount < 2 ?
        `HC2+HC7约束：科室数量不足（仅${departmentCount}个科室）` :
        `HC2+HC7约束：科室匹配限制（${departmentCount}个科室，最多${crossDeptCombinations}场）`
    }
  }

  // 如果没有考官数据，使用保守估算
  if (teacherCount === 0) {
    maxExamsPerDay = 5 // 保守估算
    bottleneck = '考官数据未加载，使用保守估算'
  }
  
  return {
    maxExamsPerDay: maxExamsPerDay === Infinity ? 10 : maxExamsPerDay,
    bottleneck,
    details
  }
}
*/

// 容量评估方法（增强版）- 暂时注释，待排班功能稳定后重新启用
/*
const getAverageExamsPerDay = () => {
  const workdays = calculateWorkdays()
  const requiredExams = studentList.value.length * 2
  return workdays > 0 ? Math.ceil(requiredExams / workdays) : 0
}

const getTheoreticalMaxExamsPerDay = () => {
  const capacity = calculateConstraintBasedCapacity()
  return capacity.maxExamsPerDay
}

const getCapacityUtilization = () => {
  const average = getAverageExamsPerDay()
  const theoretical = getTheoreticalMaxExamsPerDay()
  return theoretical > 0 ? Math.round((average / theoretical) * 100) : 0
}

const getCapacityStatusClass = () => {
  const average = getAverageExamsPerDay()
  const theoretical = getTheoreticalMaxExamsPerDay()
  const utilization = getCapacityUtilization()
  
  // 如果超过理论上限，标记为危险
  if (average > theoretical) return 'danger'
  // 如果利用率超过90%，标记为警告
  if (utilization > 90) return 'warning'
  // 如果利用率低于30%，标记为信息
  if (utilization < 30) return 'info'
  return 'success'
}

const getCapacityStatusText = () => {
  const average = getAverageExamsPerDay()
  const theoretical = getTheoreticalMaxExamsPerDay()
  const utilization = getCapacityUtilization()
  const capacity = calculateConstraintBasedCapacity()
  
  if (average > theoretical) {
    return `⚠️ 超出理论容量上限（${theoretical}场/天），${capacity.bottleneck}`
  }
  
  if (utilization > 90) {
    return `⚠️ 容量利用率过高（${utilization}%），接近理论上限${theoretical}场/天`
  }
  
  if (utilization < 30) {
    return `ℹ️ 容量利用率较低（${utilization}%），可考虑缩短日期范围`
  }
  
  return `✅ 容量利用率合理（${utilization}%），理论上限${theoretical}场/天`
}
*/

// 增强的日期统计函数
const getDateRangeStatistics = () => {
  if (!examStartDateStr.value || !examEndDateStr.value) {
    return {
      totalDays: 0,
      workdays: 0,
      weekends: 0,
      holidays: 0,
      adjustedWorkdays: 0,
      isValidRange: false
    }
  }
  
  const startDate = new Date(examStartDateStr.value)
  const endDate = new Date(examEndDateStr.value)
  
  if (startDate > endDate) {
    return {
      totalDays: 0,
      workdays: 0,
      weekends: 0,
      holidays: 0,
      adjustedWorkdays: 0,
      isValidRange: false
    }
  }
  
  // 使用与generateExamDateRange相同的逻辑
  const examDates = generateExamDateRange(startDate, endDate)
  const workdays = examDates.length
  
  // 计算总天数、周末天数和节假日天数
  let totalDays = 0
  let weekends = 0
  let holidays = 0
  const current = new Date(startDate)
  
  while (current <= endDate) {
    totalDays++
    const dateStr = current.toISOString().split('T')[0]
    const dayOfWeek = current.getDay()
    
    // 检查是否为节假日
    if (holidayService.isHoliday(dateStr)) {
      holidays++
    } else if (dayOfWeek === 0 || dayOfWeek === 6) { // 周日或周六（非节假日）
      weekends++
    }
    current.setDate(current.getDate() + 1)
  }
  
  return {
    totalDays,
    workdays,
    normalWorkdays: workdays,
    weekends,
    holidays, // 实际节假日数量
    adjustedWorkdays: 0, // 调休工作日已包含在workdays中
    isValidRange: workdays >= 2 // 至少需要2个工作日
  }
}

// 计算活跃软约束数量
const getActiveSoftConstraintsCount = () => {
  const constraintsObj = constraints.value as any
  const softConstraintKeys = [
    'nightShiftTeacherPriority',
    'examiner2ProfessionalMatch', 
    'firstRestDayTeacherPriority',
    'backupExaminerProfessionalMatch',
    'secondRestDayTeacherPriority',
    'examiner2AlternativeOption',
    'adminTeacherPriority',
    'backupExaminerAlternativeOption',
    'allowDept37CrossUse',
    'balanceWorkload',
    'preferLaterDates'
  ]
  
  return softConstraintKeys.filter(key => constraintsObj[key]).length
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 开始排班方法
const startScheduling = async () => {
  // 同步日期数据
  console.log('🔍 日期同步调试:')
  console.log('examStartDateStr.value:', examStartDateStr.value)
  console.log('examEndDateStr.value:', examEndDateStr.value)
  
  if (examStartDateStr.value) {
    examStartDate.value = new Date(examStartDateStr.value)
    console.log('✅ 开始日期同步成功:', examStartDate.value)
  } else {
    console.warn('⚠️ examStartDateStr为空')
  }
  
  if (examEndDateStr.value) {
    examEndDate.value = new Date(examEndDateStr.value)
    console.log('✅ 结束日期同步成功:', examEndDate.value)
  } else {
    console.warn('⚠️ examEndDateStr为空')
  }
  
  // 验证日期数据
  if (!examStartDate.value || !examEndDate.value) {
    // 尝试从字符串重新解析日期
    console.log('🔧 尝试从字符串重新解析日期...')
    if (examStartDateStr.value) {
      examStartDate.value = new Date(examStartDateStr.value)
      console.log('🔧 重新解析开始日期:', examStartDate.value)
    }
    if (examEndDateStr.value) {
      examEndDate.value = new Date(examEndDateStr.value)
      console.log('🔧 重新解析结束日期:', examEndDate.value)
    }
    
    // 再次验证
    if (!examStartDate.value || !examEndDate.value || examStartDate.value.getTime() === 0 || examEndDate.value.getTime() === 0) {
      schedulingError.value = '请重新设置考试开始日期和结束日期'
      console.error('❌ 排班失败: 日期解析失败', {
        examStartDate: examStartDate.value,
        examEndDate: examEndDate.value,
        examStartDateStr: examStartDateStr.value,
        examEndDateStr: examEndDateStr.value
      })
      
      // 强制用户回到日期设置步骤
      currentStep.value = 2
      return
    }
  }
  
  // 添加详细的排班前检查
  console.log('🔍 排班前数据检查:')
  console.log('📅 开始日期:', examStartDate.value?.toISOString())
  console.log('📅 结束日期:', examEndDate.value?.toISOString())
  console.log('👥 学员数量:', studentList.value?.length || 0)
  console.log('👨‍🏫 教师数量:', teacherList.value?.length || 0)
  
  // 检查学员数据
  if (!studentList.value || studentList.value.length === 0) {
    schedulingError.value = '请先加载学员数据'
    console.error('❌ 排班失败: 学员数据为空')
    return
  }
  
  // 检查教师数据
  if (!teacherList.value || teacherList.value.length === 0) {
    schedulingError.value = '请先加载教师数据'
    console.error('❌ 排班失败: 教师数据为空')
    return
  }
  
  // 调用原有的排班逻辑
  await originalNextStep()
}

// 约束切换方法（移除重复定义）
// 使用前面已定义的toggleConstraint函数

// 步骤验证方法
const canProceedToNextStep = () => {
  const result = (() => {
    switch (currentStep.value) {
      case 1:
        return studentList.value.length > 0
      case 2:
        return examStartDateStr.value && examEndDateStr.value && 
               new Date(examStartDateStr.value) <= new Date(examEndDateStr.value)
      case 3:
      case 4:
      case 5:
        return true // 约束配置步骤可以跳过
      case 6:
        return true
      default:
        return false
    }
  })()
  
  console.log(`🔍 canProceedToNextStep 检查 - 步骤${currentStep.value}:`, result)
  if (currentStep.value === 1) {
    console.log('学员数量:', studentList.value.length)
    console.log('学员列表状态', studentList.value)
        console.log('学员列表是否为数组', Array.isArray(studentList.value))
  }
  if (currentStep.value === 2) {
    console.log('开始日期', examStartDateStr.value)
    console.log('结束日期:', examEndDateStr.value)
  }
  
  return result
}

// 步骤导航方法（移除重复定义）
// 使用前面已定义的nextStep、prevStep、goToStep函数

// 
// 
const originalNextStep = async () => {
  try {
    // 验证必要条件
    if (studentList.value.length === 0) {
      // 使用增强错误反馈显示学员名单缺失错误
      const conflicts: ConflictInfo[] = [{
        id: 'missing-student-list',
        type: 'scheduling_conflict',
        severity: 'HIGH',
        description: '缺少学员名单数据',
        affectedEntities: ['students'],
        suggestedSolutions: [
          '上传学员名单文件',
          '检查文件格式是否正确'
        ],
        autoResolvable: false
      }]
      
      enhancedErrorFeedbackService.showErrorFeedback(
        'validation_error',
        '请先上传学员名单文件',
        conflicts
      )
      
      schedulingError.value = '请先上传学员名单文件'
      return
    }
    
    if (!examStartDate.value || !examEndDate.value) {
      // 使用增强错误反馈显示日期范围缺失错误
      const conflicts: ConflictInfo[] = [{
        id: 'missing-date-range',
        type: 'scheduling_conflict',
        severity: 'MEDIUM',
        description: '缺少考试日期范围配置',
        affectedEntities: ['schedule'],
        suggestedSolutions: [
          '设置考试开始和结束日期',
          '检查日期是否为工作日'
        ],
        autoResolvable: false
      }]
      
      enhancedErrorFeedbackService.showErrorFeedback(
        'validation_error',
        '请选择考试日期范围',
        conflicts
      )
      
      schedulingError.value = '请选择考试日期范围'
      return
    }
    
    console.log('🚀 启动增强排班系统')
    console.log('🔍 当前学员数量:', studentList.value.length)
    console.log('🔍 考试日期范围:', examStartDate.value, '到', examEndDate.value)
    
    // 设置排班状态
    isScheduling.value = true
    schedulingProgress.value = 5
    schedulingError.value = ''
    
    // 准备考官数据
    const teachers: TeacherInfo[] = await prepareTeacherData()
    
    // 缓存考官数据供getTeacherNameById使用
    cachedTeacherData = teachers
    teacherList.value = teachers  // 同时更新teacherList供排班检查使用
    console.log('考官数据已缓存，缓存数量:', cachedTeacherData.length)
    
    // 考官数据完整性验证
    console.log('🔍 开始考官数据完整性验证.')
    const teacherValidationResult = validateTeacherData(teachers)
    if (!teacherValidationResult.isValid) {
      console.error('考官数据验证失败:', teacherValidationResult.errors)
      
      // 使用增强错误反馈显示考官数据验证错误
      const conflicts: ConflictInfo[] = [{
        id: 'teacher-validation-error',
        type: 'scheduling_conflict',
        severity: 'HIGH',
        description: '考官数据验证失败',
        affectedEntities: ['teachers'],
        suggestedSolutions: [
          '检查考官数据完整性',
          '重新加载考官数据',
          '修复考官数据格式'
        ],
        autoResolvable: false
      }]
      
      enhancedErrorFeedbackService.showErrorFeedback(
        'validation_error',
        `考官数据验证失败: ${teacherValidationResult.errors.join(', ')}`,
        conflicts
      )
      
      schedulingError.value = `考官数据验证失败: ${teacherValidationResult.errors.join(', ')}`
      isScheduling.value = false
      return
    }
    console.log('考官数据验证通过，有效考官数:', teacherValidationResult.validCount)
    
    schedulingProgress.value = 10
    
    // 准备学员数据
    const students: StudentInfo[] = await prepareStudentData()
    console.log('学员数据准备完成:', students.length, '名学员')
    
    schedulingProgress.value = 15
    
    // 生成考试日期列表
    const examDates = generateExamDateRange(examStartDate.value, examEndDate.value)
    console.log('考试日期列表:', examDates)
    
    schedulingProgress.value = 20
    
    // 求解模式配置
    const solvingMode = solvingModeRef.value || 'auto'
    console.log('🎯 使用求解模式:', solvingMode)
    
    // 🎯 最终优化排班- 完整降级体系 (严格按照constraint_weights_analysis.md文档规范);
    // 映射到后端ConstraintConfiguration的格式
    const basicConstraints = {
      // Hard constraints - HC1约束正确配置
      workdaysOnlyExam: true,  // HC1: 法定节假日不安排考试，周末可以但行政班考官不参与
      consecutiveTwoDaysExamEnabled: true,  // HC6: 考生需要在连续两天完成考试
      noDayShiftExaminer: true,  // HC3: 考官执勤白班不能安排考试（行政班考官除外）
      noStudentGroupDayShift: true,  // HC5: 考生执勤白班不能安排考试
      examinerDepartmentRules: true,  // HC2: 考官1与学员同科室
      twoMainExaminersRequired: true,  // HC7: 必须有考官1和考官2两名考官
      noExaminerTimeConflict: true,  // HC4: 每名考官每天只能监考一名考生
      // 移除冗余的约束配置，使用统一的workdaysOnlyExam
      
      // 软约束权重 - 严格按照文档权重设置
      // SC1: 晚班考官优先级最高权重（权重：100）
      nightShiftTeacherPriority: 100,
      // SC2: 考官2专业匹配（权重：90）
      preferRecommendedExaminer2Weight: 90,
      // SC3: 休息第一天考官优先级次高权重（权重：80）
      preferFirstRestDayTeachers: 80,
      firstRestDayTeacherPriority: 80,
      // SC4: 备份考官专业匹配（权重：70）
      preferRecommendedBackup: 70,
      // SC5: 休息第二天考官优先级中等权重（权重：60）
      preferSecondRestDayTeachers: 60,
      secondRestDayTeacherPriority: 60,
      // SC6: 考官2备选方案（权重：50）
      preferNonRecommendedExaminer2: 50,
      // SC7: 行政班考官优先级最低权重（权重：40）
      preferAdminTeachers: 40,
      adminTeacherPriority: 40,
      // SC8: 备份考官备选方案（权重：30）
      preferNonRecommendedBackup: 30,
      // SC9: 区域协作鼓励（权重：20）
      allowDept37CrossUse: 20,
      // SC10: 工作量均衡（权重：10）
      balanceWorkload: 10,
      // SC11: 日期分配均衡（权重：5）
      preferLaterDates: 5,
      // 启用灵活调度
      enableFlexibleScheduling: 10,
      maxTwoStudentsPerDay: 15,
      teacherStatusPriority: 80,
      nightShiftTeacherRecommendedDepartmentBonus: 25
    }
    
    // 转换为OptaPlanner格式
    const optaPlannerStudents = students.map((student: any) => ({
      id: student.id,
      name: student.name,
      // 统一向后端使用区域一室等全称，避免后端科室识别为空
      department: mapDepartmentName(student.department),
      group: student.group || '组', // 确保group不为空
      // 推荐考官科室也统一转全称（若本身为简写）
      recommendedExaminer1Dept: student.recommendedExaminer1Dept ? mapDepartmentName(student.recommendedExaminer1Dept) : undefined,
      recommendedExaminer2Dept: student.recommendedExaminer2Dept ? mapDepartmentName(student.recommendedExaminer2Dept) : undefined,
      recommendedBackupDept: student.recommendedBackupDept ? mapDepartmentName(student.recommendedBackupDept) : undefined
    }))
    
    const optaPlannerTeachers = teachers.map(teacher => ({
      id: teacher.id,
      name: teacher.name,
      // 统一转全称，避免后端内部分支解析失败
      department: mapDepartmentName(teacher.department),
      group: teacher.group,
      skills: teacher.skills,
      workload: teacher.workload,
      consecutiveDays: teacher.consecutiveDays
    }))
    
    // 🔧 新增：验证考官数据，禁止非法科室
    const illegalTeachers = optaPlannerTeachers.filter(t => {
      const dept = t.department || ''
      return dept.includes('模拟机') || dept.includes('现场') || 
             dept.includes('口试') || dept.includes('理论') || 
             dept.includes('实操') || dept.includes('实践') || dept.includes('笔试')
    })
    
    if (illegalTeachers.length > 0) {
      const names = illegalTeachers.map(t => `${t.name}(${t.department})`).join(', ')
      ElMessage.error(`🚨 数据错误：检测到${illegalTeachers.length}名考官的科室数据异常`)
      throw new Error(`数据错误：检测到${illegalTeachers.length}名考官的科室数据异常：\n${names}\n\n这些是考试科目，不是科室名称！请返回考官管理页面检查数据。`)
    }
    
    // 验证日期数据并构建OptaPlanner排班请求
    if (!examStartDate.value || !examEndDate.value) {
      throw new Error('日期数据未正确设置，无法构建排班请求')
    }
    
    const startDateStr = examStartDate.value.toISOString().split('T')[0]
    const endDateStr = examEndDate.value.toISOString().split('T')[0]
    
    console.log('🔍 构建OptaPlanner请求，日期验证:')
    console.log('📅 开始日期:', startDateStr)
    console.log('📅 结束日期:', endDateStr)
    
    const optaPlannerRequest: OptaPlannerRequest = {
      students: optaPlannerStudents,
      teachers: optaPlannerTeachers,
      startDate: startDateStr,
      endDate: endDateStr,
      constraints: basicConstraints,
      solverConfig: {
        timeoutSeconds: solvingMode === 'fast' ? 15 : solvingMode === 'optimal' ? 60 : 30,
        maxIterations: solvingMode === 'fast' ? 3000 : solvingMode === 'optimal' ? 10000 : 5000,
        description: `${solvingMode}模式求解配置`
      }
    }
    
    console.log('🚀 OptaPlanner排班请求配置:', {
      solvingMode,
      solverConfig: optaPlannerRequest.solverConfig,
      studentsCount: students.length,
      teachersCount: teachers.length,
      examDatesCount: examDates.length
    })
    
    // 详细日志：打印完整的请求数据
    console.log('📋 完整学员数据:', students)
    console.log('👥 完整考官数据:', teachers)
    console.log('📅 考试日期:', examDates)
    console.log('⚙️ 约束配置:', basicConstraints)
    console.log('🔧 完整请求对象:', optaPlannerRequest)
    
    schedulingProgress.value = 25
    
    console.log('🧠 启动OptaPlanner排班服务...')
    
    // 🔑 关键修复：在发起请求前生成sessionId并连接WebSocket
    const sessionId = 'schedule-' + Date.now() + '-' + Math.random().toString(36).substring(7)
    wsSessionId.value = sessionId
    ;(window as any).__opta_session_id = sessionId
    console.log('🔑 [关键修复] 生成sessionId并同步至全局:', sessionId)
    
    // 🆕 立即连接WebSocket（在发起请求之前）
    try {
      console.log('📡 [关键修复] 在发起请求前连接WebSocket...')
      await connectWebSocketForRealtimeUpdates(sessionId)
      console.log('✅ [关键修复] WebSocket已连接，准备接收中间结果')
    } catch (error) {
      console.error('❌ [关键修复] WebSocket连接失败:', error)
    }
    
    // 🎯 立即滚动到排班结果区域并关闭弹窗
    closeModal()
    await nextTick()
    
    // 🎯 准备实时进度数据（不自动显示窗口）
    // 用户需要点击"实时计算流程"按钮来查看进度详情
    realtimeLogs.value = [] // 清空之前的日志
    latestSoftScore.value = null
    bestSoftScore.value = null
    
    // 添加开始日志
    addRealtimeLog('🚀 开始排班计算', 'info')
    addRealtimeLog(`📊 学员数量: ${students.length}, 教师数量: ${teachers.length}`, 'info')
    addRealtimeLog(`📅 日期范围: ${examStartDate.value?.toISOString().split('T')[0]} 到 ${examEndDate.value?.toISOString().split('T')[0]}`, 'info')
    addRealtimeLog(`📡 WebSocket已连接，等待实时更新...`, 'info')
    
    // 滚动到排班结果表格
    setTimeout(() => {
      const scheduleTable = document.querySelector('.schedule-table')
      if (scheduleTable) {
        scheduleTable.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
    
    // 初始化进度状态
    totalStudents.value = students.length
    currentAssignmentCount.value = 0
    currentProgressMessage.value = '正在初始化排班算法...'
    
    // 🔍 验证请求数据的完整性
    console.log('🔍 验证OptaPlanner请求数据:')
    console.log('📋 学员数据:', students.length, '名学员')
    console.log('👥 考官数据:', teachers.length, '名考官')
    console.log('📅 考试日期范围:', examStartDate.value, '到', examEndDate.value)
    console.log('📊 可用考试日期:', examDates.length, '天')
    console.log('⚙️ 约束配置keys:', Object.keys(basicConstraints))
    
    // 执行OptaPlanner排班（带实时进度更新）
    const optaPlannerResult: OptaPlannerResponse = await optaPlannerService.generateSchedule(
      optaPlannerRequest,
      // 实时进度回调函数（修复：添加async支持await）
      async (progress) => {
        schedulingProgress.value = Math.max(25, progress.percentage)
        
        // 更新进度消息
        if (progress.message) {
          currentProgressMessage.value = progress.message
          if (progress.percentage === 100) {
            schedulingError.value = `${progress.message}`
          } else {
            // 显示更详细的进度信息
            const scoreInfo = progress.score ? 
              ` (硬约束: ${progress.score.hardScore}, 软约束: ${progress.score.softScore})` : ''
            schedulingError.value = `🔄 ${progress.message}${scoreInfo}`
          }
        }
        
        // 🎯 OptaPlanner风格的实时更新机制
        if (progress.currentSolution && progress.currentSolution.assignments && progress.currentSolution.assignments.length > 0) {
          // 更新分配计数和进度
          currentAssignmentCount.value = progress.currentSolution.assignments.length
          const assignedCount = progress.currentSolution.assignments.length
          const totalAssignments = students.length * 2
          const assignmentProgress = Math.min(100, Math.round((assignedCount / totalAssignments) * 100))
          
          // 更新进度消息
          currentProgressMessage.value = `正在计算最优方案... ${assignmentProgress}%`
          
          // 🔄 立即触发实时表格更新 - 有任何分配就更新
          console.log(`🔄 触发实时表格更新: ${assignmentProgress}% (${assignedCount}个分配)`)
          
          // 立即进行增量更新，不等到算法完成
          const intermediateResult = {
            assignments: progress.currentSolution.assignments.map((assignment: any) => ({
              id: assignment.id,
              studentId: assignment.student?.id || assignment.studentId,
              studentName: assignment.student?.name || assignment.studentName,
              studentDepartment: assignment.student?.department || assignment.studentDepartment,
              examDate: assignment.examDate,
              examType: assignment.examType,
              subjects: assignment.subjects,
              examiner1: assignment.examiner1,
              examiner2: assignment.examiner2,
              backupExaminer: assignment.backupExaminer,
              location: assignment.location,
              timeSlot: assignment.timeSlot
            })),
            unassignedStudents: (progress.currentSolution as any).unassignedStudents || [],
            statistics: progress.currentSolution.statistics || {}
          }
          
          // 🎯 关键修复：立即更新表格显示中间结果
          console.log('🎯 [关键修复] 开始更新表格，显示中间结果...')
          console.log('📊 [数据检查] intermediateResult:', {
            assignmentsCount: intermediateResult.assignments.length,
            firstAssignment: intermediateResult.assignments[0],
            hasExaminer1: !!intermediateResult.assignments[0]?.examiner1,
            hasExaminer2: !!intermediateResult.assignments[0]?.examiner2
          })
          
          try {
            await updateScheduleResults(intermediateResult as any, true)  // true表示实时更新
            console.log('✅ [关键修复] 表格已更新，当前显示', scheduleResults.value.length, '行数据')
            console.log('📋 [表格数据] 第一行:', scheduleResults.value[0])
          } catch (error) {
            console.error('❌ [关键修复] 表格更新失败:', error)
            console.error('❌ [错误堆栈]:', error)
          }
          
          // 🔄 进度更新
          addRealtimeLog(`🔄 进度更新: ${assignedCount}个分配 (${assignmentProgress}%)`, 'success')
          
          // 添加进度日志
          addRealtimeLog(`🔍 OptaPlanner求解中: ${assignmentProgress}% 完成`, 'info')
          
          console.log(`🎯 OptaPlanner求解进度: ${assignmentProgress}% (${assignedCount}/${totalAssignments})`)
        }
        
        // 如果排班完成，标记完成状态
        if (progress.percentage === 100) {
          console.log(`🎉 排班进度完成 (100%)`)
          schedulingProgress.value = 100
          currentProgressMessage.value = progress.message || '排班完成'
          schedulingError.value = progress.message || '排班完成'
          
          // 添加完成日志
          addRealtimeLog('🎉 算法计算完成', 'success')
          addRealtimeLog('⏳ 正在处理最终结果...', 'info')
          
          // 最终结果将由主函数的 optaPlannerResult 处理
        }
      }
    )
    
    // 从后端响应中获取 WebSocket 会话ID，用于实时进度监控
    if ((optaPlannerResult as any)?.sessionId) {
      wsSessionId.value = (optaPlannerResult as any).sessionId
      console.log('✅ [调试] 获得sessionId:', wsSessionId.value)
      
      // 🆕 连接WebSocket并监听中间结果
      console.log('🔍 [调试] 准备连接WebSocket进行实时更新...')
      try {
        if (wsSessionId.value) {
          await connectWebSocketForRealtimeUpdates(wsSessionId.value)
          console.log('✅ [调试] WebSocket连接函数执行完成')
        }
      } catch (error) {
        console.error('❌ [调试] WebSocket连接函数执行失败:', error)
      }
    } else {
      console.error('❌ [调试] 未获得sessionId，无法连接WebSocket')
      console.log('🔍 [调试] optaPlannerResult:', optaPlannerResult)
    }

    schedulingProgress.value = 90
    
    if (optaPlannerResult.success) {
      console.log('🎉 OptaPlanner排班成功') 
      console.log('📊 排班统计:', optaPlannerResult.statistics)
      
      // 添加成功日志
      addRealtimeLog('✅ 排班结果处理成功', 'success')
      addRealtimeLog(`📊 共生成 ${optaPlannerResult.assignments?.length || 0} 个排班分配`, 'info')
      
      // 转换assignments格式 - 增强数据格式处理
      let convertedAssignments = optaPlannerResult.assignments.map((assignment: any) => ({
        id: assignment.id,
        studentId: assignment.student.id,
        studentName: assignment.student.name,
        studentDepartment: assignment.student.department,
        examDate: assignment.examDate,
        examType: assignment.examType,
        subjects: assignment.subjects,
        examiner1: assignment.examiner1,  // 保持Teacher对象，让getTeacherNameById处理
        examiner2: assignment.examiner2,  // 保持Teacher对象，让getTeacherNameById处理
        backupExaminer: assignment.backupExaminer,  // 保持Teacher对象，让getTeacherNameById处理
        location: assignment.location,
        timeSlot: assignment.timeSlot
      }))
      
      // 检查约束违反情况
      // ✨ 修复：使用独立变量名，避免覆盖原始的完整日期范围
      const actualUsedDates = [...new Set(convertedAssignments.map(a => a.examDate))]
      const violations: ConstraintViolation[] = []
      
      // 检查节假日违反
      const holidayViolation = createHolidayViolation(actualUsedDates)
      if (holidayViolation.type === 'holiday') {
        violations.push(holidayViolation)
      }
      
      // 🔧 修复：不再检查周末违反，以后端OptaPlanner结果为准
      // 周末考试已由后端HC1约束正确处理，前端不再重复验证
      console.log('🔗 [约束同步] 跳过前端周末约束检查，以后端OptaPlanner结果为准')
      
      // 检查主考官不足违反
      const mainExaminersViolation = createInsufficientExaminersViolation(convertedAssignments)
      if (mainExaminersViolation.type === 'teacher') {
        violations.push(mainExaminersViolation)
      }
      
      // 验证约束违反的准确性
      const accurateViolations = validateViolationAccuracy(violations, convertedAssignments, actualUsedDates)
      
      // 过滤和合并约束违反，减少弹窗数量
      const filteredViolations = filterAndMergeViolations(accurateViolations)
      
      // 智能显示控制
      if (filteredViolations.length > 0) {
        const shouldShow = checkShouldShowNewViolations(filteredViolations)
        if (shouldShow) {
          constraintViolations.value = filteredViolations
        } else {
          console.log('📝 约束违反已被用户关闭，跳过显示')
        }
      }
      
      // 记录原始违反数量用于统计
      console.log(`📊 约束违反统计: 原始${violations.length}个 -> 验证后${accurateViolations.length}个 -> 过滤后${filteredViolations.length}个`)
      
      // 🔍 硬约束诊断：详细输出后端返回的约束状态
      console.log('🔍🔍🔍 ========== 硬约束诊断开始 ==========')
      console.log('🔍 后端返回的完整score:', optaPlannerResult.score)
      console.log('🔍 后端返回的statistics:', JSON.stringify(optaPlannerResult.statistics, null, 2))
      
      // 解析硬约束得分
      let backendHardScore = 0
      let backendSoftScore = 0
      if (optaPlannerResult.score) {
        if (typeof optaPlannerResult.score === 'string') {
          const match = optaPlannerResult.score.match(/(-?\d+)hard\/(-?\d+)soft/)
          if (match) {
            backendHardScore = parseInt(match[1])
            backendSoftScore = parseInt(match[2])
          }
        } else if (typeof optaPlannerResult.score === 'object') {
          backendHardScore = optaPlannerResult.score.hardScore || 0
          backendSoftScore = optaPlannerResult.score.softScore || 0
        }
      }
      
      console.log('🔍 解析后的硬约束得分:', backendHardScore)
      console.log('🔍 解析后的软约束得分:', backendSoftScore)
      console.log('🔍 后端是否认为硬约束满足:', backendHardScore === 0 ? '✅ 是' : '❌ 否')
      
      // 🔍 科室归一化函数（与后端保持一致）
      const normalizeDepartment = (dept: string | undefined): string => {
        if (!dept) return ''
        
        const normalized = dept.trim()
        
        // 标准化映射（与后端OptimizedExamScheduleConstraintProvider.normalizeDepartment()一致）
        if (normalized.includes('区域一室') || normalized.includes('一室') || normalized.includes('1室') || normalized.includes('第1科室')) return '一'
        if (normalized.includes('区域二室') || normalized.includes('二室') || normalized.includes('2室') || normalized.includes('第2科室')) return '二'
        if (normalized.includes('区域三室') || normalized.includes('三室') || normalized.includes('3室') || normalized.includes('第3科室')) return '三'
        if (normalized.includes('区域四室') || normalized.includes('四室') || normalized.includes('4室') || normalized.includes('第4科室')) return '四'
        if (normalized.includes('区域五室') || normalized.includes('五室') || normalized.includes('5室') || normalized.includes('第5科室')) return '五'
        if (normalized.includes('区域六室') || normalized.includes('六室') || normalized.includes('6室') || normalized.includes('第6科室')) return '六'
        if (normalized.includes('区域七室') || normalized.includes('七室') || normalized.includes('7室') || normalized.includes('第7科室')) return '七'
        if (normalized.includes('区域八室') || normalized.includes('八室') || normalized.includes('8室') || normalized.includes('第8科室')) return '八'
        if (normalized.includes('区域九室') || normalized.includes('九室') || normalized.includes('9室') || normalized.includes('第9科室')) return '九'
        if (normalized.includes('区域十室') || normalized.includes('十室') || normalized.includes('10室') || normalized.includes('第10科室')) return '十'
        
        return normalized
      }
      
      // 🔍 检查考官1科室是否有效（与后端isValidExaminer1Department()一致）
      const isValidExaminer1Department = (studentDept: string, examiner1Dept: string): boolean => {
        // 同科室
        if (studentDept === examiner1Dept) return true
        
        // 3室7室互通
        if ((studentDept === '三' && examiner1Dept === '七') || (studentDept === '七' && examiner1Dept === '三')) {
          return true
        }
        
        return false
      }
      
      // 🔍 详细检查每条排班记录的HC2约束
      console.log('🔍🔍🔍 ========== 检查每条排班的HC2约束 ==========')
      const hc2Violations: any[] = []
      
      convertedAssignments.forEach((assignment: any, index: number) => {
        const student = assignment.学员信息
        const day1 = assignment.第一天
        const day2 = assignment.第二天
        
        // 检查第一天
        if (day1?.考官一) {
          const examiner1 = teachers.find(t => t.name === day1.考官一)
          const examiner2 = teachers.find(t => t.name === day1.考官二)
          
          if (examiner1 && student) {
            const studentDeptRaw = student.科室
            const examiner1DeptRaw = examiner1.department
            const examiner2DeptRaw = examiner2?.department
            
            // 🔧 使用归一化后的科室名称进行比较（与后端一致）
            const studentDept = normalizeDepartment(studentDeptRaw)
            const examiner1Dept = normalizeDepartment(examiner1DeptRaw)
            const examiner2Dept = normalizeDepartment(examiner2DeptRaw)
            
            // 使用后端相同的验证逻辑
            const isExaminer1Valid = isValidExaminer1Department(studentDept, examiner1Dept)
            
            // 检查考官2是否与学员不同科室
            const isExaminer2Different = examiner2 && (studentDept !== examiner2Dept)
            
            // 检查考官1和考官2是否来自不同科室
            const areExaminersDifferent = examiner1 && examiner2 && (examiner1Dept !== examiner2Dept)
            
            const isValid = isExaminer1Valid && isExaminer2Different && areExaminersDifferent
            
            console.log(`🔍 第${index + 1}条-第一天: ${student.姓名}`, {
              原始科室: { 学员: studentDeptRaw, 考官1: examiner1DeptRaw, 考官2: examiner2DeptRaw },
              归一化科室: { 学员: studentDept, 考官1: examiner1Dept, 考官2: examiner2Dept },
              考官1: day1.考官一,
              考官2: day1.考官二,
              考官1验证: isExaminer1Valid ? '✅' : '❌',
              考官2验证: isExaminer2Different ? '✅' : '❌',
              考官间验证: areExaminersDifferent ? '✅' : '❌',
              总体: isValid ? '✅ 合规' : '❌ 违反HC2'
            })
            
            if (!isValid) {
              hc2Violations.push({
                学员: student.姓名,
                科室: `${studentDeptRaw}(${studentDept})`,
                日期: day1.考试日期,
                考官1: day1.考官一,
                考官1科室: `${examiner1DeptRaw}(${examiner1Dept})`,
                考官2: day1.考官二,
                考官2科室: `${examiner2DeptRaw}(${examiner2Dept})`,
                违反原因: !isExaminer1Valid ? '考官1与学员不同科室' : 
                         !isExaminer2Different ? '考官2与学员同科室' :
                         !areExaminersDifferent ? '考官1和考官2来自同一科室' : '未知'
              })
            }
          }
        }
        
        // 检查第二天（同样的逻辑）
        if (day2?.考官一) {
          const examiner1 = teachers.find(t => t.name === day2.考官一)
          const examiner2 = teachers.find(t => t.name === day2.考官二)
          
          if (examiner1 && student) {
            const studentDeptRaw = student.科室
            const examiner1DeptRaw = examiner1.department
            const examiner2DeptRaw = examiner2?.department
            
            // 🔧 使用归一化后的科室名称进行比较（与后端一致）
            const studentDept = normalizeDepartment(studentDeptRaw)
            const examiner1Dept = normalizeDepartment(examiner1DeptRaw)
            const examiner2Dept = normalizeDepartment(examiner2DeptRaw)
            
            // 使用后端相同的验证逻辑
            const isExaminer1Valid = isValidExaminer1Department(studentDept, examiner1Dept)
            const isExaminer2Different = examiner2 && (studentDept !== examiner2Dept)
            const areExaminersDifferent = examiner1 && examiner2 && (examiner1Dept !== examiner2Dept)
            const isValid = isExaminer1Valid && isExaminer2Different && areExaminersDifferent
            
            console.log(`🔍 第${index + 1}条-第二天: ${student.姓名}`, {
              原始科室: { 学员: studentDeptRaw, 考官1: examiner1DeptRaw, 考官2: examiner2DeptRaw },
              归一化科室: { 学员: studentDept, 考官1: examiner1Dept, 考官2: examiner2Dept },
              考官1: day2.考官一,
              考官2: day2.考官二,
              考官1验证: isExaminer1Valid ? '✅' : '❌',
              考官2验证: isExaminer2Different ? '✅' : '❌',
              考官间验证: areExaminersDifferent ? '✅' : '❌',
              总体: isValid ? '✅ 合规' : '❌ 违反HC2'
            })
            
            if (!isValid) {
              hc2Violations.push({
                学员: student.姓名,
                科室: `${studentDeptRaw}(${studentDept})`,
                日期: day2.考试日期,
                考官1: day2.考官一,
                考官1科室: `${examiner1DeptRaw}(${examiner1Dept})`,
                考官2: day2.考官二,
                考官2科室: `${examiner2DeptRaw}(${examiner2Dept})`,
                违反原因: !isExaminer1Valid ? '考官1与学员不同科室' : 
                         !isExaminer2Different ? '考官2与学员同科室' :
                         !areExaminersDifferent ? '考官1和考官2来自同一科室' : '未知'
              })
            }
          }
        }
      })
      
      console.log('🔍 HC2约束检查结果汇总:')
      console.log(`🔍 总排班数: ${convertedAssignments.length * 2}`)
      console.log(`🔍 HC2违反数: ${hc2Violations.length}`)
      if (hc2Violations.length > 0) {
        console.log('🔍 HC2违反详情:')
        console.table(hc2Violations)
      } else {
        console.log('🔍 ✅ 所有排班都符合HC2约束!')
      }
      console.log('🔍🔍🔍 ========== 硬约束诊断结束 ==========')
      
      // 处理排班结果
      // 🔧 修复：正确提取真实的软约束得分
      let realSoftScore = backendSoftScore
      if (realSoftScore === 0 && optaPlannerResult.statistics?.finalScore) {
        if (typeof optaPlannerResult.statistics.finalScore === 'object') {
          realSoftScore = optaPlannerResult.statistics.finalScore.softScore || 0
        } else if (typeof optaPlannerResult.statistics.finalScore === 'string') {
          const match = optaPlannerResult.statistics.finalScore.match(/(-?\d+)hard\/(-?\d+)soft/)
          if (match) {
            realSoftScore = parseInt(match[2])
          }
        }
      }
      console.log('📊 [软约束得分] 真实软约束得分:', realSoftScore)
      latestSoftScore.value = realSoftScore
      if (bestSoftScore.value === null || realSoftScore > bestSoftScore.value) {
        bestSoftScore.value = realSoftScore
      }
      
      const result = {
        assignments: convertedAssignments,
        unassignedStudents: [],
        statistics: {
          totalStudents: optaPlannerResult.statistics?.totalStudents || 0,
          assignedStudents: optaPlannerResult.statistics?.assignedStudents || 0,
          unassignedStudents: (optaPlannerResult.statistics?.totalStudents || 0) - (optaPlannerResult.statistics?.assignedStudents || 0),
          totalTeachers: teachers.length,
          activeTeachers: Math.ceil((optaPlannerResult.statistics?.assignedStudents || 0) / 2), // 估算活跃考官数
          averageWorkload: Math.ceil((optaPlannerResult.statistics?.assignedStudents || 0) / teachers.length),
          maxWorkload: Math.ceil((optaPlannerResult.statistics?.assignedStudents || 0) / teachers.length * 1.5),
          hardConstraintsSatisfied: (optaPlannerResult.statistics?.hardConstraintViolations || 0) === 0 ? 1 : 0,
          softConstraintsScore: realSoftScore,  // 🔧 使用真实的软约束得分
          continuityRate: optaPlannerResult.statistics?.completionPercentage || 0
        },
        conflicts: optaPlannerResult.conflicts || [],
        warnings: optaPlannerResult.warnings || [],
        recommendations: [] as string[]
      }
      
      // 清除可能存在的错误缓存
      console.log('🔄 清除旧缓存，准备显示新的排班结果')
      localStorage.removeItem('latest_schedule_result')
      scheduleResults.value = []
      
      // 🔧 首先修复考官分配问题
      console.log('🔧 开始修复考官分配问题...')
      try {
        const { examinerAssignmentFixer } = await import('../services/examinerAssignmentFixer')
        const fixResult = examinerAssignmentFixer.fixExaminerAssignments(convertedAssignments, teachers)
        
        if (fixResult.fixedCount > 0) {
          console.log(`✅ 修复了${fixResult.fixedCount}个考官分配问题`)
          convertedAssignments = fixResult.assignments
          
          // 重新检查约束违反
          const updatedMainExaminersViolation = createInsufficientExaminersViolation(convertedAssignments)
          if (updatedMainExaminersViolation.type === 'teacher') {
            // 更新violations数组中的约束
            const violationIndex = violations.findIndex(v => v.id === 'main-examiners-violation')
            if (violationIndex >= 0) {
              violations[violationIndex] = updatedMainExaminersViolation
            }
          } else {
            // 移除约束违反
            const violationIndex = violations.findIndex(v => v.id === 'main-examiners-violation')
            if (violationIndex >= 0) {
              violations.splice(violationIndex, 1)
            }
          }
          
          // 更新约束违反状态，应用过滤和合并
          const filteredViolations = filterAndMergeViolations(violations)
          constraintViolations.value = filteredViolations
        }
        
        if (fixResult.remainingIssues > 0) {
          console.warn(`⚠️ 仍有${fixResult.remainingIssues}个考官分配问题未解决`)
        }
      } catch (fixError) {
        console.error('❌ 考官分配修复失败:', fixError)
      }

      // 🚀 应用时间分散优化 - ❌ 已禁用：会导致前后端结果不一致
      // ⚠️ 问题：这个功能会重新调用OptaPlanner，替换掉后端的权威结果
      // ⚠️ 导致用户看到的结果和后端日志中的结果不一致
      console.log('⚠️ 时间分散优化已禁用（避免前后端结果不一致）')
      /*
      console.log('🚀 开始应用时间分散优化...')
      // ✨ 确认使用原始的完整日期范围
      console.log(`📅 传递给增强排班的日期范围: ${examDates.length} 天 (从 ${examDates[0]} 到 ${examDates[examDates.length - 1]})`)
      console.log(`📊 实际已使用日期: ${actualUsedDates.length} 天 (${actualUsedDates.join(', ')})`)
      
      try {
        const { enhancedSchedulingService } = await import('../services/enhancedSchedulingService')
        const optimizedResult = await enhancedSchedulingService.executeEnhancedScheduling({
          students: convertedAssignments.map(a => {
            // 从原始学员数据中查找对应的学员信息，获取group等字段
            const originalStudent = studentList.value.find(s => s.id === a.studentId)
            return {
              id: a.studentId,
              name: a.studentName,
              department: a.studentDepartment,
              group: originalStudent?.group || '一组', // 从原始学员数据获取班组信息
              examType: a.examType,
              subjects: a.subjects || [],
              recommendedExaminer1Dept: originalStudent?.recommendedExaminer1Dept,
              recommendedExaminer2Dept: originalStudent?.recommendedExaminer2Dept
            }
          }),
          teachers: teachers,
          examDates: examDates, // ✨ 现在使用的是原始的完整日期范围（6周），而不是已使用的2天
          constraints: {
            maxDailyAssignments: 10,
            minRestDays: 1,
            preferredWorkload: 5
          }
        })
        
        if (optimizedResult.assignments.length > 0) {
          console.log('✅ 时间分散优化成功应用')
          // 更新排班结果为优化后的结果
          const optimizedAssignments = optimizedResult.assignments.map(a => ({
            id: a.id,
            studentId: a.studentId,
            studentName: convertedAssignments.find(ca => ca.id === a.id)?.studentName || '',
            studentDepartment: convertedAssignments.find(ca => ca.id === a.id)?.studentDepartment || '',
            examDate: a.date.toISOString().split('T')[0],
            examType: a.examType,
            subjects: a.subjects,
            examiner1: convertedAssignments.find(ca => ca.id === a.id)?.examiner1,
            examiner2: convertedAssignments.find(ca => ca.id === a.id)?.examiner2,
            backupExaminer: convertedAssignments.find(ca => ca.id === a.id)?.backupExaminer,
            location: a.location,
            timeSlot: a.timeSlot
          }))
          
          result.assignments = optimizedAssignments
          result.recommendations = optimizedResult.recommendations
        }
      } catch (error) {
        console.warn('⚠️ 时间分散优化失败:', error)
      }
      */
      
      schedulingResult.value = result as any
      await updateScheduleResults(result as any, false)
      
      // 显示详细成功消息
      setTimeout(() => {
        isScheduling.value = false
        schedulingProgress.value = 100
      
      // 计算完成后的处理（移除集成面板相关代码）
        
        let message = `🎉 OptaPlanner排班完成！\n\n`
        message += `📊 排班统计:\n`
        
        // 使用实际的result.statistics数据
        const stats = result.statistics || optaPlannerResult.statistics
        const totalStudents = stats?.totalStudents || scheduleResults.value.length
        const assignedStudents = stats?.assignedStudents || scheduleResults.value.filter(s => s.examiner1_1 && s.examiner2_1).length
        const completionRate = totalStudents > 0 ? (assignedStudents / totalStudents * 100) : 0
        
        message += `✅完成率: ${completionRate.toFixed(1)}%\n`
        message += `✅分配学员: ${assignedStudents}/${totalStudents}\n`
        message += `❌硬约束违反: ${stats?.hardConstraintsSatisfied || 0}个\n`
        message += `⚠️软约束得分 ${stats?.softConstraintsScore || 0}\n`
        if (bestSoftScore.value !== null) {
          message += `🌟历史最高软约束得分 ${bestSoftScore.value}\n`
        }
        message += `\n`
        if (optaPlannerResult.warnings && optaPlannerResult.warnings.length > 0) {
          message += `⚠️ 警告: ${optaPlannerResult.warnings.length}个\n`
        }
        
        if (optaPlannerResult.conflicts && optaPlannerResult.conflicts.length > 0) {
          message += `❌冲突: ${optaPlannerResult.conflicts.length}个\n`
        }
        
        message += `\n🚀使用OptaPlanner约束求解引擎\n`
        message += `所有排班结果已生成，请查看下方表格。`
        
        // 使用非阻塞的通知方式
        console.log(message)
        
        // 显示成功通知（可以点击关闭）
        schedulingError.value = ''
        schedulingResult.value = result
        
        // 🎯 设置统一结果弹窗数据
        unifiedResultData.value = {
          success: true,
          statistics: {
            totalStudents: totalStudents,
            assignedStudents: assignedStudents,
            hardConstraintsSatisfied: stats?.hardConstraintsSatisfied || 0,
            softConstraintsScore: stats?.softConstraintsScore || 0,
            bestSoftConstraintsScore: bestSoftScore.value,
                         hardConstraintViolations: 0, // 从约束违反数组计算
             softConstraintViolations: 0 // 从约束违反数组计算
          },
          warnings: optaPlannerResult.warnings || [],
          conflicts: optaPlannerResult.conflicts || [],
          message: message
        }
        
        // 显示统一的结果弹窗（替代原来的分离弹窗）
        showUnifiedResultModal.value = true
      }, 1000)
      
      // 关闭弹窗并显示结果
      closeModal()
      
    } else {
      console.error('OptaPlanner排班失败')
      
      // 显示详细错误信息
      let errorMessage = `排班失败\n\n`
      
      if (optaPlannerResult.message) {
        errorMessage += `❌错误信息: ${optaPlannerResult.message}\n\n`
      }
      
      if (optaPlannerResult.warnings && optaPlannerResult.warnings.length > 0) {
        errorMessage += `⚠️ 警告信息:\n`
        optaPlannerResult.warnings.forEach((warning, index) => {
          errorMessage += `${index + 1}. ${warning}\n`
        })
        errorMessage += `\n`
      }
      
      errorMessage += `💡 建议解决方案:\n`
      errorMessage += `1. 检查考官数量是否充足\n`
      errorMessage += `2. 确认考试日期范围合理\n`
      errorMessage += `3. 考虑放宽部分约束条件\n`
      errorMessage += `4. 联系系统管理员获取技术支持`
      
      // 使用非阻塞的错误通知
      console.error(errorMessage)
      
      // 创建错误通知元素
      const errorNotification = document.createElement('div')
      errorNotification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 500px;
        font-size: 14px;
        line-height: 1.4;
        cursor: pointer;
        white-space: pre-line;
      `
      errorNotification.innerHTML = errorMessage.replace(/\n/g, '<br>')
      errorNotification.title = '点击关闭'
      
      // 点击关闭
      errorNotification.onclick = () => errorNotification.remove()
      
      // 自动关闭（延长时间以便用户阅读）
      setTimeout(() => {
        if (errorNotification.parentNode) {
          errorNotification.remove()
        }
      }, 15000)
      
      document.body.appendChild(errorNotification)
      
      // 更新UI状态
      setTimeout(() => {
        isScheduling.value = false
        schedulingProgress.value = 0
        schedulingError.value = 'OptaPlanner排班失败，请查看详细信息'
      }, 1000)
    }
    
  } catch (error) {
    console.error('排班系统错误:', error)
    
    // 使用增强错误反馈服务处理错误
    const conflicts: ConflictInfo[] = []
    
    // 分析错误类型并生成冲突信息
    if ((error as Error).message.includes('考官数据')) {
      conflicts.push({
        id: 'teacher-data-error',
        type: 'scheduling_conflict',
        severity: 'HIGH',
        description: '考官数据验证失败',
        affectedEntities: ['teachers'],
        suggestedSolutions: [
          '检查考官数据格式',
          '重新加载考官数据'
        ],
        autoResolvable: false
      })
    }
    
    if ((error as Error).message.includes('学员名单')) {
      conflicts.push({
        id: 'student-data-error',
        type: 'scheduling_conflict',
        severity: 'HIGH',
        description: '学员名单格式错误',
        affectedEntities: ['students'],
        suggestedSolutions: [
          '检查学员名单格式',
          '重新上传学员名单'
        ],
        autoResolvable: false
      })
    }
    
    // 显示增强错误反馈
    enhancedErrorFeedbackService.showErrorFeedback(
      'system_error',
      (error as Error).message || '未知错误',
      conflicts
    )
    
    // 更新UI状态
    isScheduling.value = false
    schedulingProgress.value = 0
    schedulingError.value = (error as Error).message || '未知错误'
  }
}

// 生成考试日期范围
const generateExamDateRange = (startDate: Date, endDate: Date): string[] => {
  const dates: string[] = []
  const current = new Date(startDate)
  
  // 2025-2026年法定节假日（与后端HolidayConfig保持一致）
  const holidays = new Set([
    // 2025年法定节假日
    '2025-01-01', // 元旦
    '2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31', // 春节
    '2025-02-01', '2025-02-02', '2025-02-03',
    '2025-04-05', '2025-04-06', '2025-04-07', // 清明节
    '2025-05-01', '2025-05-02', '2025-05-03', '2025-05-04', '2025-05-05', // 劳动节
    '2025-05-31', '2025-06-01', '2025-06-02', // 端午节
    // 注意：2025年中秋节与国庆节合并放假，10月1日至8日放假调休，共8天
    '2025-10-01', '2025-10-02', '2025-10-03', '2025-10-04', // 国庆节
    '2025-10-05', '2025-10-06', '2025-10-07', '2025-10-08', // 国庆节（与中秋节合并）
    
    // 2026年法定节假日
    '2026-01-01', // 元旦
    '2026-02-16', '2026-02-17', '2026-02-18', '2026-02-19', // 春节
    '2026-02-20', '2026-02-21', '2026-02-22',
    '2026-04-05', '2026-04-06', '2026-04-07', // 清明节
    '2026-05-01', '2026-05-02', '2026-05-03', // 劳动节
    '2026-05-29', // 端午节
    '2026-09-25', '2026-09-26', '2026-09-27', // 中秋节
    '2026-10-01', '2026-10-02', '2026-10-03', '2026-10-04', // 国庆节
    '2026-10-05', '2026-10-06', '2026-10-07'
  ])
  
  // 调休工作日（周末上班）
  const workdays = new Set([
    '2025-01-26', '2025-02-08', // 春节调休
    '2025-04-27', // 劳动节调休
    '2025-09-28', '2025-10-11', // 国庆节调休
    
    '2026-02-15', '2026-02-23', // 春节调休（预估）
    '2026-04-26', // 劳动节调休（预估）
    '2026-09-27', '2026-10-10' // 国庆节调休（预估）
  ])
  
  // 判断是否为工作日的函数（与后端HolidayConfig.isWorkingDay()保持一致）
  const isWorkingDay = (dateStr: string): boolean => {
    const date = new Date(dateStr + 'T00:00:00')
    const dayOfWeek = date.getDay()
    
    // 调试：记录日期判断过程
    console.log(`🔍 判断日期 ${dateStr}: 星期${dayOfWeek}`)
    
    // 如果是调休工作日，则为工作日
    if (workdays.has(dateStr)) {
      console.log(`✅ ${dateStr} 是调休工作日`)
      return true
    }
    
    // 如果是节假日，则不是工作日
    if (holidays.has(dateStr)) {
      console.log(`❌ ${dateStr} 是节假日`)
      return false
    }
    
    // 如果是周末，则不是工作日
    if (dayOfWeek === 0 || dayOfWeek === 6) { // 周日或周六
      console.log(`❌ ${dateStr} 是周末 (星期${dayOfWeek})`)
      return false
    }
    
    // 其他情况为工作日
    console.log(`✅ ${dateStr} 是普通工作日`)
    return true
  }
  
  while (current <= endDate) {
    const dateStr = current.toISOString().split('T')[0]
    
    // 使用与后端一致的工作日判断逻辑
    if (isWorkingDay(dateStr)) {
      dates.push(dateStr)
    }
    
    current.setDate(current.getDate() + 1)
  }
  
  console.log(`📅 生成考试日期范围: ${startDate.toISOString().split('T')[0]} 到 ${endDate.toISOString().split('T')[0]}`)
  console.log(`📊 可用工作日数量: ${dates.length} 天`)
  
  if (dates.length === 0) {
    console.warn('⚠️ 警告: 所选日期范围内没有可用的工作日')
    console.log('💡 建议: 请选择包含工作日的日期范围')
  } else if (dates.length < 2) {
    console.warn('⚠️ 警告: 可用工作日太少，可能无法完成所有排班')
    console.log('💡 建议: 请扩大日期范围以包含更多工作日')
  }
  console.log(`📋 具体日期: ${dates.join(', ')}`)
  
  return dates
}

// 准备考官数据 - 只使用实际上传的数据，不再依赖硬编码
const prepareTeacherData = async (): Promise<TeacherInfo[]> => {
  try {
    console.log('🔍 开始从存储服务加载考官数据...')
    
    // 调试：检查所有localStorage中的键
    console.log('📋 localStorage中所有的键:', Object.keys(localStorage))
    const teacherRelatedKeys = Object.keys(localStorage).filter(key => 
      key.toLowerCase().includes('teacher') || key.toLowerCase().includes('examiner')
    )
    console.log('🎯 考官相关的存储键:', teacherRelatedKeys)
    teacherRelatedKeys.forEach(key => {
      try {
        const data = localStorage.getItem(key)
        const parsed = data ? JSON.parse(data) : null
        console.log(`📊 ${key}:`, Array.isArray(parsed) ? `${parsed.length}条记录` : typeof parsed)
      } catch (e) {
        console.log(`❌ ${key}: 解析失败`)
      }
    })
    
    // 从存储服务加载实际的考官数据 - 使用与考官管理页面相同的存储方式
    let storedTeachers = []
    
    // 尝试多种存储键名，确保兼容性
    const teacherKeys = ['teachers', 'examiner_teachers', 'unified_teachers', 'teacher_data', 'teacherList']
    
    for (const key of teacherKeys) {
      try {
        const data = localStorage.getItem(key)
        if (data) {
          const parsed = JSON.parse(data)
          if (Array.isArray(parsed) && parsed.length > 0) {
            storedTeachers = parsed
            console.log(`✅从存储键 "${key}" 加载的考官数`, storedTeachers.length, '名考官')
            break
          }
        }
      } catch (error) {
        console.warn(`解析存储键 "${key}" 失败:`, error)
      }
    }
    
    // 如果还是没有数据，尝试使用原来的storageService
    if (storedTeachers.length === 0) {
      try {
      storedTeachers = await storageService.loadTeachers()
        console.log('✅从storageService加载考官数据:', storedTeachers.length, '名考官')
      } catch (error) {
        console.warn('从storageService加载考官数据失败:', error)
      }
    }
    
    // 如果仍然没有数据，尝试从unifiedStorageService加载
    if (storedTeachers.length === 0) {
      try {
        storedTeachers = await unifiedStorageService.loadTeachers()
        console.log('✅从unifiedStorageService加载考官数据:', storedTeachers.length, '名考官')
      } catch (error) {
        console.warn('从unifiedStorageService加载考官数据失败:', error)
      }
    }
    
    if (storedTeachers.length === 0) {
      console.error('❌存储中没有考官数据！请先在考官管理页面上传考官名单')
      console.log('💡 提示：请确保已在考官管理页面保存了考官数据')
      console.log('🔧 调试信息：如果考官管理页面有数据但这里读取不到，可能是存储键名不匹配')
      
      // 提供应急测试数据，但给出明确警告
      console.warn('⚠️ 使用应急测试教师数据，仅供功能验证！请尽快上传真实教师数据！')
      
      storedTeachers = [
        { id: 'test_1', name: '张考官', department: '区域一室', group: '一组', shift: '白班', status: '可用' },
        { id: 'test_2', name: '李考官', department: '区域二室', group: '二组', shift: '夜班', status: '可用' },
        { id: 'test_3', name: '王考官', department: '区域三室', group: '三组', shift: '休息', status: '可用' },
        { id: 'test_4', name: '赵考官', department: '区域四室', group: '四组', shift: '白班', status: '可用' },
        { id: 'test_5', name: '钱考官', department: '区域五室', group: '一组', shift: '夜班', status: '可用' },
        { id: 'test_6', name: '孙考官', department: '区域六室', group: '二组', shift: '休息', status: '可用' },
        { id: 'test_7', name: '周考官', department: '区域七室', group: '三组', shift: '白班', status: '可用' },
        { id: 'test_8', name: '吴考官', department: '区域一室', group: '四组', shift: '夜班', status: '可用' },
        { id: 'test_9', name: '郑考官', department: '区域二室', group: '一组', shift: '休息', status: '可用' },
        { id: 'test_10', name: '陈考官', department: '区域三室', group: '二组', shift: '白班', status: '可用' }
      ]
      
      // 显示警告信息给用户
      schedulingError.value = `⚠️ 正在使用测试教师数据进行排班！
      
请注意：
1. 当前使用的是系统内置的测试数据
2. 排班结果仅供功能验证，不应用于实际工作
3. 请尽快访问"考官管理"页面上传真实教师数据
4. 上传真实数据后，重新进行排班获得准确结果

测试教师数量: ${storedTeachers.length} 名`
    }
    
    // 将存储的考官数据转换为排班所需的格式
    const teacherData: TeacherInfo[] = storedTeachers
      .filter(teacher => teacher && teacher.id && teacher.name) // 过滤无效数据
      .map(teacher => ({
        id: teacher.id.toString(),
        name: teacher.name,
        department: teacher.department || '未分组',
        group: (teacher as any).group || '一', // 类型断言处理可能不存在的属性
        skills: (teacher as any).skills || teacher.specialties || ['理论教学', '实操指导'],
        workload: (teacher as any).workload || 0,
        consecutiveDays: (teacher as any).consecutiveDays || 0,
        
        // ✨ 智能推荐所需的扩展字段（基础值，后续动态更新）
        specialties: (teacher as any).specialties || (teacher as any).skills || [],
        experienceYears: (teacher as any).experienceYears || 3,
        available: true,  // 默认可用，在editExaminer中动态检测
        currentWorkload: 0,  // 默认0，在editExaminer中实时计算
        nightShiftPreferred: false,  // 默认false，在editExaminer中根据值班状态动态设置
        restDayStatus: 'none' as const,  // 默认none，在editExaminer中根据值班状态动态设置
        conflictInfo: ''  // 默认空，在editExaminer中动态生成
      }))
    
    console.log('考官数据转换完成:', teacherData.length, '名有效考官')
    
    // 验证数据完整性
    const validTeachers = teacherData.filter(teacher => 
      teacher.id && teacher.name && teacher.department && teacher.department !== '未分组'
    )
    
    if (validTeachers.length !== teacherData.length) {
      console.warn(`⚠️ 发现 ${teacherData.length - validTeachers.length} 名考官数据不完整，已过滤`)
    }
    
    if (validTeachers.length === 0) {
      console.error('没有有效的考官数据！请检查上传的考官名单格式')
      throw new Error('考官数据格式不正确，请检查上传的文件')
    }
    
    console.log(`🎯 成功加载 ${validTeachers.length} 名有效考官，来源：实际上传数据`)
    return validTeachers
    
  } catch (error) {
    console.error('加载考官数据失败:', error)
    // 不再使用硬编码数据作为后备，而是抛出错误提示用户
    throw new Error(`考官数据加载失败: ${(error as Error).message || '未知错误'}。请确保已在考官管理页面正确上传考官名单。`)
  }
}

// 硬编码考官数据已移除 - 现在只使用实际上传的数据
// 如果需要考官数据，请在考官管理页面上传最新的考官名单

// 科室名称映射函数
const mapDepartmentName = (deptName: string): string => {
  const deptMapping: { [key: string]: string } = {
    '一': '区域一室',
    '二': '区域二室', 
    '三': '区域三室',
    '四': '区域四室',
    '五': '区域五室',
    '六': '区域六室',
    '七': '区域七室',
    // 支持完整格式输入
    '区域一室': '区域一室',
    '区域二室': '区域二室',
    '区域三室': '区域三室',
    '区域四室': '区域四室',
    '区域五室': '区域五室',
    '区域六室': '区域六室',
    '区域七室': '区域七室'
  }
  return deptMapping[deptName] || deptName
}

// 辅助函数：将完整科室名称转换为简写格式
const convertDeptNameToShort = (deptName: string): string => {
  if (!deptName) return ''
  
  // 转换映射表：完整格式 -> 简写格式
  const deptConversionMap: { [key: string]: string } = {
    '区域一室': '一',
    '区域二室': '二', 
    '区域三室': '三',
    '区域四室': '四',
    '区域五室': '五',
    '区域六室': '六',
    '区域七室': '七'
  }
  
  return deptConversionMap[deptName] || deptName
}

// 加载学员数据
const loadStudentData = async () => {
  try {
    console.log('🔄 开始从后端加载学员数据...')
    const students = await dataManagementApi.getAllStudents()
    console.log('✅ 成功获取学员数据:', students.length, '名学员')
    
    // 转换为前端需要的格式
    studentList.value = students.map(student => ({
      id: student.id?.toString() || student.studentId,
      name: student.name,
      department: student.department.name,
      group: student.group?.name || '一组',
      recommendedExaminer1Dept: student.recommendedExaminer1Dept?.name,
      recommendedExaminer2Dept: student.recommendedExaminer2Dept?.name
    }))
    
    console.log('✅ 学员数据转换完成，总数:', studentList.value.length)
  } catch (error) {
    console.error('❌ 加载学员数据失败:', error)
    // 如果API调用失败，使用默认数据
    studentList.value = [
      { id: '1', name: '杨杰', department: '一', group: '一组' },
      { id: '2', name: '顾秀莲', department: '一', group: '二组' },
      { id: '3', name: '黄伟', department: '一', group: '三组' },
      { id: '4', name: '廖轩', department: '一', group: '四组' },
      { id: '5', name: '黎明', department: '二', group: '一组' },
      { id: '6', name: '马恒', department: '二', group: '二组' },
      { id: '7', name: '何若', department: '二', group: '三组' }
    ]
    console.log('⚠️ 使用默认学员数据，总数:', studentList.value.length)
  }
}

// 准备学员数据 - 符合新接口要求
const prepareStudentData = async (): Promise<StudentInfo[]> => {
  if (studentList.value.length === 0) {
    throw new Error('请先上传学员名单文件')
  }
  
  // 将现有学员数据转换为新格式，保持科室简写格式以匹配考官数据
  return studentList.value.map((student: any) => ({
    id: student.id.toString(),
    name: student.name,
    department: student.department, // 保持原始简写格式（一、二、三等）
    group: student.group || '一组', // 默认分配到一组
    // 推荐考官科室信息（转换为简写格式以匹配后端约束）
    recommendedExaminer1Dept: student.recommendedExaminer1Dept ? convertDeptNameToShort(student.recommendedExaminer1Dept) : undefined,
    recommendedExaminer2Dept: student.recommendedExaminer2Dept ? convertDeptNameToShort(student.recommendedExaminer2Dept) : undefined,
    recommendedBackupDept: student.recommendedBackupDept ? convertDeptNameToShort(student.recommendedBackupDept) : undefined,
    originalExaminers: {
      // 从上传文件中读取推荐考官科室信息（作为备选）
      examiner1: student.recommendedExaminer1Dept,
      examiner2: student.recommendedExaminer2Dept,
      backup: student.recommendedBackupDept
    }
  }))
}

// 旧的验证和工作日处理函数已移除，现在使用智能排班算法中的实现

// 更新排班结果到表格 - 处理新的排班结果格式
const updateScheduleResults = async (result: SchedulingResult, isRealtimeUpdate = false) => {
  console.log('🔍 开始处理排班结果', result)
  
  // 先进行数据修复，再进行验证
  console.log('🔧 开始数据修复...')
  
  // 确保考官数据缓存已初始化
  if (!cachedTeacherData) {
    console.log('🔄 初始化考官数据缓存.')
    try {
      cachedTeacherData = await prepareTeacherData()
      console.log('考官数据缓存初始化完成，缓存数量:', cachedTeacherData.length)
    } catch (error) {
      console.error('初始化考官数据缓存失败', error)
      cachedTeacherData = []
    }
  }
  
  // 应用前端显示修复器，确保数据格式一致
  console.log('🔧 应用前端显示修复器.')
  try {
    // 保持Teacher对象不变，让getTeacherNameById函数处理
    // 不要在这里转换为字符串，因为getTeacherNameById函数已经能正确处理Teacher对象
    console.log('🔍 检查assignments中的教师数据结构:')
    result.assignments.forEach((assignment: any, index: number) => {
      if (index < 3) { // 只打印前3个用于调试
        console.log(`Assignment ${index}:`, {
          examiner1: assignment.examiner1,
          examiner2: assignment.examiner2,
          backupExaminer: assignment.backupExaminer
        })
      }
    })
    
    const fixedResult = FrontendDisplayFixer.fixScheduleResultDisplay(result, cachedTeacherData || [])
    console.log('数据格式修复完成')
    result = fixedResult as any
  } catch (error) {
    console.error('数据格式修复失败:', error)
    // 继续使用原始数据，但记录错误
  }
  
  // 增强数据验证
  if (!result) {
    console.error('排班结果为空或undefined')
    schedulingError.value = '排班结果数据为空，请重试'
    return
  }
  
  if (!result.assignments) {
    console.error('排班结果缺少assignments字段')
    schedulingError.value = '排班结果格式错误：缺少assignments数据'
    return
  }
  
  if (!Array.isArray(result.assignments)) {
    console.error('assignments不是数组格式:', typeof result.assignments)
    schedulingError.value = '排班结果格式错误：assignments数据格式不正确'
    return
  }
  
  console.log('🔍 算法返回的assignments数量:', result.assignments.length)
  
  // 验证每个assignment的数据完整性
  const validAssignments = result.assignments.filter((assignment, index) => {
    if (!assignment) {
      console.warn(`⚠️ ${index + 1}个assignment为空`)
      return false
    }
    
    if (!assignment.studentId || !assignment.studentName) {
      console.warn(`⚠️ ${index + 1}个assignment缺少学员信息:`, assignment)
      return false
    }
    
    return true
  })
  
  console.log(`有效的assignments数量: ${validAssignments.length}/${result.assignments.length}`)
  
  // 🔍 添加学员数据分析，检查是否有学员丢失
  const originalStudentIds = new Set()
  const assignedStudentIds = new Set()
  
  // 统计原始学员数据
  studentList.value.forEach(student => {
    originalStudentIds.add(student.id.toString())
  })
  
  // 统计assignments中的学员
  validAssignments.forEach(assignment => {
    assignedStudentIds.add(assignment.studentId.toString())
  })
  
  console.log('🔍 学员数据分析:')
  console.log(`📊 原始学员数量: ${originalStudentIds.size}`)
  console.log(`📊 assignments中的学员数量: ${assignedStudentIds.size}`)
  console.log(`📊 原始学员ID列表:`, Array.from(originalStudentIds))
  console.log(`📊 assignments中的学员ID列表:`, Array.from(assignedStudentIds))
  
  // 检查丢失的学员
  const missingStudents: any[] = []
  originalStudentIds.forEach(studentId => {
    if (!assignedStudentIds.has(studentId)) {
      const student = studentList.value.find(s => s.id.toString() === studentId)
      if (student) {
        missingStudents.push(student)
      }
    }
  })
  
  if (missingStudents.length > 0) {
    console.warn(`⚠️ 发现${missingStudents.length}名学员在排班结果中缺失:`)
    missingStudents.forEach(student => {
      console.warn(`❌ 缺失学员: ${student.name} (ID: ${student.id}, 科室: ${student.department})`)
    })
    
    // 🔧 自动添加缺失学员到结果中，标记为"未安排"
    console.log('🔧 自动添加缺失学员到结果表格中...')
  }
  
  // 如果没有有效的assignments，显示详细错误信息
  if (validAssignments.length === 0) {
    console.error('❌ 没有有效的排班分配数据')
    console.log('🔍 原始assignments数据:', result.assignments)
    console.log('🔍 过滤条件检查:')
    result.assignments.forEach((assignment, index) => {
      console.log(`Assignment ${index}:`, {
        hasStudentId: !!assignment.studentId,
        hasStudentName: !!assignment.studentName,
        hasExamDate: !!assignment.examDate,
        hasExamType: !!assignment.examType,
        assignment
      })
    })
    schedulingError.value = '排班数据验证失败：没有有效的分配记录'
    return
  }
  
  // 清空旧数据
  scheduleResults.value = []
  
  // 将排班结果转换为表格数据格式
  const newResults: ScheduleResultRow[] = []
  
  // 按学员分组排班结果
  const studentExams = new Map<string, {
    studentName: string
    studentDepartment: string
    day1: any | null
    day2: any | null
  }>()
  
  // 使用验证过的assignments数据
  validAssignments.forEach((assignment, index) => {
    // 增强assignment数据验证
    try {
      console.log(`🔍 处理${index + 1}个assignment:`, assignment)
      console.log(`🔍 详细examiner数据:`, {
        examiner1_type: typeof assignment.examiner1,
        examiner1_value: assignment.examiner1,
        examiner2_type: typeof assignment.examiner2,
        examiner2_value: assignment.examiner2,
        backupExaminer_type: typeof assignment.backupExaminer,
        backupExaminer_value: assignment.backupExaminer
      })
      
      // 修复assignment.id为null的问题
      if (!assignment.id) {
        console.warn(`⚠️ Assignment ID为null，使用索引生成ID: ${index + 1}`)
        assignment.id = `assignment_${index + 1}_${assignment.studentId}`
      }
      
      // 修复examDate为null的问题 - ❌ 已禁用：不再覆盖后端的日期分配
      // ⚠️ 问题：这段代码会用前端的默认日期覆盖后端OptaPlanner的智能日期选择
      // ⚠️ 导致无论后端计算出什么日期，前端总是显示10-10 & 10-11
      if ((!assignment.examDate || assignment.examDate === '' || assignment.examDate === 'null')) {
        console.error(`❌ 后端未返回考试日期！assignment:`, assignment)
        console.error(`❌ 这是后端数据问题，请检查OptaPlanner输出`)
        // 不再强制分配日期，让错误暴露出来
      } else if (assignment.examDate) {
        console.log(`✅ 使用后端分配的日期: ${assignment.studentName} -> ${assignment.examDate}`)
      }
      
      /* 
      // 原日期强制分配逻辑（已禁用）
      if ((!assignment.examDate || assignment.examDate === '' || assignment.examDate === 'null') && examStartDate.value && examEndDate.value) {
        // 使用现有的generateExamDateRange函数生成可用工作日
        const startDate = new Date(examStartDate.value)
        const endDate = new Date(examEndDate.value)
        const availableDates = generateExamDateRange(startDate, endDate)
        
        console.log(`📅 生成考试日期范围: ${examStartDate.value} 到 ${examEndDate.value}`)
        console.log(`📊 可用工作日数量: ${availableDates.length} 天`)
        console.log(`📋 具体日期: ${availableDates.join(', ')}`)
        
        // 🔧 修复：确保同一学员的两次考试分配到连续的不同日期
        const studentIndex = Math.floor(index / 2) // 每个学员有两次考试
        const examIndex = index % 2 // 0=第一天，1=第二天
        
        // 确保有足够的连续日期对
        const baseDateIndex = Math.min(studentIndex * 2, availableDates.length - 2)
        let assignedDate
        
        if (examIndex === 0) {
          // 第一天考试
          assignedDate = availableDates[baseDateIndex] || availableDates[0]
        } else {
          // 第二天考试，必须是第一天的下一个工作日
          assignedDate = availableDates[baseDateIndex + 1] || availableDates[Math.min(baseDateIndex + 1, availableDates.length - 1)]
        }
        
        assignment.examDate = assignedDate
        console.log(`🔧 强制分配${assignment.studentName}的考试日期: ${assignment.examDate} (学员索引:${studentIndex}, 考试序号:${examIndex})`)
      }
      */
      
      // 修复examType识别问题
      let examType = assignment.examType
      if (!examType || (examType !== 'day1' && examType !== 'day2')) {
        // 根据考试日期推断examType
        if (assignment.examDate && examStartDate.value) {
          const examDate = new Date(assignment.examDate)
          const startDate = new Date(examStartDate.value)
          const daysDiff = Math.floor((examDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
          
          if (daysDiff === 0) {
            examType = 'day1'
            console.log(`🔧 根据日期推断${assignment.studentName}为第一天考试`)
          } else if (daysDiff === 1) {
            examType = 'day2'
            console.log(`🔧 根据日期推断${assignment.studentName}为第二天考试`)
          } else {
            // 如果无法推断，根据学员在数组中的位置分配
            examType = (index % 2 === 0) ? 'day1' : 'day2'
            console.log(`🔧 根据索引推断${assignment.studentName}为${examType}考试`)
          }
          assignment.examType = examType
        } else {
          console.error(`❌ Assignment缺少examDate和examType: ${assignment.studentName}`)
          examType = (index % 2 === 0) ? 'day1' : 'day2' // 根据索引分配
          assignment.examType = examType
        }
      }
      
      // 修复备份考官为null的问题 - 改进分配逻辑
      if ((!assignment.backupExaminer || assignment.backupExaminer === '' || assignment.backupExaminer === 'null') && cachedTeacherData && cachedTeacherData.length > 0) {
        // 获取已分配的考官，避免重复 - 支持字符串和对象格式
        const assignedExaminers = new Set()
        if (assignment.examiner1) {
          const examiner1Id = typeof assignment.examiner1 === 'string'
            ? assignment.examiner1
            : (assignment.examiner1 && typeof assignment.examiner1 === 'object' && 'id' in assignment.examiner1)
              ? (assignment.examiner1 as { id?: string | number }).id
              : undefined
          if (examiner1Id) assignedExaminers.add(examiner1Id)
        }
        if (assignment.examiner2) {
          const examiner2Id = typeof assignment.examiner2 === 'string'
            ? assignment.examiner2
            : (assignment.examiner2 && typeof assignment.examiner2 === 'object' && 'id' in assignment.examiner2)
              ? (assignment.examiner2 as { id?: string | number }).id
              : undefined
          if (examiner2Id) assignedExaminers.add(examiner2Id)
        }
        
        // 优先选择不同科室的考官作为备份考官
        let availableBackup = cachedTeacherData.find(teacher => 
          !assignedExaminers.has(teacher.id) && 
          !assignedExaminers.has(teacher.name) && // 也检查姓名
          teacher.department !== assignment.studentDepartment
        )
        
        // 如果没有不同科室的，就选择任意未被分配的考官
        if (!availableBackup) {
          availableBackup = cachedTeacherData.find(teacher => 
            !assignedExaminers.has(teacher.id) && 
            !assignedExaminers.has(teacher.name)
          )
        }
        
        // 如果还是没有，使用智能均衡分配，确保备份考官分配均匀
        if (!availableBackup) {
          // 🔧 修复：彻底解决备份考官过度分配问题
          // 统计每个考官在当前日期被分配为备份考官的次数
          const currentDateBackupCount = new Map()
          const currentDate = assignment.examDate
          
          result.assignments.forEach((assign: any) => {
            if (assign.backupExaminer && assign.examDate === currentDate) {
              const name = typeof assign.backupExaminer === 'string' ? assign.backupExaminer : assign.backupExaminer.name
              if (name) {
                currentDateBackupCount.set(name, (currentDateBackupCount.get(name) || 0) + 1)
              }
            }
          })
          
          // 找到在当前日期被分配次数最少且可用的考官作为备份考官
          let minCount = Infinity
          let selectedTeacher = null
          
          for (const teacher of cachedTeacherData) {
            // 跳过已经被分配为考官1或考官2的考官（同一场考试不能兼任）
            if (assignedExaminers.has(teacher.id) || assignedExaminers.has(teacher.name)) {
              continue
            }
            
            // 检查科室约束：备份考官最好来自不同科室
            const studentDept = assignment.studentDepartment || '未知'
            const teacherDept = teacher.department || '未知'
            
            // 优先选择不同科室的考官作为备份考官
            const isDifferentDept = !teacherDept.includes(studentDept) && !studentDept.includes(teacherDept)
            
            const currentCount = currentDateBackupCount.get(teacher.name) || 0
            
            // 优先级：1. 不同科室且分配次数少 2. 分配次数少
            const priority = isDifferentDept ? currentCount - 0.5 : currentCount
            
            if (priority < minCount) {
              minCount = priority
              selectedTeacher = teacher
            }
          }
          
          // 如果找到了合适的考官，使用它
          if (selectedTeacher) {
            availableBackup = selectedTeacher
            const actualCount = currentDateBackupCount.get(selectedTeacher.name) || 0
            console.log(`🎯 均衡分配备份考官: ${selectedTeacher.name} (${selectedTeacher.department}) 当前日期分配次数: ${actualCount}`)
          } else {
            // 备用方案：使用轮询，但确保均衡分配
            const sortedTeachers = [...cachedTeacherData].sort((a, b) => {
              const countA = currentDateBackupCount.get(a.name) || 0
              const countB = currentDateBackupCount.get(b.name) || 0
              return countA - countB
            })
            
            // 选择被分配次数最少的考官
            availableBackup = sortedTeachers[0]
            const actualCount = currentDateBackupCount.get(availableBackup.name) || 0
            console.log(`🔄 轮询分配备份考官: ${availableBackup.name} (当前日期分配次数: ${actualCount})`)
          }
        }
        
        if (availableBackup) {
          // @ts-ignore - backupExaminer可以是string或对象，运行时会正确处理
          assignment.backupExaminer = availableBackup
          console.log(`🔧 智能分配${assignment.studentName}的备份考官: ${availableBackup.name} (${availableBackup.department})`)
        }
      }
      
      const studentId = assignment.studentId
      
      if (!studentExams.has(studentId)) {
        studentExams.set(studentId, {
          studentName: assignment.studentName,
          studentDepartment: assignment.studentDepartment,
          day1: null,
          day2: null
        })
      }
      
      const studentData = studentExams.get(studentId)!
      
      if (examType === 'day1') {
        studentData.day1 = assignment
        console.log(`✅ 设置${assignment.studentName}的第一天考试`)
      } else if (examType === 'day2') {
        studentData.day2 = assignment
        console.log(`✅ 设置${assignment.studentName}的第二天考试`)
      } else {
        console.warn(`⚠️ 未知的考试类型: ${examType}，强制分配到day1`)
        studentData.day1 = assignment
      }
      
    } catch (error) {
      console.error(`处理${index + 1}个assignment时出错`, error, assignment)
    }
  })
  
  console.log(`🔍 按学员分组后，共${studentExams.size}名学员`)
  
  // 🔧 添加备份考官工作量统计和验证
  const backupWorkloadStats = new Map()
  result.assignments.forEach((assignment: any) => {
    if (assignment.backupExaminer) {
      const name = typeof assignment.backupExaminer === 'string' ? assignment.backupExaminer : assignment.backupExaminer.name
      if (name) {
        backupWorkloadStats.set(name, (backupWorkloadStats.get(name) || 0) + 1)
      }
    }
  })
  
  console.log('📊 备份考官工作量统计:')
  Array.from(backupWorkloadStats.entries())
    .sort((a, b) => b[1] - a[1]) // 按工作量降序排列
    .forEach(([name, count]) => {
      const level = count > 5 ? '🔴' : count > 3 ? '🟡' : '🟢'
      console.log(`  ${level} ${name}: ${count}次 ${count > 5 ? '(过度分配)' : count > 3 ? '(适中)' : '(合理)'}`)
    })
  
  // 🔧 添加连续两天考试硬约束验证
  const validateConsecutiveDaysConstraint = () => {
    const violations: Array<{studentName: string, issue: string, severity: 'hard' | 'soft'}> = []
    
    studentExams.forEach((examData, studentId) => {
      if (examData.day1 && examData.day2) {
        const date1 = new Date(examData.day1.examDate)
        const date2 = new Date(examData.day2.examDate)
        const dayDiff = Math.abs((date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24))
        
        // 检查是否为连续两天（工作日）
        if (dayDiff !== 1) {
          violations.push({
            studentName: examData.studentName,
            issue: `考试日期不连续：第一天=${examData.day1.examDate}, 第二天=${examData.day2.examDate}, 间隔=${dayDiff}天`,
            severity: 'hard'
          })
          console.error(`❌ 硬约束违反: ${examData.studentName}的考试日期不连续`)
        } else {
          console.log(`✅ ${examData.studentName}的考试日期符合连续两天要求`)
        }
      } else {
        violations.push({
          studentName: examData.studentName,
          issue: `缺少完整的两天考试安排: day1=${examData.day1 ? '已安排' : '缺失'}, day2=${examData.day2 ? '已安排' : '缺失'}`,
          severity: 'hard'
        })
      }
    })
    
    if (violations.length > 0) {
      console.error('🚨 发现硬约束违反:', violations)
      // 可以选择在这里抛出错误或显示警告
    }
    
    return violations
  }
  
  // 🔧 添加学员白班不参加考试硬约束验证
  const validateStudentDayShiftConstraint = () => {
    const violations: Array<{studentName: string, issue: string, severity: 'hard' | 'soft', day: number}> = []
    
    studentExams.forEach((examData, studentId) => {
      [examData.day1, examData.day2].forEach((exam, dayIndex) => {
        if (exam && exam.examDate) {
          // 这里需要检查学员在考试日期是否执勤白班
          // 由于缺少具体的轮值信息，我们先添加基础验证框架
          const examDate = new Date(exam.examDate)
          const dayOfWeek = examDate.getDay()
          
          // 基础验证：确保不在周末安排考试（除非特殊情况）
          if (dayOfWeek === 0 || dayOfWeek === 6) {
            violations.push({
              studentName: examData.studentName,
              issue: `考试安排在周末：${exam.examDate} (星期${dayOfWeek === 0 ? '日' : '六'})`,
              severity: 'soft',
              day: dayIndex + 1
            })
            console.warn(`⚠️ 软约束违反: ${examData.studentName}在第${dayIndex + 1}天的考试安排在周末`)
          }
          
          // 🔍 检查学员班组是否在该日期执勤白班
          try {
            // 获取该日期的值班安排
            const examDateObj = new Date(exam.examDate)
            const dateStr = examDateObj.toISOString().split('T')[0] // YYYY-MM-DD格式
            
            // 简化的白班检查逻辑（基于现有的值班计算）
            const dayOfWeek = examDateObj.getDay() // 0=周日, 1=周一, ..., 6=周六
            
            // 基于四班倒轮值规律进行简单检查
            // 这里需要根据实际的轮值规律进行调整
            const studentGroup = examData.studentName.includes('一') ? '一组' : 
                                examData.studentName.includes('二') ? '二组' : 
                                examData.studentName.includes('三') ? '三组' : 
                                examData.studentName.includes('四') ? '四组' : '未知组'
            
            // 模拟白班检查（实际应该调用dutyRotationService）
            if (studentGroup !== '未知组') {
              // 这里应该有更精确的轮值检查逻辑
              console.log(`🔍 检查${examData.studentName}(${studentGroup})在${exam.examDate}的值班状态`)
              
              // 如果检测到可能的白班冲突，添加到违反列表
              // 暂时使用简化逻辑：工作日且特定条件下认为可能是白班
              if (dayOfWeek >= 1 && dayOfWeek <= 5) { // 工作日
                violations.push({
                  studentName: examData.studentName,
                  issue: `需要验证${exam.examDate}是否为${studentGroup}白班执勤日`,
                  severity: 'soft', // 标记为软约束，需要人工确认
                  day: dayIndex + 1
                })
                console.warn(`⚠️ 需要确认: ${examData.studentName}在第${dayIndex + 1}天(${exam.examDate})的白班状态`)
              }
            }
          } catch (error) {
            console.error(`检查${examData.studentName}白班状态时出错:`, error)
          }
        }
      })
    })
    
    return violations
  }
  
  // 🔧 添加硬约束违反自动修复逻辑
  const fixConsecutiveDaysViolations = () => {
    const availableDates = generateExamDateRange(new Date(examStartDate.value!), new Date(examEndDate.value!))
    let fixedCount = 0
    
    // 收集已使用的日期对，避免冲突
    const usedDatePairs = new Set<string>()
    
    // 先收集正确的日期对
    studentExams.forEach((examData, studentId) => {
      if (examData.day1 && examData.day2) {
        const date1 = new Date(examData.day1.examDate)
        const date2 = new Date(examData.day2.examDate)
        const dayDiff = Math.abs((date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24))
        
        if (dayDiff === 1) {
          // 记录正确的日期对
          const pair = `${examData.day1.examDate}-${examData.day2.examDate}`
          usedDatePairs.add(pair)
        }
      }
    })
    
    // 修复不正确的日期分配
    studentExams.forEach((examData, studentId) => {
      if (examData.day1 && examData.day2) {
        const date1 = new Date(examData.day1.examDate)
        const date2 = new Date(examData.day2.examDate)
        const dayDiff = Math.abs((date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24))
        
        // 如果日期不连续或相同，自动修复
        if (dayDiff !== 1) {
          console.warn(`🔧 自动修复${examData.studentName}的考试日期问题: day1=${examData.day1.examDate}, day2=${examData.day2.examDate}, 间隔=${dayDiff}天`)
          
          // 找到一个未使用的连续日期对
          let foundPair = false
          for (let i = 0; i < availableDates.length - 1; i++) {
            const firstDate = availableDates[i]
            const secondDate = availableDates[i + 1]
            
            // 检查这两个日期是否连续
            const d1 = new Date(firstDate)
            const d2 = new Date(secondDate)
            const diff = (d2.getTime() - d1.getTime()) / (1000 * 60 * 60 * 24)
            
            if (diff === 1) {
              const pair = `${firstDate}-${secondDate}`
              
                                            // 🔧 HC6约束检查：学员不能在其白班日期参加考试
               // 从studentList.value中查找学员班组信息
               const student = studentList.value.find((s: any) => s.name === examData.studentName)
               const studentGroup = student?.group
               let hasHC6Violation = false
               
               if (studentGroup) {
                // 检查两个日期是否违反HC6约束
                for (const checkDate of [firstDate, secondDate]) {
                  const baseDate = new Date('2025-09-04')
                  const targetDate = new Date(checkDate)
                  const daysDiff = Math.floor((targetDate.getTime() - baseDate.getTime()) / (1000 * 60 * 60 * 24))
                  const cyclePosition = ((daysDiff % 4) + 4) % 4
                  
                  const schedules = [
                    { dayShift: '二组', nightShift: '一组' },  // 位置0
                    { dayShift: '三组', nightShift: '二组' },  // 位置1
                    { dayShift: '四组', nightShift: '三组' },  // 位置2
                    { dayShift: '一组', nightShift: '四组' }   // 位置3
                  ]
                  
                  const dayShift = schedules[cyclePosition].dayShift
                  if (studentGroup === dayShift) {
                    console.warn(`🚨 HC6违反: ${examData.studentName}(${studentGroup}) 不能在 ${checkDate}(${dayShift}白班) 参加考试`)
                    hasHC6Violation = true
                    break
                  }
                }
              }
              
              // 检查这个日期对是否已被使用且不违反HC6约束
              if (!usedDatePairs.has(pair) && !hasHC6Violation) {
                // 分配给学员并标记为已使用
                examData.day1.examDate = firstDate
                examData.day2.examDate = secondDate
                usedDatePairs.add(pair)
                console.log(`✅ 修复${examData.studentName}考试日期: ${firstDate} -> ${secondDate} (HC6检查通过)`)
                fixedCount++
                foundPair = true
                break
              } else if (hasHC6Violation) {
                console.log(`⚠️ 跳过${examData.studentName}的日期对 ${firstDate}-${secondDate} (HC6约束冲突)`)
              }
            }
          }
          
          if (!foundPair) {
            console.error(`❌ 无法为${examData.studentName}找到可用的连续日期对`)
          }
        }
      }
    })
    
    return fixedCount
  }
  
  // 执行硬约束验证
  const localConstraintViolations = validateConsecutiveDaysConstraint()
  const dayShiftViolations = validateStudentDayShiftConstraint()
  
  // 🔧 新增：验证HC2科室匹配约束
const departmentViolations: any[] = [] // validateDepartmentMatchingConstraint(scheduleResults.value)

// 🔧 新增：验证HC4考官时间冲突约束  
const timeConflictViolations: any[] = [] // validateExaminerTimeConflictConstraint(scheduleResults.value)
  
  // 如果发现硬约束违反，尝试自动修复 - ❌ 已禁用
  // ⚠️ 问题：这个功能会覆盖后端OptaPlanner的权威结果
  // ⚠️ 后端可能故意分配不连续的日期（例如分散日期以均衡负载）
  // ⚠️ 前端不应该擅自"修复"后端的决策
  console.warn('⚠️ [禁用] 前端自动修复功能已禁用，完全信任后端OptaPlanner结果')
  
  const allConstraintViolations = [...localConstraintViolations, ...dayShiftViolations, ...departmentViolations, ...timeConflictViolations]
  
  /*
  // 原自动修复逻辑（已禁用）
  const hardViolations = allConstraintViolations.filter(v => v.severity === 'hard')
  if (hardViolations.length > 0) {
    console.log('🔧 发现硬约束违反，尝试自动修复...')
    const fixedCount = fixConsecutiveDaysViolations()
    console.log(`✅ 自动修复了${fixedCount}个硬约束违反`)
    
    // 重新验证
    const revalidationViolations = validateConsecutiveDaysConstraint()
    const remainingHardViolations = revalidationViolations.filter(v => v.severity === 'hard')
    if (remainingHardViolations.length === 0) {
      console.log('🎉 所有硬约束违反已修复！')
    } else {
      console.warn('⚠️ 仍有硬约束违反无法自动修复:', remainingHardViolations)
    }
  }
  */
  
  // 🔧 修复：直接使用后端OptaPlanner权威结果，避免前端约束验证差异
  console.log('🔗 [约束同步] 使用后端OptaPlanner权威约束结果')
  
  // 🔍 添加诊断日志
  console.log('🔍 [诊断] ===== 开始约束得分解析 =====')
  console.log('🔍 [诊断] result.score:', result.score)
  console.log('🔍 [诊断] result.score类型:', typeof result.score)
  console.log('🔍 [诊断] result.statistics:', result.statistics)
  console.log('🔍 [诊断] result.statistics.hardConstraintViolations:', result.statistics?.hardConstraintViolations)
  console.log('🔍 [诊断] result.statistics.softConstraintsScore:', result.statistics?.softConstraintsScore)
  
  // 🎯 检查后端返回的硬约束状态
  let backendHardScore = 0
  let backendSoftScore = 0
  
  // ✨ 改进的得分解析逻辑，支持多种格式
  // 尝试从多个可能的位置获取得分
  if (result.score) {
    // 情况1：score是对象格式 {hardScore: -16000, softScore: 84850}
    if (typeof result.score === 'object' && result.score.hardScore !== undefined) {
    backendHardScore = result.score.hardScore || 0
    backendSoftScore = result.score.softScore || 0
      console.log('🔍 [诊断] 从result.score对象获取:', { backendHardScore, backendSoftScore })
    }
    // 情况2：score是字符串格式 "-16000hard/84850soft"
    else if (typeof result.score === 'string') {
      const match = result.score.match(/(-?\d+)hard\/(-?\d+)soft/)
      if (match) {
        backendHardScore = parseInt(match[1])
        backendSoftScore = parseInt(match[2])
        console.log('🔍 [诊断] 从result.score字符串解析:', { backendHardScore, backendSoftScore })
      }
    }
  }
  
  // 备用方案：从statistics获取
  if (backendHardScore === 0 && backendSoftScore === 0 && result.statistics) {
    // 尝试从hardConstraintViolations和softConstraintsScore获取
    if (result.statistics.hardConstraintViolations !== undefined) {
      backendHardScore = -Math.abs(result.statistics.hardConstraintViolations)
      console.log('🔍 [诊断] 从statistics.hardConstraintViolations获取:', backendHardScore)
    }
    if (result.statistics.softConstraintsScore !== undefined) {
      backendSoftScore = result.statistics.softConstraintsScore
      console.log('🔍 [诊断] 从statistics.softConstraintsScore获取:', backendSoftScore)
    }
    
    // 尝试从finalScore获取
    if (backendHardScore === 0 && result.statistics.finalScore) {
      if (typeof result.statistics.finalScore === 'object') {
    backendHardScore = result.statistics.finalScore.hardScore || 0
    backendSoftScore = result.statistics.finalScore.softScore || 0
        console.log('🔍 [诊断] 从statistics.finalScore对象获取:', { backendHardScore, backendSoftScore })
      } else if (typeof result.statistics.finalScore === 'string') {
        const match = result.statistics.finalScore.match(/(-?\d+)hard\/(-?\d+)soft/)
        if (match) {
          backendHardScore = parseInt(match[1])
          backendSoftScore = parseInt(match[2])
          console.log('🔍 [诊断] 从statistics.finalScore字符串解析:', { backendHardScore, backendSoftScore })
        }
      }
    }
  }
  
  console.log('🔍 [诊断] ===== 结束约束得分解析 =====')
  console.log(`📊 [约束同步] 后端约束状态: 硬约束=${backendHardScore}, 软约束=${backendSoftScore}`)
  
  // 清空前端验证结果，使用后端权威数据
  allConstraintViolations.length = 0
  
  if (backendHardScore < 0) {
    // 后端存在硬约束违反，添加通用违反提示
    allConstraintViolations.push({
      studentName: '系统检测',
      issue: `后端检测到硬约束违反 (硬约束得分: ${backendHardScore})`,
      severity: 'hard',
      constraintId: 'BACKEND_HARD_VIOLATION',
      examDate: new Date().toISOString().split('T')[0],
      violationType: 'system'
    })
    console.log('⚠️ [约束同步] 后端检测到硬约束违反')
  } else {
    // 🔧 确保清空constraintViolations显示
    constraintViolations.value = []
    console.log('✅ [约束同步] 后端无硬约束违反，前端约束列表已清空')
  }

  // 🔧 HC6本地验证：检查学员是否在其白班日期参加考试
  console.log('🔧 开始HC6本地验证...')
  
  function calculateDutySchedule(dateStr: string) {
    const baseDate = new Date('2025-09-04')
    const targetDate = new Date(dateStr)
    const daysDiff = Math.floor((targetDate.getTime() - baseDate.getTime()) / (1000 * 60 * 60 * 24))
    const cyclePosition = ((daysDiff % 4) + 4) % 4
    
    const schedules = [
      { dayShift: '二组', nightShift: '一组' },  // 位置0
      { dayShift: '三组', nightShift: '二组' },  // 位置1
      { dayShift: '四组', nightShift: '三组' },  // 位置2
      { dayShift: '一组', nightShift: '四组' }   // 位置3
    ]
    
    return schedules[cyclePosition]
  }
  
  for (const result of scheduleResults.value) {
    const resultAny = result as any
    const studentGroup = resultAny.学员信息?.班组
    if (!studentGroup) continue
    
    for (const day of ['第一天', '第二天']) {
      const dayInfo = resultAny[day]
      if (!dayInfo?.考试日期) continue
      
      const dutySchedule = calculateDutySchedule(dayInfo.考试日期)
      const isStudentOnDayShift = studentGroup === dutySchedule.dayShift
      
      console.log(`🔍 HC6验证: ${resultAny.学员信息.姓名} (${studentGroup}) 在 ${dayInfo.考试日期}`)
      console.log(`🔍 白班班组: ${dutySchedule.dayShift}, 学员班组: ${studentGroup}, 是否白班: ${isStudentOnDayShift}`)
      
      if (isStudentOnDayShift) {
        console.error(`🚨 HC6违反: 学员 ${resultAny.学员信息.姓名} (${studentGroup}) 在白班日期 ${dayInfo.考试日期} 参加考试!`)
        allConstraintViolations.push({
          studentName: resultAny.学员信息.姓名,
          issue: `学员在白班执勤日(${dayInfo.考试日期})参加考试，违反HC6约束`,
          severity: 'hard',
          constraintId: 'HC6',
          examDate: dayInfo.考试日期,
          violationType: 'day_shift_conflict'
        })
      } else {
        console.log(`✅ HC6合规: 学员 ${resultAny.学员信息.姓名} 在 ${dayInfo.考试日期} 非白班执勤`)
      }
    }
  }
  
  console.log('✅ HC6本地验证完成')

  // 合并所有约束违反（现在可能包含后端同步的结果）
  const allViolations = allConstraintViolations
  if (allViolations.length > 0) {
    console.error('🚨 发现约束违反问题!')
    console.log('📊 约束验证结果:', {
      总违反数: allViolations.length,
      硬约束违反: allViolations.filter(v => v.severity === 'hard').length,
      软约束违反: allViolations.filter(v => v.severity === 'soft').length,
      详细信息: allViolations
    })
    
    // 显示每个违反的详细信息
    allViolations.forEach((violation, index) => {
      const severity = violation.severity === 'hard' ? '🚨 硬约束' : '⚠️ 软约束'
      console.log(`${severity}违反 #${index + 1}: ${violation.studentName} - ${violation.issue}`)
    })
  } else {
    console.log('✅ 所有约束验证通过，无违反情况')
  }
  
  // 转换为表格格子
  studentExams.forEach((examData, studentId) => {
    let day1 = examData.day1
    let day2 = examData.day2
    
    // 确保day1是较早的日期，day2是较晚的日期
    if (day1 && day2) {
      console.log(`🔍 ${examData.studentName}原始日期: day1=${day1.examDate}, day2=${day2.examDate}`)
      
      const date1 = new Date(day1.examDate)
      const date2 = new Date(day2.examDate)
      
      console.log(`🔍 ${examData.studentName}解析后日期: date1=${date1.toISOString()}, date2=${date2.toISOString()}`)
      console.log(`🔍 ${examData.studentName}时间戳比: date1=${date1.getTime()}, date2=${date2.getTime()}`)
      
      // 如果day1的日期晚于day2，则交换它们
      if (date1.getTime() > date2.getTime()) {
        console.log(`🔄 ${examData.studentName}需要交换日期顺序`)
        // 交换两天的考试安排
        const temp = day1;
        day1 = day2;
        day2 = temp;
        console.log(`🔄 ${examData.studentName}交换日期: day1=${day1?.examDate || 'null'}, day2=${day2?.examDate || 'null'}`)
      } else {
        console.log(`${examData.studentName}日期顺序正确，无需交换`)
      }
    }
    
    console.log(`🔍 处理学员${examData.studentName}的显示数据`, {
       day1: day1 ? {
         examDate: day1.examDate,
         examiner1: day1.examiner1,
         examiner2: day1.examiner2,
         backupExaminer: day1.backupExaminer
       } : null,
       day2: day2 ? {
         examDate: day2.examDate,
         examiner1: day2.examiner1,
         examiner2: day2.examiner2,
         backupExaminer: day2.backupExaminer
       } : null
     })
    
    // 增强数据转换的安全性
    try {
      // 转换考官姓名
      let examiner1_1 = day1 ? getTeacherNameById(day1.examiner1) : '未分组'
      let examiner1_2 = day1 ? getTeacherNameById(day1.examiner2) : '未分组'
      const backup1 = day1 ? getTeacherNameById(day1.backupExaminer) : '未分组'
      let examiner2_1 = day2 ? getTeacherNameById(day2.examiner1) : '未分组'
      let examiner2_2 = day2 ? getTeacherNameById(day2.examiner2) : '未分组'
      const backup2 = day2 ? getTeacherNameById(day2.backupExaminer) : '未分组'
      
      // 详细调试考官转换结果
      console.log(`🔍 ${examData.studentName}考官转换结果:`, {
        day1_examiner1_raw: day1?.examiner1,
        day1_examiner2_raw: day1?.examiner2,
        day1_examiner1_converted: examiner1_1,
        day1_examiner2_converted: examiner1_2,
        day2_examiner1_raw: day2?.examiner1,
        day2_examiner2_raw: day2?.examiner2,
        day2_examiner1_converted: examiner2_1,
        day2_examiner2_converted: examiner2_2
      })
      
      // 强化数据验证：检查考官重复和缺失问题
      const validationErrors: string[] = []
      
      // 检查第一天考官配备
      if (examiner1_1 === '未分组' || examiner1_2 === '未分组') {
        validationErrors.push(`${examData.studentName}第一天缺少主考官配备`)
      }
      
      if (examiner1_1 === examiner1_2 && examiner1_1 !== '未分组') {
        console.error(`约束违反: ${examData.studentName}第一天考官1和考官2相同`, {
          examiner1_1,
          examiner1_2,
          day1_examiner1_raw: day1?.examiner1,
          day1_examiner2_raw: day1?.examiner2
        })
        
        validationErrors.push(`${examData.studentName}第一天考官重复分配`)
        
        // 智能修复：尝试从可用考官中重新分配考官2
        if (examiner1_1 !== '未分组') {
          console.warn(`🔧 智能修复${examData.studentName}第一天考官重复问题：重新分配考官2`)
          const alternativeExaminer = findAlternativeExaminer(examiner1_1, examData.studentDepartment)
          examiner1_2 = alternativeExaminer || '重新分配失败'
          console.log(`🔧 考官2重新分配结果: ${examiner1_2}`)
        }
      }
      
      // 检查第二天考官配备
      if (examiner2_1 === '未分组' || examiner2_2 === '未分组') {
        validationErrors.push(`${examData.studentName}第二天缺少主考官配备`)
      }
      
      if (examiner2_1 === examiner2_2 && examiner2_1 !== '未分组') {
        console.error(`约束违反: ${examData.studentName}第二天考官1和考官2相同`, {
          examiner2_1,
          examiner2_2,
          day2_examiner1_raw: day2?.examiner1,
          day2_examiner2_raw: day2?.examiner2
        })
        
        validationErrors.push(`${examData.studentName}第二天考官重复分配`)
        
        // 智能修复：尝试从可用考官中重新分配考官2
        if (examiner2_1 !== '未分组') {
          console.warn(`🔧 智能修复${examData.studentName}第二天考官重复问题：重新分配考官2`)
          const alternativeExaminer = findAlternativeExaminer(examiner2_1, examData.studentDepartment)
          examiner2_2 = alternativeExaminer || '重新分配失败'
          console.log(`🔧 考官2重新分配结果: ${examiner2_2}`)
        }
      }
      
      // 记录验证错误用于后续处理
      if (validationErrors.length > 0) {
        console.warn(`⚠️ ${examData.studentName}数据验证发现问题:`, validationErrors)
      }
      
      const studentRecord = {
        id: parseInt(studentId) || 0,
        department: mapDepartmentName(examData.studentDepartment || '未知'), 
        student: examData.studentName || '未知学员',
        date1: day1 && day1.examDate ? formatDateFromString(day1.examDate) : '未安排',
        type1: day1 && day1.subjects ? day1.subjects.join('/') : '未安排',
        examiner1_1,
        examiner1_2,
        backup1,
        date2: day2 && day2.examDate ? formatDateFromString(day2.examDate) : '未安排',
        type2: day2 && day2.subjects ? day2.subjects.join('/') : '未安排',
        examiner2_1,
        examiner2_2,
        backup2
      }
      
      // 最终验证：确保记录中没有重复考官
      console.log(`🔍 ${examData.studentName}最终数据验证`, {
        day1: { examiner1: examiner1_1, examiner2: examiner1_2, backup: backup1 },
        day2: { examiner1: examiner2_1, examiner2: examiner2_2, backup: backup2 }
      })
      
      newResults.push(studentRecord)
      console.log(`完成学员${examData.studentName}的数据转换`)
      
    } catch (error) {
      console.error(`转换学员${examData.studentName}数据时出错`, error)
        // 添加错误记录，避免丢失学员信息
      newResults.push({
        id: parseInt(studentId) || 0,
        department: '数据错误',
        student: examData.studentName || '未知学员',
        date1: '数据错误',
        type1: '数据错误',
        examiner1_1: '数据错误',
        examiner1_2: '数据错误',
        backup1: '数据错误',
        date2: '数据错误',
        type2: '数据错误',
        examiner2_1: '数据错误',
        examiner2_2: '数据错误',
        backup2: '数据错误'
      })
    }
  })
  
  // 处理未分配的学员
  if (result.unassignedStudents && Array.isArray(result.unassignedStudents) && result.unassignedStudents.length > 0) {
    console.log(`⚠️ 发现${result.unassignedStudents.length}名未分配学员`)
    
    result.unassignedStudents.forEach((student, index) => {
      try {
        // 验证学员数据
        if (!student) {
          console.warn(`⚠️ ${index + 1}个未分配学员数据为空`)
          return
        }
        
        if (!student.name || !student.id) {
          console.warn(`⚠️ ${index + 1}个未分配学员缺少基本信息:`, student)
          return
        }
        
        console.log(`🔍 未分配学员${student.name}的数据`, {
          id: student.id,
          name: student.name,
          department: student.department,
          group: student.group
        })
        
        newResults.push({
          id: parseInt(student.id) || 0,
          department: mapDepartmentName(student.department || '未知'),
          student: student.name,
          date1: '未安排',
          type1: '未安排',
          examiner1_1: '未分组',
          examiner1_2: '未分组',
          backup1: '未分组',
          date2: '未安排',
          type2: '未安排',
          examiner2_1: '未分组',
          examiner2_2: '未分组',
          backup2: '未分组',
        })
        
      } catch (error) {
        console.error(`处理${index + 1}个未分配学员时出错`, error, student)
      }
    })
  }
  
  // 🔧 处理在assignments中缺失的学员（前面检测到的missingStudents）
  if (missingStudents.length > 0) {
    console.log(`🔧 添加${missingStudents.length}名缺失学员到结果表格中`)
    
    missingStudents.forEach((student, index) => {
      try {
        console.log(`🔧 添加缺失学员${student.name}到结果中`)
        
        newResults.push({
          id: parseInt(student.id) || 0,
          department: mapDepartmentName(student.department || '未知'),
          student: student.name,
          date1: '未安排',
          type1: '未安排',
          examiner1_1: '算法未分配',
          examiner1_2: '算法未分配',
          backup1: '算法未分配',
          date2: '未安排',
          type2: '未安排',
          examiner2_1: '算法未分配',
          examiner2_2: '算法未分配',
          backup2: '算法未分配',
        })
        
      } catch (error) {
        console.error(`添加缺失学员${student.name}时出错`, error, student)
      }
    })
    
    console.log(`✅ 已添加${missingStudents.length}名缺失学员，总记录数: ${newResults.length}`)
  }
  
  // 🔍 数据修复完成后进行验证
  console.log('🔍 开始数据验证...')
  const validationResult = DataValidationService.validateScheduleResult(result)
  
  if (validationResult.errors.length > 0) {
    console.error('数据验证发现错误:', validationResult.errors)
    const report = DataValidationService.generateValidationReport(validationResult)
    console.error('📋 验证报告:\n', report)
    
    // 如果错误都是考试日期缺失问题，且我们已经修复了，就不显示错误
    const onlyDateErrors = validationResult.errors.every(error => 
      (error as any).type === 'MISSING_EXAM_DATE'
    )
    
    if (onlyDateErrors) {
      console.log('✅ 所有错误都是考试日期问题，前端已自动修复')
    }
  }
  
  if (validationResult.warnings.length > 0) {
    console.warn('⚠️ 数据验证发现警告:', validationResult.warnings)
  }
  
  // 使用修复后的数据
  if (validationResult.fixedData) {
    console.log('🔧 使用数据验证服务修复后的数据')
    result = validationResult.fixedData
  }
  
  // 更新排班结果数据
  console.log('🔍 准备更新scheduleResults，newResults长度:', newResults.length)
  console.log('🔍 newResults详细内容:', newResults)
  
  // 验证数据转换结果
  newResults.forEach((result, index) => {
    console.log(`📋 ${index + 1}条记录 ${result.student} (${result.department})`, {
      第一天: {
        日期: result.date1,
        考官一: result.examiner1_1,
        考官二: result.examiner1_2,
        备份: result.backup1
      },
      第二天: {
        日期: result.date2,
        考官一: result.examiner2_1,
        考官二: result.examiner2_2,
        备份: result.backup2
      }
    })
  })
  
  // 按照第一天考试日期排序，确保日期按时间顺序显示
  newResults.sort((a, b) => {
    // 将日期字符串转换为Date对象进行比较
    const dateA = new Date(a.date1.replace(/^(\d+)\.(\d+)$/, '2025-$1-$2'))
    const dateB = new Date(b.date1.replace(/^(\d+)\.(\d+)$/, '2025-$1-$2'))
    return dateA.getTime() - dateB.getTime()
  })
  
  // 🎬 实时更新时使用动画渐进式显示数据
  if (isRealtimeUpdate) {
    console.log('🎬 [动态更新] 实时更新表格数据，模拟算法优化过程')
    
    const oldLength = scheduleResults.value.length
    const newLength = newResults.length
    
    if (oldLength === 0) {
      // 🆕 第一次显示，逐行添加
      console.log('🆕 首次渲染，逐行添加数据')
      for (let i = 0; i < newResults.length; i++) {
        scheduleResults.value.push(newResults[i])
        // 每添加一行延迟一下，创造流畅的视觉效果
        if (i < newResults.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
    } else if (newLength > oldLength) {
      // 📈 有新增行，先更新旧行，再添加新行
      console.log(`📈 数据增长: ${oldLength} → ${newLength}，更新旧行并添加新行`)
      
      // 更新已存在的行（模拟算法在调整考官和日期）
      for (let i = 0; i < oldLength; i++) {
        scheduleResults.value[i] = newResults[i]
      }
      await nextTick()
      
      // 添加新行
      for (let i = oldLength; i < newLength; i++) {
        scheduleResults.value.push(newResults[i])
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    } else {
      // 🔄 行数相同，直接替换数据（考官和日期会变化）
      console.log(`🔄 更新现有${newLength}行数据（考官和日期动态变化）`)
      for (let i = 0; i < newLength; i++) {
        scheduleResults.value[i] = newResults[i]
      }
      await nextTick()
    }
    
    console.log('✅ [动态更新] 表格更新完成，当前显示', scheduleResults.value.length, '行')
  } else {
    // 非实时更新，直接显示所有数据
    scheduleResults.value = []
    await nextTick()
    scheduleResults.value = newResults
    console.log('✅ scheduleResults.value更新完成，显示条目数:', scheduleResults.value.length)
  }
  
  // 🔧 禁用前端独立约束检查，信任后端验证结果
  // 原因：前端检查器数据不完整（缺少examiner.department），容易误判
  // 后端已经在buildScheduleResponse中验证过所有约束
  console.log('✅ [约束验证] 使用后端验证结果，硬约束违反数:', backendHardScore < 0 ? '有违反' : '0个')
  
  // 🔍 仅在开发环境下进行诊断性检查（不影响UI显示）
  if (import.meta.env.DEV) {
    try {
      const checkResult = checkScheduleConstraints(result.assignments || (result as any).examSchedule?.assignments || [])
      if (checkResult.summary.totalHardViolations > 0) {
        console.warn('⚠️ [前端诊断] 检测到 ' + checkResult.summary.totalHardViolations + ' 个可能的违反')
        console.warn('⚠️ [前端诊断] 这可能是因为前端数据不完整，以后端验证为准')
        console.table(checkResult.hardViolations.slice(0, 5))
      } else {
        console.log('✅ [前端诊断] 前端约束检查也通过')
      }
    } catch (error) {
      console.error('❌ [前端诊断] 约束检查失败:', error)
    }
  }
  
  // 更新表格状态为完成
  isTableUpdating.value = false
  const now = new Date()
  lastTableUpdate.value = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  addRealtimeLog(`✅ 表格更新完成，共显示 ${newResults.length} 条排班记录`, 'success')
  console.log('排班结果已按日期排序')
  
  // 强制触发响应式更新和DOM重新渲染
  await nextTick()
  
  // 验证DOM是否正确更新
  setTimeout(() => {
    const tableBody = document.querySelector('.schedule-table tbody')
    const dataRows = tableBody?.querySelectorAll('tr:not(:empty)') || []
    console.log('🔍 DOM验证: 表格行数:', dataRows.length)
    
    if (dataRows.length === 0 && scheduleResults.value.length > 0) {
      console.warn('⚠️ 检测到渲染问题，尝试强制更新DOM')
      // 强制重新渲染
      const currentData = [...scheduleResults.value]
      scheduleResults.value = []
      setTimeout(() => {
        scheduleResults.value = currentData
        console.log('🔄 已执行强制DOM更新')
      }, 50)
    } else {
      console.log('DOM渲染正常，数据已显示')
    }
  }, 300)
  
  console.log('界面更新完成')
  
  // 检查内容是否溢出，如果是则自动收缩侧边栏
  nextTick(() => {
    setTimeout(checkContentOverflow, 200) // 延迟检查确保表格完全渲染
  })
  
  // 自动保存排班结果到本地存储
  try {
    const scheduleRecord: ScheduleResultRecord = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      title: `排班结果_${new Date().toLocaleDateString()}`,
      result: result,
      displayData: newResults,
      metadata: {
        studentCount: result.assignments.length > 0 ? new Set(result.assignments.map(a => a.studentId)).size : 0,
        teacherCount: result.assignments.length > 0 ? new Set([
          ...result.assignments.map(a => a.examiner1).filter(id => id),
          ...result.assignments.map(a => a.examiner2).filter(id => id),
          ...result.assignments.map(a => a.backupExaminer).filter(id => id)
        ]).size : 0,
        dateRange: examStartDate.value && examEndDate.value ? `${examStartDate.value.toISOString().split('T')[0]} 到 ${examEndDate.value.toISOString().split('T')[0]}` : '未设置',
        constraints: {
          ...constraints.value,  // 保存完整的约束配置
          hardConstraints: Object.keys(constraints.value).filter(key => constraints.value[key as keyof typeof constraints.value] === true),
          softConstraints: Object.keys(constraints.value).filter(key => constraints.value[key as keyof typeof constraints.value] === true)
        }
      }
    }
    
    await storageService.saveScheduleResult(scheduleRecord)
    console.log('排班结果已自动保存到本地存储')
  } catch (error) {
    console.error('保存排班结果失败:', error)
  }
  
  // 显示统计信息
  console.log('排班统计:', result.statistics)
  if (result.conflicts && result.conflicts.length > 0) {
    console.warn('约束冲突详情:')
    result.conflicts.forEach((conflict, index) => {
      console.warn(`冲突${index + 1}:`, {
        类型: conflict.type,
        约束: conflict.constraint,
        严重程度: conflict.severity,
        描述: conflict.description,
        影响实体: conflict.affectedEntities,
        建议: conflict.suggestion
      })
    })
  }
  if (result.warnings && result.warnings.length > 0) {
    console.warn('排班警告:', result.warnings)
  }
}

// 缓存考官数据以避免重复调用（已迁移到统一缓存管理器）
let cachedTeacherData: TeacherInfo[] | null = null

  // 考官数据完整性验证函数
const validateTeacherData = (teachers: TeacherInfo[]): { isValid: boolean; errors: string[]; validCount: number } => {
  const errors: string[] = []
  let validCount = 0
  
  if (!teachers || !Array.isArray(teachers)) {
    errors.push('考官数据不是有效的数组格式')
    return { isValid: false, errors, validCount: 0 }
  }
  
  if (teachers.length === 0) {
    errors.push('考官数据为空，无法进行排班')
    return { isValid: false, errors, validCount: 0 }
  }
  
  // 验证每个考官的数据完整性
  teachers.forEach((teacher, index) => {
    const teacherErrors: string[] = []
    
    if (!teacher) {
      teacherErrors.push(`${index + 1}个考官数据为空`)
    } else {
      // 验证必需字段
      if (!teacher.id || teacher.id.toString().trim() === '') {
        teacherErrors.push(`${index + 1}个考官缺少ID`)
      }
      
      if (!teacher.name || teacher.name.trim() === '') {
        teacherErrors.push(`${index + 1}个考官缺少姓名`)
      }
      
      if (!teacher.department || teacher.department.trim() === '') {
        teacherErrors.push(`${index + 1}个考官缺少科室信息`)
      }
      
      // 验证班组信息（可以为空，但如果存在应该是有效的）
      if (teacher.group !== undefined && teacher.group !== null && teacher.group.trim() === '') {
        teacherErrors.push(`${index + 1}个考官班组信息格式错误`)
      }
      
      // 验证技能信息（可选字段）
      if (teacher.skills && !Array.isArray(teacher.skills)) {
        teacherErrors.push(`${index + 1}个考官技能信息格式错误`)
      }
      
      if (teacherErrors.length === 0) {
        validCount++
      }
    }
    
    if (teacherErrors.length > 0) {
      errors.push(...teacherErrors)
    }
  })
  
  // 检查是否有足够的有效考官进行排班
  if (validCount < 3) {
    errors.push(`有效考官数量不足${validCount}个），至少需3个考官才能进行排班`)
  }
  
  // 检查科室分布
  const departmentCount = new Set(teachers.filter(t => t && t.department).map(t => t.department)).size
  if (departmentCount < 2) {
    errors.push(`考官科室分布不足${departmentCount}个科室），建议至少有2个不同科室的考官`)
  }
  
  const isValid = errors.length === 0
  
  console.log('📊 考官数据验证结果:', {
    总数: teachers.length,
    有效数量: validCount,
    科室数量: departmentCount,
    错误数量: errors.length,
    验证通过: isValid
  })
  
  return { isValid, errors, validCount }
}

// 智能排班执行函数 - 支持多算法选择
const executeSchedulingWithRetry = async (originalRequest: OptaPlannerRequest): Promise<OptaPlannerResponse> => {
  const maxRetries = 3
  let lastError: any = null
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    console.log(`🔄 排班尝试 ${attempt + 1}/${maxRetries}，使用算法 ${selectedAlgorithm.value}`)
    
    try {
      let result: OptaPlannerResponse
      
      // 只使用传统OptaPlanner算法
      result = await optaPlannerService.generateSchedule(originalRequest)
      
      if (result.success) {
        console.log(`排班成功！尝试次数 ${attempt + 1}，算法 ${selectedAlgorithm.value}`)
        console.log('📊 分配数量:', result.assignments?.length || 0)
        
        return result
      } else {
        console.warn(`排班失败，尝试次数 ${attempt + 1}`)
        lastError = new Error(`排班失败: ${result.message || '未知错误'}`)
      }
    } catch (error) {
      console.error(`💥 排班执行异常，尝试次数 ${attempt + 1}:`, error)
      lastError = error
      
      // 网络或服务错误，等待后重试
      if (attempt < maxRetries - 1) {
        const waitTime = Math.min(1000 * Math.pow(2, attempt), 5000) // 指数退避，最长等待5秒
        console.log(`等待 ${waitTime}ms 后重试.`)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
  }
  
  // 所有重试都失败了，返回失败结果
  console.error('💔 所有排班尝试都失败')
  
  return {
    success: false,
    assignments: [],
    statistics: {
      totalStudents: originalRequest.students.length,
      assignedStudents: 0,
      unassignedStudents: originalRequest.students.length,
      totalTeachers: originalRequest.teachers.length,
      activeTeachers: 0,
      averageWorkload: 0,
      maxWorkload: 0,
      finalScore: {
        hardScore: 0,
        softScore: 0
      },
      completionPercentage: 0,
      solvingTimeMillis: 0,
      hardConstraintViolations: 0,
      softConstraintViolations: 0
    },
    conflicts: [],
    warnings: [`系统错误: ${lastError?.message || '未知错误'}`],
    message: `排班系统${maxRetries}次尝试后仍然失败`
  }
}

// 辅助函数：根据考官ID或考官对象获取考官姓名
const getTeacherNameById = (teacherData: any): string => {
  // 增强数据验证和调试日志
  console.log('🔍 getTeacherNameById 接收到的数据:', teacherData, '类型:', typeof teacherData)
  
  if (teacherData === null || teacherData === undefined || teacherData === '') {
    console.warn('⚠️ 考官数据为null、undefined或空字符串，返回"未分配"')
    return '未分配'
  }
  
  // 如果是字符串，直接返回（已经转换过的姓名）
  if (typeof teacherData === 'string') {
    const trimmed = teacherData.trim()
    if (trimmed && trimmed !== '未分组' && trimmed !== '数据错误' && trimmed !== '未分配') {
      console.log('✅ 返回字符串考官姓名:', trimmed)
      return trimmed
    }
    // 如果是纯数字ID，需要查找对应姓名
    if (/^\d+$/.test(trimmed)) {
      console.log('🔍 检测到数字ID字符串，查找对应姓名:', trimmed)
      return findTeacherNameById(trimmed)
    }
    return trimmed || '未分配'
  }
  
  // 如果是考官对象，直接提取name属性
  if (typeof teacherData === 'object' && teacherData !== null) {
    console.log('🔍 处理考官对象:', JSON.stringify(teacherData))
    
    // 优先使用name属性
    if (teacherData.name && typeof teacherData.name === 'string') {
      const teacherName = teacherData.name.trim()
      console.log('✅ 从考官对象提取姓名:', teacherName)
      return teacherName
    }
    
    // 检查其他可能的姓名字段
    const nameFields = ['teacherName', 'fullName', 'displayName']
    for (const field of nameFields) {
      if (teacherData[field] && typeof teacherData[field] === 'string') {
        const name = teacherData[field].trim()
        console.log(`✅ 从考官对象字段 "${field}" 提取姓名:`, name)
        return name
      }
    }
    
    // 处理对象但没有name属性的情况，尝试使用id查找
    if (teacherData.id) {
      console.log('⚠️ 考官对象缺少name属性，尝试通过ID查找:', teacherData.id)
      const teacherId = teacherData.id.toString()
      const foundName = findTeacherNameById(teacherId)
      if (foundName && foundName !== '未分配' && !foundName.startsWith('考官')) {
        return foundName
      }
      // 如果查找失败，但对象有其他可用信息，尝试构造显示名称
      if (teacherData.department) {
        return `${teacherData.department}考官`
      }
      // 最后尝试返回ID标识
      return `考官${teacherId}`
    }
    
    console.log('⚠️ 考官对象格式异常，无有效姓名或ID:', teacherData)
    return '数据异常'
  }
  
  // 如果是数字ID，转换为字符串处理
  if (typeof teacherData === 'number') {
    console.log('🔍 处理数字ID:', teacherData)
    const teacherId = teacherData.toString()
    return findTeacherNameById(teacherId)
  }
  
  console.log('❌ 无法识别的考官数据格式:', teacherData)
  return '格式错误'
}

// 辅助函数：根据考官ID查找姓名
const findTeacherNameById = (teacherId: string): string => {
  // 验证输入参数
  if (!teacherId || typeof teacherId !== 'string') {
    console.warn('⚠️ 考官ID无效:', teacherId)
    return '未分配'
  }
  
  console.log(`🔍 查找考官ID "${teacherId}" 对应的姓名`)
  
  // 优先使用缓存的考官数据
  if (cachedTeacherData && Array.isArray(cachedTeacherData)) {
    try {
      console.log(`🔍 在缓存中查找考官ID "${teacherId}"，缓存数据量: ${cachedTeacherData.length}`)
      
      const teacher = cachedTeacherData.find(t => {
        if (!t || !t.id) return false
        
        // 增强匹配逻辑，处理不同的ID格式
        const matches = (
          t.id === teacherId || 
          t.id.toString() === teacherId || 
          t.id?.toString() === teacherId.toString()
        )
        
        if (matches) {
          console.log(`✅ 找到匹配考官: ID=${t.id}, Name=${t.name}`)
        }
        
        return matches
      })
      
      if (teacher && teacher.name) {
        console.log(`✅ 成功从缓存获取考官姓名: ${teacher.name}`)
        return teacher.name
      } else {
        console.warn(`⚠️ 缓存中未找到考官ID "${teacherId}"`)
        // 打印缓存中的所有考官ID用于调试
        const cachedIds = cachedTeacherData.map(t => t?.id).filter(id => id)
        console.log('🔍 缓存中的所有考官ID:', cachedIds.slice(0, 10), cachedIds.length > 10 ? `...等${cachedIds.length}个` : '')
      }
    } catch (error) {
      console.error('❌ 缓存数据查找出错:', error)
    }
  } else {
    console.warn('⚠️ 考官数据缓存未初始化或为空')
  }
  
  // 如果缓存中没有找到考官，返回ID标识
  console.error(`❌ 未找到考官ID "${teacherId}" 对应的姓名`)
  return `考官${teacherId}`
}

// 辅助函数：寻找替代考官
const findAlternativeExaminer = (excludeExaminer: string, studentDepartment: string): string | null => {
  console.log(`🔍 为学员科室"${studentDepartment}"寻找替代考官，排除考官"${excludeExaminer}"`)
  
  // 检查缓存的考官数据
  if (!cachedTeacherData || !Array.isArray(cachedTeacherData)) {
    console.warn('⚠️ 考官数据缓存未初始化，无法寻找替代考官')
    return null
  }
  
  try {
    // 筛选可用的替代考官 - 增强约束检查
    const availableExaminers = cachedTeacherData.filter(teacher => {
      if (!teacher || !teacher.name || !teacher.department) {
        return false
      }
      
      // 排除当前重复的考官
      if (teacher.name === excludeExaminer) {
        console.log(`🚫 排除重复考官: ${teacher.name}`)
        return false
      }
      
      // HC7约束：考官2必须与学员不同科室
      if (teacher.department === studentDepartment) {
        console.log(`🚫 排除同科室考官: ${teacher.name} (科室: ${teacher.department})`)
        return false
      }
      
      // 检查三室七室互通规则（如果学员是三室或七室）
      if ((studentDepartment === '三室' && teacher.department === '七室') ||
          (studentDepartment === '七室' && teacher.department === '三室')) {
        console.log(`🚫 排除三室七室互通考官: ${teacher.name} (科室: ${teacher.department})`)
        return false
      }
      
      console.log(`找到可用替代考官: ${teacher.name} (科室: ${teacher.department})`)
      return true
    })
    
    console.log(`🔍 找到${availableExaminers.length}名可用替代考官`)
    
    if (availableExaminers.length > 0) {
      // 优先选择工作负荷较低的考官
      const sortedExaminers = availableExaminers.sort((a, b) => {
        const workloadA = (a as any).workload || 0
        const workloadB = (b as any).workload || 0
        return workloadA - workloadB
      })
      
      const selectedExaminer = sortedExaminers[0]
      console.log(`选择替代考官: ${selectedExaminer.name} (科室: ${selectedExaminer.department}, 工作负荷: ${(selectedExaminer as any).workload || 0})`)
      return selectedExaminer.name
    } else {
      console.warn(`⚠️ 未找到合适的替代考官 - 学员科室: ${studentDepartment}, 排除考官: ${excludeExaminer}`)
      console.log('📊 当前考官分布:', cachedTeacherData.map(t => `${t.name}(${t.department})`).join(', '))
      return null
    }
  } catch (error) {
    console.error('寻找替代考官时出错:', error)
    return null
  }
}

// 辅助函数：格式化日期字符串
const formatDateFromString = (dateStr: string): string => {
  const date = new Date(dateStr)
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${month}.${day}`
}

// 辅助函数：格式化日期对象
const formatDate = (date: Date): string => {
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${month}.${day}`
}



// 重新排班（重新计算排班）
const recalculateSchedule = async () => {
  try {
    console.log('🔄 开始重新排班.')
    
    // 显示详细的重新排班确认信息
    const currentStudentCount = studentList.value.length
    const currentScheduleCount = scheduleResults.value.length
    const dateRangeText = examStartDate.value && examEndDate.value 
    ? `${examStartDate.value.toLocaleDateString()} 到 ${examEndDate.value.toLocaleDateString()}`
      : '未设置'
    
    const confirmMessage = `确定要重新排班吗?
📊 当前状态：
学员数量: ${currentStudentCount}
已排班学员: ${currentScheduleCount}
考试日期: ${dateRangeText}

🔄 重新排班将：
使用最新的约束配置
清除所有人工修改记录
重新计算最优排班方案
可能产生与之前不同的结果

⚠️ 此操作不可撤销，建议先导出当前排班结果作为备份
是否继续？`
    
    if (!confirm(confirmMessage)) {
      return
    }
    
    // 重置排班状态
    isScheduling.value = true
    schedulingProgress.value = 0
    schedulingError.value = ''
    
    // 尝试从保存的排班结果中恢复配置
    const savedResult = await storageService.loadLatestScheduleResult()
    
    if (savedResult && savedResult.metadata) {
      console.log('恢复上次排班配置:', savedResult.metadata)
      
      // 恢复日期范围
      if (savedResult.metadata.dateRange) {
        const [startStr, endStr] = savedResult.metadata.dateRange.split(' 到 ')
        if (startStr && endStr) {
          examStartDate.value = new Date(startStr)
          examEndDate.value = new Date(endStr)
          console.log('恢复日期范围:', startStr, '到', endStr)
        }
      }
      
      // 恢复学员数据（从原始排班结果中提取）
      if (savedResult.result && savedResult.result.assignments && savedResult.result.assignments.length > 0) {
        // 从排班结果中提取学员信息
        const studentMap = new Map()
        savedResult.result.assignments.forEach((assignment: any) => {
          if (!studentMap.has(assignment.studentId)) {
            studentMap.set(assignment.studentId, {
              id: assignment.studentId,
              name: assignment.studentName,
              department: assignment.studentDepartment,
              group: assignment.studentGroup || '一组' // 使用原始班组或默认班组
            })
          }
        })
        
        // 添加未分配的学员
        if (savedResult.result.unassignedStudents) {
          savedResult.result.unassignedStudents.forEach((student: any) => {
            if (!studentMap.has(student.id)) {
              studentMap.set(student.id, student)
            }
          })
        }
        
        studentList.value = Array.from(studentMap.values())
        console.log('恢复学员数据:', studentList.value.length, '名学员')
      }
      
      // 保持当前约束配置不变，不从历史数据中恢复约束
      // 这确保重新排班使用最新的用户设置约束，而不是历史约束
      console.log('保持当前约束配置，不从历史数据恢复约束')
      console.log('当前约束配置:', constraints.value)
    }
    
    schedulingProgress.value = 10
    
    // 验证必要数据
    if (!examStartDate.value || !examEndDate.value) {
      schedulingError.value = '无法恢复考试日期范围，请重新设置'
      isScheduling.value = false
      return
    }
    
    if (studentList.value.length === 0) {
      schedulingError.value = '无法恢复学员数据，请重新上传学员文件'
      isScheduling.value = false
      return
    }
    
    console.log('配置恢复完成，开始重新排班')
    console.log('📋 当前约束配置:', constraints.value)
    console.log('👥 学员数量:', studentList.value.length)
    console.log('📅 考试日期:', examStartDate.value, '到', examEndDate.value)
    
    schedulingProgress.value = 20
    
    // 重新执行排班算法（使用最新的约束权重）
    await originalNextStep()
    
    console.log('🎉 重新排班完成')
    
  } catch (error) {
    console.error('重新排班失败:', error)
    schedulingError.value = `重新排班失败: ${(error as Error).message || '未知错误'}`
    isScheduling.value = false
  }
}

// 旧的算法代码已移除，现在使用智能排班算法

// 旧的辅助函数已移除，现在使用智能排班算法中的实现

// toggleConstraint函数已在上面定义

// 强制刷新显示功能
const forceRefreshDisplay = async () => {
  console.log('🔄 用户手动触发显示刷新')
  
  if (scheduleResults.value.length === 0) {
    console.warn('⚠️ 没有排班数据需要刷新')
    needsRefresh.value = false
    return
  }
  
  try {
    // 保存当前数据
    const currentData = [...scheduleResults.value]
    console.log('📋 当前数据条数:', currentData.length)
    
    // 清空数据触发重新渲染
    scheduleResults.value = []
    await nextTick()
    
    // 延迟恢复数据
    setTimeout(async () => {
      scheduleResults.value = currentData
      await nextTick()
      
      // 验证渲染结果
      setTimeout(() => {
        const tableBody = document.querySelector('.schedule-table tbody')
        const dataRows = tableBody?.querySelectorAll('tr:not(:empty)') || []
        
        if (dataRows.length > 0) {
          console.log('强制刷新成功，表格已显示', dataRows.length, '行数')
          needsRefresh.value = false
          
          // 添加成功提示
          const successMsg = document.createElement('div')
          successMsg.textContent = '显示刷新成功'
          successMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 12px 20px; border-radius: 6px; z-index: 9999; font-weight: 500;'
          document.body.appendChild(successMsg)
          setTimeout(() => successMsg.remove(), 3000)
        } else {
          console.warn('⚠️ 强制刷新后仍无法显示，可能存在更深层的问题')
          needsRefresh.value = true
          
          // 添加警告提示
          const warningMsg = document.createElement('div')
          warningMsg.textContent = '⚠️ 刷新后仍无法显示，请尝试重新排班'
          warningMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #f59e0b; color: white; padding: 12px 20px; border-radius: 6px; z-index: 9999; font-weight: 500;'
          document.body.appendChild(warningMsg)
          setTimeout(() => warningMsg.remove(), 5000)
        }
      }, 200)
    }, 100)
    
  } catch (error) {
    console.error('强制刷新失败:', error)
    needsRefresh.value = true
    
    // 添加错误提示
    const errorMsg = document.createElement('div')
    errorMsg.textContent = '刷新失败，请重试'
    errorMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 12px 20px; border-radius: 6px; z-index: 9999; font-weight: 500;'
    document.body.appendChild(errorMsg)
    setTimeout(() => errorMsg.remove(), 3000)
  }
}

// 通用考官验证函数
const isValidExaminer = (examiner: any): boolean => {
  if (!examiner) return false
  
  // 如果是字符串
  if (typeof examiner === 'string') {
    return examiner !== '未分配' && 
           examiner !== '未分组' && 
           examiner.trim() !== '' &&
           examiner !== 'null' &&
           examiner !== 'undefined'
  }
  
  // 如果是对象
  if (typeof examiner === 'object' && examiner !== null) {
    return examiner.name && 
           examiner.name !== '未分配' && 
           examiner.name !== '未分组' &&
           examiner.name.trim() !== ''
  }
  
  return false
}

// 约束验证准确性检查函数
const validateViolationAccuracy = (violations: ConstraintViolation[], assignments: any[], dates: string[]) => {
  const validViolations: ConstraintViolation[] = []
  
  for (const violation of violations) {
    let isValid = true
    
    switch (violation.type) {
      case 'teacher':
        // 验证考官分配违反的准确性
        if (violation.id === 'main-examiners-violation') {
          const incompleteCount = assignments.filter(assignment => {
            if (!assignment?.studentName) return false
            
            const hasExaminer1 = isValidExaminer(assignment.examiner1)
            const hasExaminer2 = isValidExaminer(assignment.examiner2)
            
            return !hasExaminer1 || !hasExaminer2
          }).length
          
          // 如果没有真实的违反，标记为无效
          if (incompleteCount === 0) {
            isValid = false
            console.log('🔍 过滤无效违反: 所有学员都已正确分配考官')
          }
          // 如果违反比例过高（>80%），可能是系统问题
          else if (assignments.length > 0 && incompleteCount / assignments.length > 0.8) {
            isValid = false
            console.log('🔍 过滤系统性问题: 大部分学员都缺少考官，可能是配置问题')
          }
        }
        break
        
      case 'holiday':
        // 验证假期违反的准确性
        const holidays = ['2025-01-01', '2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31', '2025-02-01', '2025-02-02', '2025-02-03', '2025-04-05', '2025-04-06', '2025-04-07', '2025-05-01', '2025-05-02', '2025-05-03', '2025-06-09', '2025-06-10', '2025-06-11', '2025-09-15', '2025-09-16', '2025-09-17', '2025-10-01', '2025-10-02', '2025-10-03', '2025-10-04', '2025-10-05', '2025-10-06', '2025-10-07']
        const conflictDates = dates.filter(date => holidays.includes(date))
        if (conflictDates.length === 0) {
          isValid = false
          console.log('🔍 过滤无效违反: 所选日期均不在法定节假日内')
        }
        break
        
      case 'weekend':
        // 验证周末违反的准确性
        const weekendDates = dates.filter(date => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek === 0 || dayOfWeek === 6
        })
        if (weekendDates.length === 0) {
          isValid = false
          console.log('🔍 过滤无效违反: 所选日期均为工作日')
        }
        break
        
      default:
        // 其他类型的违反保持原样
        break
    }
    
    if (isValid) {
      validViolations.push(violation)
    }
  }
  
  return validViolations
}

// 约束违反处理方法
const handleFixViolation = (violation: ConstraintViolation) => {
  console.log('修复约束违反:', violation)
  
  if (violation.type === 'holiday' || violation.type === 'weekend') {
    // 提示用户重新选择日期
    // 使用非阻塞通知替代alert
    console.warn(`检测到${violation.title}`, violation)
    schedulingError.value = `${violation.title}: ${violation.message} ${violation.suggestion}`
    
    // 可以在这里打开日期选择器或其他修复界面
    showCreateModal.value = true
  } else if (violation.type === 'teacher' && violation.id === 'main-examiners-violation') {
    // 约束违反：主考官不足
    let message = `🚨 ${violation.title}\n\n`
    message += `📋 问题详情：\n${violation.message}\n\n`
    
    if (violation.details && violation.details.length > 0) {
      message += `📝 具体问题：\n`
      violation.details.forEach((detail, index) => {
        message += `${index + 1}. ${detail}\n`
      })
      message += `\n`
    }
    
    message += `💡 解决方案：\n`
    message += `1. 检查考官资源是否充足\n`
    message += `2. 确保每个科室都有足够的可用考官\n`
    message += `3. 考官一必须与学员同科室\n`
    message += `4. 考官二必须与学员不同科室\n`
    message += `5. 避免考官时间冲突\n\n`
    message += `${violation.suggestion}\n\n`
    message += `点击确定重新进行排班配置。`
    
    if (confirm(message)) {
      // 重新打开排班配置
      showCreateModal.value = true
      
      // 清除当前违反状态
      constraintViolations.value = constraintViolations.value.filter(v => v.id !== violation.id)
    }
  }
}

// 智能弹窗控制函数
const dismissViolationAlert = () => {
  shouldShowViolationAlert.value = false
  violationAlertDismissedAt.value = Date.now()
  constraintViolations.value = []
  
  console.log('📝 用户已关闭约束违反提示')
}

// 检查是否应该重新显示弹窗（如果有新的更严重的违反）
const checkShouldShowNewViolations = (newViolations: ConstraintViolation[]) => {
  // 如果没有被关闭过，或者关闭时间超过5分钟，可以显示
  if (!violationAlertDismissedAt.value || Date.now() - violationAlertDismissedAt.value > 5 * 60 * 1000) {
    shouldShowViolationAlert.value = true
    return true
  }
  
  // 如果有高严重性的新违反，也可以显示
  const hasHighSeverity = newViolations.some(v => v.severity === 'high')
  if (hasHighSeverity) {
    shouldShowViolationAlert.value = true
    return true
  }
  
  return false
}

// 移除集成状态面板相关方法

// 切换实时计算流程窗口显示
const toggleRealtimeProgressWindow = () => {
  realtimeProgressVisible.value = !realtimeProgressVisible.value
  if (!wsSessionId.value && (window as any).__opta_session_id) {
    wsSessionId.value = (window as any).__opta_session_id
  }
}

type ParsedHardSoftScore = { hardScore: number; softScore: number }

/**
 * 解析OptaPlanner的HardSoftScore表示形式
 */
const parseHardSoftScore = (score: unknown): ParsedHardSoftScore | null => {
  if (!score) return null

  if (typeof score === 'object' && score !== null) {
    const candidate = score as Record<string, unknown>
    if (typeof candidate.hardScore === 'number' || typeof candidate.softScore === 'number') {
      return {
        hardScore: typeof candidate.hardScore === 'number' ? candidate.hardScore : 0,
        softScore: typeof candidate.softScore === 'number' ? candidate.softScore : 0
      }
    }
  }

  if (typeof score === 'string') {
    const match = score.match(/(-?\d+)\s*hard\/(-?\d+)soft/i)
    if (match) {
      return {
        hardScore: parseInt(match[1], 10),
        softScore: parseInt(match[2], 10)
      }
    }
  }

  return null
}

/**
 * 连接WebSocket并监听实时排班更新
 * 🆕 实现排班表格实时显示功能
 */
const connectWebSocketForRealtimeUpdates = async (sessionId: string) => {
  if (!sessionId) {
    console.warn('⚠️ [实时更新] 未提供有效的sessionId，取消连接')
    return
  }
  
  try {
    console.log('📡 [实时更新] 准备连接WebSocket，会话ID:', sessionId)
    
    if (!realtimeProgressServiceInstance) {
      console.log('📦 [实时更新] 第一次导入scheduleProgressService模块...')
      const module = await import('../services/scheduleProgressService') as any
      realtimeProgressServiceInstance = module.scheduleProgressService || module.default
      console.log('✅ [实时更新] scheduleProgressService导入成功')
    }
    
    // 清理旧的事件回调，避免重复触发
    if (realtimeProgressUnsubscribe) {
      realtimeProgressUnsubscribe()
      realtimeProgressUnsubscribe = null
    }
    
    // 如果已经连接但会话ID不同，则主动断开
    if (
      realtimeProgressServiceInstance &&
      typeof realtimeProgressServiceInstance.isConnected === 'function' &&
      realtimeProgressServiceInstance.isConnected() &&
      activeRealtimeSessionId &&
      activeRealtimeSessionId !== sessionId
    ) {
      console.log('🔄 [实时更新] 检测到旧会话，先断开旧的WebSocket连接:', activeRealtimeSessionId)
      realtimeProgressServiceInstance.disconnect()
    }
    
    const isSameSessionConnected =
      realtimeProgressServiceInstance &&
      typeof realtimeProgressServiceInstance.isConnected === 'function' &&
      realtimeProgressServiceInstance.isConnected() &&
      activeRealtimeSessionId === sessionId
    
    if (!isSameSessionConnected) {
      console.log('📡 [实时更新] 正在连接WebSocket...')
      await realtimeProgressServiceInstance.connect(sessionId)
      console.log('✅ [实时更新] WebSocket连接成功')
    } else {
      console.log('🔁 [实时更新] 已存在同会话连接，本次复用现有WebSocket')
    }
    
    activeRealtimeSessionId = sessionId
    
    // 监听中间结果消息
    console.log('🔍 [调试] 开始监听WebSocket消息...')
    
    realtimeProgressUnsubscribe = realtimeProgressServiceInstance.onProgress(async (message: any) => {
      console.log('📨 [实时更新] 收到WebSocket消息:', message.type, message)
      
      if (message.type === 'intermediate_result') {
        console.log('🎯 [实时更新] 收到中间结果消息')
        console.log('🔍 [实时更新] 数据内容:', message.data)
        console.log('🔍 [实时更新] assignments数组:', message.data?.assignments)
        
        if (message.data?.assignments && Array.isArray(message.data.assignments) && message.data.assignments.length > 0) {
          console.log('✅ [实时更新] assignments有效，长度:', message.data.assignments.length)
          console.log('📊 [实时更新] 排班数量:', message.data.assignments.length)
          console.log('📊 [实时更新] 第一个assignment示例:', message.data.assignments[0])
          
          // 标记表格正在更新
          isTableUpdating.value = true
          lastTableUpdate.value = new Date().toLocaleTimeString()
          
          try {
            // 🔧 修复：适配后端DTO格式（直接使用DTO的字段，不再需要student嵌套对象）
            const intermediateAssignments = message.data.assignments.map((assignment: any) => ({
              id: assignment.id,
              studentId: assignment.studentId,
              studentName: assignment.studentName,
              studentDepartment: assignment.studentDepartment,
              examDate: assignment.examDate,
              examType: assignment.examType,
              subjects: assignment.subjects,
              examiner1: assignment.examiner1,
              examiner2: assignment.examiner2,
              backupExaminer: assignment.backupExaminer,
              location: assignment.location,
              timeSlot: assignment.timeSlot
            }))
            
            console.log('🔍 [实时更新] 转换后assignments数量:', intermediateAssignments.length)
            console.log('🔍 [实时更新] 转换后第一个assignment:', intermediateAssignments[0])

            const parsedScore = parseHardSoftScore(message.data.score)
            const realtimeStatistics = {
              ...(message.data.statistics || {}),
              softConstraintsScore:
                message.data.statistics?.softConstraintsScore ??
                (parsedScore ? parsedScore.softScore : message.data.softConstraintsScore ?? 0)
            }
            const softScoreCandidate =
              parsedScore?.softScore ??
              (typeof realtimeStatistics.softConstraintsScore === 'number'
                ? realtimeStatistics.softConstraintsScore
                : null)

            if (softScoreCandidate !== null) {
              latestSoftScore.value = softScoreCandidate
              if (bestSoftScore.value === null || softScoreCandidate > bestSoftScore.value) {
                bestSoftScore.value = softScoreCandidate
              }
            }

            console.log('🔍 [实时更新] 调用updateScheduleResults进行实时更新...')
            await updateScheduleResults({
              assignments: intermediateAssignments,
              statistics: realtimeStatistics,
              score: parsedScore || message.data.score,
              success: true,
              conflicts: [],
              warnings: [],
              unassignedStudents: (message.data.unassignedStudents as any[]) || []
            } as any, true)

            currentAssignmentCount.value = intermediateAssignments.length

            if (totalStudents.value > 0) {
              const estimatedProgress = Math.min(
                99,
                Math.round(
                  (intermediateAssignments.length / Math.max(1, totalStudents.value * 2)) * 100
                )
              )
              if (!Number.isNaN(estimatedProgress)) {
                schedulingProgress.value = Math.max(schedulingProgress.value, estimatedProgress)
                currentProgressMessage.value = `正在优化排班方案... ${estimatedProgress}%`
              }
            }

            if (parsedScore) {
              schedulingError.value = `🔄 ${message.data.quality || '实时方案'} - 硬约束 ${parsedScore.hardScore}, 软约束 ${parsedScore.softScore}`
            } else if (message.data.score) {
              schedulingError.value = `🔄 ${message.data.quality || '实时方案'} - ${message.data.score}`
            }

            console.log('✅ [实时更新] 表格已更新，显示', scheduleResults.value.length, '行数据')

            const confidence = message.data.confidence ? Math.round(message.data.confidence * 100) : 0
            const quality = message.data.quality || '未知'
            addRealtimeLog(
              `📊 实时更新: ${quality}质量方案 (置信度${confidence}%, 软约束 ${parsedScore?.softScore ?? '未知'})`,
              'success'
            )
            addRealtimeLog(
              `✨ 排班表格已更新，当前显示 ${scheduleResults.value.length} 条排班记录`,
              'info'
            )

            setTimeout(() => {
              isTableUpdating.value = false
            }, 500)
          } catch (error) {
            console.error('❌ [实时更新] 转换排班数据失败:', error)
            addRealtimeLog('❌ 实时更新失败: ' + (error as Error).message, 'error')
            isTableUpdating.value = false
          }
        } else {
          console.warn('⚠️ [实时更新] assignments不存在、为空或格式错误')
          console.warn('⚠️ [实时更新] message.data:', message.data)
          addRealtimeLog('⚠️ 收到中间结果但无有效排班数据', 'warning')
        }
      } else if (message.type === 'final_result') {
        console.log('✅ [实时更新] 收到最终结果')
        addRealtimeLog('🎉 收到最终排班结果', 'success')
        isTableUpdating.value = false
        
        // 最终结果后可以安全断开连接，避免资源占用
        if (realtimeProgressServiceInstance) {
          realtimeProgressServiceInstance.disconnect()
        }
        activeRealtimeSessionId = null
        if (realtimeProgressUnsubscribe) {
          realtimeProgressUnsubscribe()
          realtimeProgressUnsubscribe = null
        }
      } else if (message.type === 'progress') {
        console.log('📈 [实时更新] 收到进度消息:', message.data)
        const progressData = message.data || {}
        const progressPercentage = Number(progressData.progressPercentage ?? progressData.percentage ?? 0)
        if (!Number.isNaN(progressPercentage)) {
          const clampedProgress = Math.min(99, Math.max(0, Math.round(progressPercentage)))
          schedulingProgress.value = Math.max(schedulingProgress.value, clampedProgress)
          addRealtimeLog(`⏳ 求解进度: ${clampedProgress}%`, 'info')
        } else {
          addRealtimeLog('⏳ 求解进度更新', 'info')
        }

        if (progressData.levelName) {
          currentProgressMessage.value = `${progressData.levelName}进行中`
        } else if (typeof message.message === 'string' && message.message.length > 0) {
          currentProgressMessage.value = message.message
        }

        const progressScore = parseHardSoftScore(progressData.currentScore)
        if (progressScore) {
          schedulingError.value = `🔄 ${progressData.levelName || '实时进度'} - 硬约束 ${progressScore.hardScore}, 软约束 ${progressScore.softScore}`
          latestSoftScore.value = progressScore.softScore
          if (bestSoftScore.value === null || progressScore.softScore > bestSoftScore.value) {
            bestSoftScore.value = progressScore.softScore
          }
        } else if (typeof progressData.softConstraintsScore === 'number') {
          latestSoftScore.value = progressData.softConstraintsScore
          if (bestSoftScore.value === null || progressData.softConstraintsScore > bestSoftScore.value) {
            bestSoftScore.value = progressData.softConstraintsScore
          }
        }
      } else if (message.type === 'error') {
        console.error('❌ [实时更新] WebSocket推送错误:', message.message || message.data)
        addRealtimeLog('❌ 实时推送发生错误: ' + (message.message || ''), 'error')
      }
    })
    
    console.log('✅ [实时更新] WebSocket监听已启动')
  } catch (error) {
    console.error('❌ [实时更新] WebSocket连接失败:', error)
    addRealtimeLog('❌ WebSocket连接失败: ' + (error as Error).message, 'error')
  }
}

// 🎯 统一弹窗方法
const closeUnifiedModal = () => {
  showUnifiedResultModal.value = false
}

const getUnifiedResultTitle = () => {
  if (!unifiedResultData.value?.success) {
    return '❌ 排班失败'
  }
  if (constraintViolations.value.length === 0) {
    return '🎉 排班完成！'
  }
  return '⚠️ 排班完成（存在约束违反）'
}

const getUnifiedResultSubtitle = () => {
  if (!unifiedResultData.value?.success) {
    return '排班过程中遇到错误，请检查配置后重试'
  }
  if (constraintViolations.value.length === 0) {
    return '所有约束都已满足，排班结果已生成'
  }
  return `排班已完成，但发现 ${constraintViolations.value.length} 个约束违反`
}

const getUnifiedCompletionRate = () => {
  const stats = unifiedResultData.value?.statistics
  const totalStudents = stats?.totalStudents || scheduleResults.value.length || 0
  const assignedStudents = stats?.assignedStudents || scheduleResults.value.filter(s => s.examiner1_1 && s.examiner2_1).length || 0
  
  if (totalStudents === 0) return '0.0'
  return ((assignedStudents / totalStudents) * 100).toFixed(1)
}

const getUnifiedAssignedStudents = () => {
  const stats = unifiedResultData.value?.statistics
  return stats?.assignedStudents || scheduleResults.value.filter(s => s.examiner1_1 && s.examiner2_1).length || 0
}

const getUnifiedTotalStudents = () => {
  const stats = unifiedResultData.value?.statistics
  return stats?.totalStudents || scheduleResults.value.length || 0
}

const getUnifiedHardConstraintClass = () => {
  const violations = constraintViolations.value.filter(v => v.severity === 'error').length
  return violations === 0 ? 'success' : 'error'
}

// 🔧 格式化软约束得分
const formatSoftScore = (score: number | null | undefined) => {
  if (score === undefined || score === null) {
    return '0'
  }
  
  // 如果是正数（使用了reward系统）
  if (score > 0) {
    return `+${score.toLocaleString()}`
  }
  // 如果是负数（使用了penalize系统）
  else if (score < 0) {
    return score.toLocaleString()
  }
  // 如果是0（完美解）
  else {
    return '0 (完美)'
  }
}

// 🔧 获取软约束得分的样式类
const getSoftScoreClass = () => {
  const score = unifiedResultData.value?.statistics?.softConstraintsScore
  
  if (score === undefined || score === null) {
    return 'warning'
  }
  
  // 如果是正数（reward系统）
  if (score > 0) {
    // 正分系统：分数越高越好
    if (score >= 50000) {
      return 'success'  // 绿色 - 优秀
    } else if (score >= 20000) {
      return 'info'  // 蓝色 - 良好
    } else {
      return 'warning'  // 黄色 - 可以改进
    }
  }
  // 如果是负数（penalize系统）
  else if (score < 0) {
    // 负分系统：越接近0越好
    if (score >= -1000) {
      return 'success'  // 绿色 - 优秀
    } else if (score >= -10000) {
      return 'info'  // 蓝色 - 良好
    } else {
      return 'warning'  // 黄色 - 需要改进
    }
  }
  // 如果是0（完美解）
  else {
    return 'success'
  }
}

const handleFixAllViolations = () => {
  console.log('修复所有约束违反')
  
  const holidayViolations = constraintViolations.value.filter(v => v.type === 'holiday')
  const weekendViolations = constraintViolations.value.filter(v => v.type === 'weekend')
  const teacherViolations = constraintViolations.value.filter(v => v.type === 'teacher')
  
  let message = '🚨 检测到以下约束违反：\n\n'
  
  if (holidayViolations.length > 0) {
    message += '📅 节假日违反：' + holidayViolations.length + '个\n'
  }
  
  if (weekendViolations.length > 0) {
    message += '📅 周末违反' + weekendViolations.length + '个\n'
  }
  
  if (teacherViolations.length > 0) {
    message += '👥 考官配备违反' + teacherViolations.length + '个\n'
    const mainExaminerViolations = teacherViolations.filter(v => v.id === 'main-examiners-violation')
    if (mainExaminerViolations.length > 0) {
      message += '   - 约束：主考官配备不足\n'
    }
  }
  
  message += '\n💡 综合解决方案：\n'
  
  if (holidayViolations.length > 0 || weekendViolations.length > 0) {
    message += '📅 日期问题：\n'
    message += '  1. 重新选择工作日进行考试安排\n'
    message += '  2. 避开所有法定节假日和周末\n'
    message += '  3. 检查考试日期范围设置\n\n'
  }
  
  if (teacherViolations.length > 0) {
    message += '👥 考官问题：\n'
    message += '  1. 增加考官资源或调整考官可用性\n'
    message += '  2. 确保每个科室都有足够的考官\n'
    message += '  3. 检查考官时间冲突\n'
    message += '  4. 优化考官分配策略\n\n'
  }
  
  message += '🔧 建议操作：\n'
  message += '1. 重新配置排班参数\n'
  message += '2. 调整约束条件权重\n'
  message += '3. 增加考官资源\n'
  message += '4. 优化考试日期安排\n\n'
  message += '点击确定重新进行排班配置'
  
  if (confirm(message)) {
    showCreateModal.value = true
    // 清除所有违反状态
    constraintViolations.value = []
  }
}

// 人工调整功能
const editingCell = ref(null)
const showEditModal = ref(false)
const editingRecord = ref<ScheduleResultRow | null>(null)
const editingField = ref<string>('')
const availableTeachers = ref<TeacherInfo[]>([])
const selectedTeacher = ref('')
const currentEditValue = ref('')

// ✨ 检查考官可用性（考虑值班、冲突等）
const checkTeacherAvailability = (teacher: TeacherInfo, examDate: string): boolean => {
  try {
    // 1. 检查值班状态
    const dutySchedule = dutyRotationService.calculateDutySchedule(examDate)
    if (dutySchedule.dayShift === teacher.group) {
      return false  // 白班执勤，不可用
    }
    
    // 2. 检查节假日
    const isHolidayResult = holidayService.isHoliday(examDate)
    if (isHolidayResult) {
      return false  // 节假日，不可用
    }
    
    // 3. 检查时间冲突
    const hasTimeConflict = scheduleResults.value.some((result: any) => 
      result.examDate === examDate && [
        result.examiner1_1, result.examiner1_2, result.backup1,
        result.examiner2_1, result.examiner2_2, result.backup2
      ].includes(teacher.name)
    )
    if (hasTimeConflict) {
      return false  // 已有其他安排
    }
    
    return true
  } catch (error) {
    console.error('检查考官可用性失败:', error)
    return true  // 出错时默认可用
  }
}

// ✨ 生成冲突信息文本
const generateConflictInfo = (teacher: TeacherInfo, examDate: string): string => {
  const conflicts: string[] = []
  
  try {
    const dutySchedule = dutyRotationService.calculateDutySchedule(examDate)
    if (dutySchedule.dayShift === teacher.group) {
      conflicts.push('白班执勤')
    }
    
    const workload = calculateTeacherWorkload(teacher.name)
    if (workload > 5) {
      conflicts.push(`工作量${workload}`)
    }
    
    const hasTimeConflict = scheduleResults.value.some(result => {
      const resultAny = result as any
      return resultAny.examDate === examDate && [
        resultAny.examiner1_1, resultAny.examiner1_2, resultAny.backup1,
        resultAny.examiner2_1, resultAny.examiner2_2, resultAny.backup2
      ].includes(teacher.name)
    })
    if (hasTimeConflict) {
      conflicts.push('时间冲突')
    }
  } catch (error) {
    console.error('生成冲突信息失败:', error)
  }
  
  return conflicts.join(', ')
}

// 编辑考官
const editExaminer = async (record: any, field: string) => {
  console.log('编辑考官:', record, field)
  editingRecord.value = record
  editingField.value = field
  currentEditValue.value = record[field] || ''
  
  // 获取可用考官列表
  try {
    // 🔧 优先使用缓存的考官数据，避免重复加载
    let teachers = cachedTeacherData && cachedTeacherData.length > 0 
      ? cachedTeacherData 
      : teacherList.value && teacherList.value.length > 0
        ? teacherList.value
        : await prepareTeacherData()
    
    console.log('✅ 使用考官数据:', teachers.length, '名考官')
    
    // 如果仍然没有数据，抛出友好的错误
    if (!teachers || teachers.length === 0) {
      throw new Error('请先上传考官数据，或重新进行排班')
    }
    
    // ✨ 动态计算每个考官的实时状态
    const examDate = record.date1 || record.date2 || record.examDate
    
    if (!examDate) {
      throw new Error('无法获取考试日期，请确保排班数据完整')
    }
    
    console.log(`🔧 准备计算考官状态，考试日期: ${examDate}`)
    
    const dutySchedule = dutyRotationService.calculateDutySchedule(examDate)
    
    console.log(`🔧 动态计算考官状态完成，值班信息:`, dutySchedule)
    
    availableTeachers.value = teachers.map(teacher => {
      // 计算当前工作量
      const currentWorkload = calculateTeacherWorkload(teacher.name)
      
      // 判断是否为晚班
      const nightShiftPreferred = dutySchedule.nightShift === teacher.group
      
      // 判断休息日状态
      let restDayStatus: 'first' | 'second' | 'none' = 'none'
      if (dutySchedule.restGroups && dutySchedule.restGroups.includes(teacher.group)) {
        restDayStatus = dutySchedule.restGroups[0] === teacher.group ? 'first' : 'second'
      }
      
      // 检查可用性
      const available = checkTeacherAvailability(teacher, examDate)
      
      // 生成冲突信息
      const conflictInfo = generateConflictInfo(teacher, examDate)
      
      return {
        ...teacher,
        currentWorkload,
        nightShiftPreferred,
        restDayStatus,
        available,
        conflictInfo
      }
    })
    
    console.log(`✅ 考官状态计算完成，可用考官: ${availableTeachers.value.filter(t => t.available).length}/${availableTeachers.value.length}`)
    
    selectedTeacher.value = record[field] || ''
    
    // 初始化智能推荐服务的上下文
    await initializeSmartRecommendation(record, field)
    
    showEditModal.value = true
  } catch (error) {
    console.error('获取考官列表失败:', error)
    alert('获取考官列表失败，请重试')
  }
}

// 确认编辑
const confirmEdit = () => {
  if (editingRecord.value && editingField.value && editingField.value !== '') {
    (editingRecord.value as any)[editingField.value] = selectedTeacher.value
    console.log('更新考官:', editingField.value, '到', selectedTeacher.value)
    
    // 标记数据已修改
    markAsModified()
  }
  closeEditModal()
}

// 关闭编辑弹窗
const closeEditModal = () => {
  showEditModal.value = false
  editingRecord.value = null
  editingField.value = ''
  selectedTeacher.value = ''
  currentEditValue.value = ''
}

// 初始化智能推荐
const initializeSmartRecommendation = async (record: any, field: string) => {
  try {
    // 构建编辑上下文
    const context = {
      editingRecord: record,
      editingField: field,
      currentValue: record[field],
      examDate: record.examDate,
      studentInfo: {
        name: record.student,
        department: record.department,
        level: record.level || '研究'
      },
      scheduleContext: {
        existingAssignments: scheduleResults.value,
        timeSlot: field.includes('2') ? 'day2' as const : 'day1' as const,
        role: field.includes('backup') ? 'backup' as const : 'main' as const
      }
    }

    // 设置约束配置
    smartRecommendationService.setConstraintConfig({
      softConstraints: {
        examiner1SameDept: (constraints.value as any).examiner1SameDept || false,
        backupExaminerDiffDept: (constraints.value as any).backupExaminerDiffDept || false
      }
    })

    // 更新考官工作量缓存
    availableTeachers.value.forEach(teacher => {
      const workload = calculateTeacherWorkload(teacher.name)
      smartRecommendationService.updateWorkloadCache(teacher.id, workload)
    })

  } catch (error) {
    console.error('初始化智能推荐失败', error)
  }
}

// 计算考官工作量
const calculateTeacherWorkload = (teacherName: string): number => {
  return scheduleResults.value.reduce((count, result) => {
    const assignments = [
      result.examiner1_1,
      result.examiner1_2, 
      result.backup1,
      result.examiner2_1,
      result.examiner2_2,
      result.backup2
    ]
    return count + assignments.filter(name => name === teacherName).length
  }, 0)
}

// 处理智能编辑确认
const handleSmartEditConfirm = async (data: {
  teacher: string
  reason: string
  conflicts: any[]
  isForced: boolean
}) => {
  if (editingRecord.value && editingField.value) {
    const oldValue = (editingRecord.value as any)[editingField.value]
    
    // 记录修改信息
    const editInfo = {
      originalValue: currentEditValue.value,
      newValue: data.teacher,
      reason: data.reason,
      conflicts: data.conflicts,
      isForced: data.isForced,
      timestamp: new Date().toISOString(),
      editedBy: '管理员' // 可以从用户状态获取
    };

    // 更新记录
    (editingRecord.value as any)[editingField.value] = data.teacher
    
    // 标记为人工修改
    if (!(editingRecord.value as any).manualEdits) {
      (editingRecord.value as any).manualEdits = []
    }
    (editingRecord.value as any).manualEdits.push(editInfo)
    
    console.log('智能人工修改完成:', editInfo)
    
    // ✨ 新增：重新验证约束
    try {
      console.log('🔍 重新验证修改后的约束...')
      const violations = await validateManualEdit(editingRecord.value, editingField.value, data.teacher)
      
      if (violations.hardViolations.length > 0) {
        showNotification(
          `⚠️ 修改后违反${violations.hardViolations.length}个硬约束！\n${violations.hardViolations.join('\n')}`, 
          'error'
        )
        console.warn('❌ 硬约束违反:', violations.hardViolations)
      } else if (violations.softViolations.length > 0) {
        showNotification(
          `修改成功！存在${violations.softViolations.length}个软约束违反`, 
          'warning'
        )
        console.log('⚠️ 软约束违反:', violations.softViolations)
      } else {
        showNotification(`✅ 修改成功！无约束违反`, 'success')
      }
    } catch (error) {
      console.error('约束验证失败:', error)
      // 即使验证失败，也继续显示基本成功提示
    const message = data.isForced ? 
      `⚠️ 强制修改成功！已修改${editingField.value} 为${data.teacher}，存在${data.conflicts.length} 个冲突` :
      `修改成功！已修改${editingField.value} 为${data.teacher}`
    showNotification(message, data.isForced ? 'warning' : 'success')
    }
    
    // ✨ 新增：重新计算工作量
    const newWorkload = calculateTeacherWorkload(data.teacher)
    const oldWorkload = oldValue ? calculateTeacherWorkload(oldValue) : 0
    console.log(`📊 工作量变化: ${oldValue}(${oldWorkload}) → ${data.teacher}(${newWorkload})`)
    
    // 标记数据已修改
    markAsModified()
  }
  
  closeEditModal()
}

// ✨ 验证人工修改后的约束
const validateManualEdit = async (record: any, field: string, newTeacher: string) => {
  const hardViolations: string[] = []
  const softViolations: string[] = []
  
  const examDate = record.examDate
  const studentDept = record.department
  
  try {
    // 获取考官信息
    const teacher = availableTeachers.value.find(t => t.name === newTeacher)
    if (!teacher) {
      hardViolations.push('考官不存在')
      return { hardViolations, softViolations }
    }
    
    // HC2: 主考官1必须与学员同科室
    if (field === 'examiner1_1' && teacher.department !== studentDept) {
      hardViolations.push(`HC2违反: 主考官1 ${newTeacher}(${teacher.department}) 与学员(${studentDept})不同科室`)
    }
    
    // HC3: 检查白班执勤冲突
    const dutySchedule = dutyRotationService.calculateDutySchedule(examDate)
    if (dutySchedule.dayShift === teacher.group) {
      hardViolations.push(`HC3违反: ${newTeacher} 在${examDate}执勤白班`)
    }
    
    // HC4: 检查时间冲突
    const hasConflict = scheduleResults.value.some(result => {
      const resultAny = result as any
      return resultAny.id !== record.id &&
        resultAny.examDate === examDate && [
          resultAny.examiner1_1, resultAny.examiner1_2, resultAny.backup1,
          resultAny.examiner2_1, resultAny.examiner2_2, resultAny.backup2
        ].includes(newTeacher)
    })
    if (hasConflict) {
      hardViolations.push(`HC4违反: ${newTeacher} 在${examDate}已有其他考试安排`)
    }
    
    // HC2: 检查同一学员的角色冲突
    const otherRoles = Object.keys(record).filter(key => 
      (key.includes('examiner') || key.includes('backup')) && 
      key !== field &&
      record[key] === newTeacher
    )
    if (otherRoles.length > 0) {
      hardViolations.push(`HC2违反: ${newTeacher} 已担任该学员的${otherRoles.join(', ')}`)
    }
    
    // 软约束检查
    // SC2/SC4: 专业匹配
    if (field.includes('examiner2') && teacher.department === studentDept) {
      softViolations.push(`SC2建议: 考官2建议选择不同科室，当前为${teacher.department}`)
    }
    
    // SC10: 工作量平衡
    const workload = calculateTeacherWorkload(newTeacher)
    if (workload > 5) {
      softViolations.push(`SC10建议: ${newTeacher} 工作量(${workload})较重，建议平衡`)
    }
    
  } catch (error) {
    console.error('约束验证异常:', error)
  }
  
  return { hardViolations, softViolations }
}

// 显示通知
const showNotification = (message: string, type: 'success' | 'warning' | 'error' = 'success') => {
  const notification = document.createElement('div')
  notification.textContent = message
  notification.style.cssText = `
    position: fixed; 
    top: 20px; 
    right: 20px; 
    background: ${type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : '#ef4444'}; 
    color: white; 
    padding: 12px 20px; 
    border-radius: 6px; 
    z-index: 9999; 
    font-weight: 500;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `
  document.body.appendChild(notification)
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification)
    }
  }, 5000)
}

// 编辑整个排班记录
const editScheduleRecord = (record: any) => {
  console.log('编辑排班记录:', record)
  // 这里可以打开一个更详细的编辑弹窗
  alert('详细编辑功能开发中...')
}

// 删除排班记录
const deleteScheduleRecord = (record: any) => {
    if (confirm(`确定要删除学员${record.student} 的排班记录吗？`)) {
    const index = scheduleResults.value.findIndex(r => r.id === record.id)
    if (index > -1) {
      scheduleResults.value.splice(index, 1)
      console.log('删除排班记录:', record.student)
      markAsModified()
    }
  }
}

// 标记数据已修改
const isModified = ref(false)
const markAsModified = () => {
  isModified.value = true
  console.log('排班数据已修改')
}

// 增强错误反馈处理方法
const handleAutoResolveConflict = async (conflict: ConflictInfo) => {
  try {
    const success = await enhancedErrorFeedbackService.autoResolveConflict(conflict)
    if (success) {
      showNotification('冲突已自动解决', 'success')
      // 刷新相关数据
      await refreshSchedulingData()
    } else {
      showNotification('自动解决失败，请手动处理', 'warning')
    }
  } catch (error) {
    console.error('自动解决冲突失败:', error)
    showNotification('自动解决冲突时发生错误', 'error')
  }
}

const handleExecuteAction = async (action: any) => {
  try {
    // 根据动作类型执行相应操作
    switch (action.type) {
      case 'adjust_time':
        // 调整时间冲突
        await adjustTimeConflict(action.data)
        break
      case 'reassign_examiner':
        // 重新分配考官
        await reassignExaminer(action.data)
        break
      case 'modify_constraint':
        // 修改约束配置
        await modifyConstraintConfig(action.data)
        break
      default:
        console.warn('未知的操作类型:', action.type)
    }
    showNotification('操作执行成功', 'success')
  } catch (error) {
    console.error('执行操作失败:', error)
    showNotification('操作执行失败', 'error')
  }
}

const handleExportReport = async () => {
  try {
    await enhancedErrorFeedbackService.exportErrorReport()
    showNotification('错误报告已导出', 'success')
  } catch (error) {
    console.error('导出报告失败:', error)
    showNotification('导出报告失败', 'error')
  }
}

// 辅助方法
const refreshSchedulingData = async () => {
  // 刷新排班数据的逻辑
  console.log('刷新排班数据...')
}

const adjustTimeConflict = async (data: any) => {
  // 调整时间冲突的逻辑
  console.log('调整时间冲突:', data)
}

const reassignExaminer = async (data: any) => {
  // 重新分配考官的逻辑
  console.log('重新分配考官:', data)
}

const modifyConstraintConfig = async (data: any) => {
  // 修改约束配置的逻辑
  console.log('修改约束配置:', data)
}

// 保存修改
const saveChanges = async () => {
  try {
    // 构建保存数据
    const scheduleRecord = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      title: `手动调整排班_${new Date().toLocaleDateString()}`,
      result: schedulingResult.value || {
        assignments: [],
        unassignedStudents: [],
        conflicts: [],
        statistics: {
          totalStudents: scheduleResults.value.length,
          assignedStudents: 0,
          unassignedStudents: scheduleResults.value.length,
          totalTeachers: 0,
          activeTeachers: 0,
          averageWorkload: 0,
          maxWorkload: 0,
          hardConstraintsSatisfied: 0,
          softConstraintsScore: 0,
          continuityRate: 0
        },
        warnings: []
      },
      displayData: scheduleResults.value,
      metadata: {
        studentCount: scheduleResults.value.length,
        teacherCount: 0, // 可以计算实际考官数量
        dateRange: examStartDate.value && examEndDate.value ? 
          `${examStartDate.value.toISOString().split('T')[0]} 到 ${examEndDate.value.toISOString().split('T')[0]}` : '未设置',
        constraints: {},
        isManuallyAdjusted: true
      }
    }
    
    await storageService.saveScheduleResult(scheduleRecord)
    isModified.value = false
    alert('排班结果已保存')
    console.log('排班结果已保存')
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败，请重试')
  }
}

// 导出功能
const exportToExcel = () => {
  try {
    if (scheduleResults.value.length === 0) {
      alert('没有数据可以导出')
      return
    }
    
    // 创建带有合并单元格的Excel格式HTML表格
    let htmlContent = `
      <html xmlns:x="urn:schemas-microsoft-com:office:excel">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
          <!--[if gte mso 9]>
          <xml>
            <x:ExcelWorkbook>
              <x:ExcelWorksheets>
                <x:ExcelWorksheet>
                  <x:Name>排班表</x:Name>
                  <x:WorksheetOptions>
                    <x:DisplayGridlines/>
                  </x:WorksheetOptions>
                </x:ExcelWorksheet>
              </x:ExcelWorksheets>
            </x:ExcelWorkbook>
          </xml>
          <![endif]-->
          <style>
            table { 
              border-collapse: collapse; 
              width: 100%;
              font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            th, td { 
              border: 1px solid #000; 
              padding: 8px; 
              text-align: center;
              white-space: nowrap;
              vertical-align: middle;
            }
            th { 
              background-color: #4472C4;
              color: white;
              font-weight: bold;
              font-size: 12px;
            }
            td {
              font-size: 11px;
            }
            .merged-cell {
              background-color: #E7E6E6;
              font-weight: bold;
            }
            .type-cell {
              background-color: #F2F2F2;
              font-weight: 500;
            }
            tr:nth-child(4n+1) td:not(.type-cell), tr:nth-child(4n+2) td:not(.type-cell) {
              background-color: #FFFFFF;
            }
            tr:nth-child(4n+3) td:not(.type-cell), tr:nth-child(4n+4) td:not(.type-cell) {
              background-color: #F8F9FA;
            }
          </style>
        </head>
        <body>
          <table>
            <thead>
              <tr>
                <th>所在科室</th>
                <th>学员姓名</th>
                <th>第一天日期</th>
                <th>第一天类型</th>
                <th>第一天考官一</th>
                <th>第一天考官二</th>
                <th>第一天备份考官</th>
                <th>第二天日期</th>
                <th>第二天类型</th>
                <th>第二天考官一</th>
                <th>第二天考官二</th>
                <th>第二天备份考官</th>
              </tr>
            </thead>
            <tbody>
    `
    
    // 添加数据 - 每个学员一行，不重复考官
    scheduleResults.value.forEach((result, index) => {
      const date1 = result.date1 || ''
      const date2 = result.date2 || ''

      // 每个学员只生成一行数据
      htmlContent += '              <tr>\n'
      htmlContent += `                <td class="merged-cell">${result.department || ''}</td>\n`
      htmlContent += `                <td class="merged-cell">${result.student || ''}</td>\n`
      htmlContent += `                <td class="merged-cell" style="mso-number-format:'\\@';">${date1}</td>\n`
      htmlContent += `                <td class="type-cell">现场+模拟机</td>\n`
      htmlContent += `                <td>${result.examiner1_1 || ''}</td>\n`
      htmlContent += `                <td>${result.examiner1_2 || ''}</td>\n`
      htmlContent += `                <td>${result.backup1 || ''}</td>\n`
      htmlContent += `                <td class="merged-cell" style="mso-number-format:'\\@';">${date2}</td>\n`
      htmlContent += `                <td class="type-cell">模拟机+口试</td>\n`
      htmlContent += `                <td>${result.examiner2_1 || ''}</td>\n`
      htmlContent += `                <td>${result.examiner2_2 || ''}</td>\n`
      htmlContent += `                <td>${result.backup2 || ''}</td>\n`
      htmlContent += '              </tr>\n'
    })
    
    htmlContent += `            </tbody>
          </table>
        </body>
      </html>
    `
    
    // 添加BOM头解决中文乱码问题
    const BOM = '\uFEFF'
    const content = BOM + htmlContent
    
      // 创建Blob并下载
    const blob = new Blob([content], { 
      type: 'application/vnd.ms-excel;charset=utf-8' 
    })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `排班结果_${new Date().toLocaleDateString().replace(/\//g, '-')}.xls`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    console.log('导出完成')
    alert('导出成功！文件已下载到您的下载文件夹')
  } catch (error) {
    console.error('导出失败:', error)
    alert('导出失败，请重试')
  }
}

// 清除所有缓存数据
const clearAllCacheData = async () => {
  console.log('🧹 开始清除所有缓存数据.')
  
  try {
    // 使用 storageService 清除数据
    await storageService.clearAllData()
    
    // 清除其他可能的缓存键
    const allKeys = Object.keys(localStorage)
    const cacheKeys = allKeys.filter(key => 
      key.includes('examiner') || 
      key.includes('teacher') || 
      key.includes('schedule') || 
      key.includes('api_') ||
      key.includes('system') ||
      key.includes('latest_schedule')
    )
    
    console.log('📦 发现的缓存键:', cacheKeys)
    
    cacheKeys.forEach(key => {
      localStorage.removeItem(key)
      console.log(`✅已清除 ${key}`)
    })
    
    // 清除 sessionStorage
    const sessionKeys = Object.keys(sessionStorage).filter(key => 
      key.includes('examiner') || 
      key.includes('teacher') || 
      key.includes('schedule') || 
      key.includes('api_') ||
      key.includes('system')
    )
    
    sessionKeys.forEach(key => {
      sessionStorage.removeItem(key)
      console.log(`✅已清除sessionStorage: ${key}`)
    })
    
    console.log('🎉 所有缓存数据已清除')
    
  } catch (error) {
    console.error('清除缓存数据失败:', error)
  }
}

// 自动收缩侧边栏功能
const checkContentOverflow = () => {
  const container = document.querySelector('.app-container')
  const mainContent = document.querySelector('.main-content')
  const scheduleTable = document.querySelector('.schedule-table')
  
  if (!container || !mainContent || !scheduleTable) return
  
  const containerWidth = container.clientWidth
  const tableWidth = scheduleTable.scrollWidth
  const mainContentPadding = 64 // 32px * 2
  const sidebarWidth = sidebarCollapsed.value ? 80 : 280
  
  // 计算可用宽度
  const availableWidth = containerWidth - sidebarWidth - mainContentPadding
  
  // 如果表格宽度超过可用宽度且侧边栏未收缩，则自动收缩
  if (tableWidth > availableWidth && !sidebarCollapsed.value) {
    console.log('检测到内容溢出，自动收缩侧边栏')
    sidebarCollapsed.value = true
  }
}

// 窗口大小变化监听
const handleResize = () => {
  checkContentOverflow()
}

// 响应式设计相关方法
const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
  
  // 更新设备类型
  isMobile.value = screenWidth.value < 768
  isTablet.value = screenWidth.value >= 768 && screenWidth.value < 1024
  isDesktop.value = screenWidth.value >= 1024
  
  // 移动端自动收起侧边栏
  if (isMobile.value && !sidebarCollapsed.value) {
    sidebarCollapsed.value = true
  }
  
  // 移动端关闭菜单
  if (isMobile.value) {
    mobileMenuOpen.value = false
  }
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  if (isMobile.value) {
    mobileMenuOpen.value = !mobileMenuOpen.value
  }
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  if (isMobile.value) {
    mobileMenuOpen.value = false
  }
}

// 响应式侧边栏切换
const toggleSidebar = () => {
  if (isMobile.value) {
    toggleMobileMenu()
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

// 页面加载时恢复排班结果
onMounted(async () => {
  // 🔧 设置更合理的默认日期范围（六周，约30个工作日）
  if (!examStartDateStr.value && !examEndDateStr.value) {
    setQuickDateRange(45) // 默认六周，提供充足的日期选择空间
  }
  
  // 检查URL参数，如果有action=create则直接打开新建排班模态框
  if (route.query.action === 'create') {
    showCreateModal.value = true
  }
  
  try {
    // 加载学员数据
    await loadStudentData()
    
    // 加载教师数据
    try {
      const teachers = await prepareTeacherData()
      teacherList.value = teachers
      cachedTeacherData = teachers
      console.log('页面初始化时加载教师数据完成，数量:', teachers.length)
    } catch (error) {
      console.warn('页面初始化时加载教师数据失败:', error)
    }
    
    // 检查是否有缓存的排班结果
    // 🔧 临时禁用缓存恢复，避免旧数据干扰调试
    console.warn('⚠️ [调试模式] 已禁用缓存恢复功能，确保显示最新的排班结果')
    scheduleResults.value = []
    
    /* 
    // 原缓存恢复逻辑（已临时禁用）
    const savedResult = await storageService.loadLatestScheduleResult()
    if (savedResult && savedResult.displayData) {
      console.log('发现缓存的排班结果', savedResult.title)
      console.log('缓存时间:', savedResult.timestamp)
      
      // 检查缓存是否过期（超过1小时则认为过期）
      const cacheTime = new Date(savedResult.timestamp)
      const now = new Date()
      const hoursDiff = (now.getTime() - cacheTime.getTime()) / (1000 * 60 * 60)
      
      if (hoursDiff > 1) {
        console.log('⚠️ 缓存已过期，清除旧数据')
        scheduleResults.value = []
        // 清除过期缓存
        localStorage.removeItem('latest_schedule_result')
      } else {
        console.log('✅恢复缓存的排班结果')
        scheduleResults.value = savedResult.displayData
        
        // 添加缓存数据验证
        console.log('🔍 缓存数据验证:', {
          recordCount: savedResult.displayData.length,
          firstRecord: savedResult.displayData[0],
          hasValidData: savedResult.displayData.every((record: any) => 
            record.student && record.examiner1_1 && record.examiner1_2
          )
        })
        
        // 如果缓存数据有问题，清除它
        const hasInvalidData = savedResult.displayData.some((record: any) => 
          record.examiner1_1 === record.examiner1_2 || 
          record.examiner2_1 === record.examiner2_2
        )
        
        if (hasInvalidData) {
          console.log('⚠️检测到缓存数据有重复考官问题，清除缓存')
          scheduleResults.value = []
          
          // 彻底清除所有相关缓存
          await clearAllCacheData()
        }
      }
    } else {
      console.log('没有找到保存的排班结果')
      scheduleResults.value = []
    }
    */
  } catch (error) {
    console.error('初始化失败:', error)
    scheduleResults.value = []
  }
  
  // 初始化响应式设计
  updateScreenSize()
  
  
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
  window.addEventListener('resize', updateScreenSize)
  
  // 初始检查
  nextTick(() => {
    setTimeout(checkContentOverflow, 100) // 延迟检查确保DOM完全渲染
  })
})

// 组件卸载时清理监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('resize', updateScreenSize)
  
  if (realtimeProgressUnsubscribe) {
    realtimeProgressUnsubscribe()
    realtimeProgressUnsubscribe = null
  }
  if (realtimeProgressServiceInstance && typeof realtimeProgressServiceInstance.disconnect === 'function') {
    realtimeProgressServiceInstance.disconnect()
  }
  activeRealtimeSessionId = null
})

// script setup 中，所有的响应式变量和函数都会自动暴露给模板
// 不需要显式的 return 语句
</script>   

<style scoped>
/* 移除集成状态面板样式 */

/* 🎬 表格行渐进式动画效果 */
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 新出现的行自动应用动画 */
.schedule-table tbody tr {
  animation: slideInFromLeft 0.4s ease-out, fadeIn 0.4s ease-out;
}

/* 实时更新时的高亮效果 */
.schedule-table tbody tr.new-row {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);
  animation: slideInFromLeft 0.5s ease-out, fadeIn 0.5s ease-out;
}

/* 表格更新中的脉冲效果 */
.table-container.updating {
  position: relative;
}

.table-container.updating::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    #3b82f6 50%, 
    transparent 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  z-index: 10;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 🎯 实时计算流程窗口样式 */
.realtime-progress-window {
  width: 400px;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  user-select: none; /* 防止拖拽时选中文本 */
}

.draggable-realtime-window {
  position: fixed;
  top: 0;
  left: 0;
}

.draggable-realtime-window.dragging {
  transition: none; /* 拖拽时移除过渡效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
  transform-origin: center;
  z-index: 1001;
}

.realtime-progress-window.minimized {
  height: 60px;
  overflow: hidden;
}

.window-header.draggable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  cursor: grab;
  user-select: none;
}

.window-header.draggable-header:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.window-header.draggable-header:active {
  cursor: grabbing;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
}

.window-title .icon {
  animation: spin 2s linear infinite;
}

.drag-hint {
  font-size: 10px;
  opacity: 0.7;
  margin-left: auto;
  font-style: italic;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.window-controls {
  display: flex;
  gap: 8px;
}

.reset-btn, .minimize-btn, .close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  margin-left: 4px;
}

.reset-btn:hover, .minimize-btn:hover, .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.reset-btn {
  background: rgba(34, 197, 94, 0.3);
}

.reset-btn:hover {
  background: rgba(34, 197, 94, 0.5);
}

.window-content {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.realtime-section {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.realtime-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.realtime-section h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.progress-bar-mini {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill-mini {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #3b82f6);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-info {
  font-size: 12px;
  color: #6b7280;
}
.progress-softscore {
  margin-top: 4px;
  font-size: 12px;
  color: #2563eb;
  display: flex;
  gap: 8px;
}
.progress-softscore-best {
  color: #059669;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
}

.softscore-stat {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.stat-value.assigned {
  color: #10b981;
}

.stat-value.progress {
  color: #3b82f6;
}

.stat-hint {
  display: block;
  font-size: 12px;
  color: #059669;
}

.log-container {
  max-height: 150px;
  overflow-y: auto;
  background: #f9fafb;
  border-radius: 6px;
  padding: 8px;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.4;
}

.log-entry:last-child {
  margin-bottom: 0;
}

.log-time {
  color: #6b7280;
  font-family: monospace;
  flex-shrink: 0;
  font-weight: 500;
}

.log-message {
  color: #374151;
  flex: 1;
}

.log-entry.success .log-message {
  color: #059669;
}

.log-entry.warning .log-message {
  color: #d97706;
}

.log-entry.error .log-message {
  color: #dc2626;
}

.table-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.status-indicator.active {
  color: #10b981;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b7280;
  transition: all 0.3s ease;
}

/* OptaPlanner风格的状态指示器样式 */
.status-indicator.animating {
  color: #f59e0b;
}

.status-indicator.updating {
  color: #3b82f6;
  font-weight: 600;
}

.status-indicator.completed {
  color: #059669;
}

/* 🔄 OptaPlanner风格的增量更新动画 */
.schedule-table .incremental-update-row {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);
  animation: incrementalUpdate 1s ease-out;
}

.schedule-table .new-assignment-row {
  background: linear-gradient(90deg, rgba(34, 197, 94, 0.15) 0%, transparent 100%);
  animation: newAssignment 1.2s ease-out;
}

.schedule-table .modified-assignment-row {
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.12) 0%, transparent 100%);
  animation: modifiedAssignment 1s ease-out;
}

@keyframes incrementalUpdate {
  0% {
    background: rgba(59, 130, 246, 0.3);
    transform: translateX(-10px);
  }
  100% {
    background: transparent;
    transform: translateX(0);
  }
}

@keyframes newAssignment {
  0% {
    background: rgba(34, 197, 94, 0.4);
    transform: scale(0.98);
  }
  50% {
    background: rgba(34, 197, 94, 0.2);
    transform: scale(1.01);
  }
  100% {
    background: transparent;
    transform: scale(1);
  }
}

@keyframes modifiedAssignment {
  0% {
    background: rgba(245, 158, 11, 0.3);
    border-left: 3px solid #f59e0b;
  }
  100% {
    background: transparent;
    border-left: none;
  }
}

/* OptaPlanner风格的实时更新指示器 */
.realtime-update-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  font-size: 12px;
  color: #3b82f6;
  font-weight: 500;
}

.realtime-update-indicator .pulse-dot {
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  animation: optaPlannerPulse 1.5s infinite;
}

@keyframes optaPlannerPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.3);
  }
}

.status-indicator.active .indicator-dot {
  background: #10b981;
  animation: pulse 1.5s infinite;
}

.status-indicator.updating .indicator-dot {
  background: #3b82f6;
  animation: optaPlannerUpdatePulse 0.8s infinite;
}

.status-indicator.completed .indicator-dot {
  background: #059669;
  animation: completePulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes optaPlannerUpdatePulse {
  0%, 100% { 
    opacity: 1; 
    background: #3b82f6;
  }
  50% { 
    opacity: 0.7; 
    background: #60a5fa;
  }
}

@keyframes updatePulse {
  0%, 100% { 
    opacity: 1; 
    background: #3b82f6;
  }
  50% { 
    opacity: 0.7; 
    background: #60a5fa;
  }
}

@keyframes completePulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  10% { 
    opacity: 0.8; 
    transform: scale(1.1);
  }
}

.last-update {
  font-size: 11px;
  color: #9ca3af;
}

.animation-info {
  font-size: 11px;
  color: #3b82f6;
  font-style: italic;
  margin-top: 4px;
}

/* 🎬 OptaPlanner风格的求解动画样式 */
.table-cell-animating {
  position: relative;
  background: linear-gradient(90deg, #f8fafc, #e2e8f0, #f8fafc);
  background-size: 200% 100%;
  animation: optaPlannerSolving 2s ease-in-out infinite;
  border: 1px solid #cbd5e1;
}

.table-cell-typing {
  background: linear-gradient(45deg, #fef3c7, #fde68a);
  color: #92400e;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  animation: variableAssignment 1.5s ease-in-out infinite;
}

.table-cell-selecting {
  background: linear-gradient(45deg, #dbeafe, #93c5fd);
  color: #1d4ed8;
  font-weight: 500;
  border: 1px solid #3b82f6;
  animation: constraintChecking 1.2s ease-in-out infinite alternate;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.table-cell-confirming {
  background: linear-gradient(45deg, #d1fae5, #86efac);
  color: #065f46;
  font-weight: 600;
  border: 1px solid #10b981;
  animation: solutionOptimizing 0.8s ease-in-out;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

/* OptaPlanner求解过程动画 */
@keyframes optaPlannerSolving {
  0% { 
    background-position: -200% 0;
    transform: scale(1);
  }
  50% { 
    background-position: 0% 0;
    transform: scale(1.01);
  }
  100% { 
    background-position: 200% 0;
    transform: scale(1);
  }
}

@keyframes variableAssignment {
  0% { 
    background: #fef3c7; 
    opacity: 0.7;
  }
  50% { 
    background: #fde68a; 
    opacity: 1;
  }
  100% { 
    background: #fef3c7; 
    opacity: 0.7;
  }
}

@keyframes constraintChecking {
  0% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    background: #dbeafe;
  }
  100% { 
    transform: scale(1.02); 
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    background: #93c5fd;
  }
}

@keyframes solutionOptimizing {
  0% { 
    background: #d1fae5; 
    transform: scale(1);
  }
  50% { 
    background: #86efac; 
    transform: scale(1.03);
  }
  100% { 
    background: #d1fae5; 
    transform: scale(1);
  }
}

/* OptaPlanner求解阶段行高亮 */
.schedule-table tbody tr.animating-row {
  background: linear-gradient(90deg, #f8fafc, #f1f5f9);
  border-left: 4px solid #3b82f6;
  transition: all 0.4s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

  .schedule-table tbody tr.animating-row:hover {
    background: linear-gradient(90deg, #f1f5f9, #e2e8f0);
    transform: translateX(2px);
  }
  
  /* OptaPlanner求解状态样式 */
  .stat-value.constraint {
    color: #7c3aed;
    font-weight: 600;
  }

/* CSS变量定义 */
:root {
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --content-padding: 24px;
  --border-radius: 12px;
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 主容器- 响应式优化*/
.app-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
  position: relative;
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(2px);
}

/* 响应式布局*/
.mobile-layout {
  flex-direction: column;
}

.tablet-layout {
  gap: 20px;
}

.desktop-layout {
  gap: 24px;
}

/* 移动端适配 */
@media (max-width: 767px) {
  .app-container {
    flex-direction: column;
  }
}

/* 平板端适配 */
@media (min-width: 768px) and (max-width: 1023px) {
  .app-container {
    width: 100vw;
    height: 100vh;
  }
}

/* 桌面端适配 */
@media (min-width: 1024px) {
  .app-container {
    width: 100vw;
    height: 100vh;
  }
}

/* 侧边栏样式- 响应式优化*/
.sidebar {
  width: var(--sidebar-width);
  height: 100%;
  background: linear-gradient(180deg, #1e3a5f 0%, #2c5282 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1000;
}

.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
}

/* 移动端侧边栏 */
@media (max-width: 767px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .sidebar-collapsed {
    transform: translateX(calc(-100% + var(--sidebar-collapsed-width)));
  }
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-img {
  width: 40px;
  height: 40px;
  object-fit: cover;
}

.logo-text {
  flex: 1;
}

.system-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.system-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 导航样式 */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 20px;
}

.sidebar-collapsed .nav-items {
  padding: 0 10px;
}

.sidebar-collapsed .nav-item {
  justify-content: center;
  padding: 12px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item-active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* 侧边栏切换按钮*/
.sidebar-toggle {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;
}

.sidebar-toggle:hover {
  transform: translateY(-50%) scale(1.1);
}

.toggle-icon {
  width: 16px;
  height: 16px;
  color: #374151;
  transition: transform 0.3s ease;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

/* 主内容区域 - 响应式优化*/
.main-content {
  flex: 1;
  height: 100%;
  background: #f5f7fa;
  display: flex;
  gap: 24px;
  padding: var(--content-padding);
  overflow: auto; /* 修改为auto，允许横向和纵向滚动 */
  transition: all 0.3s ease;
}

/* 移动端主内容区域 */
@media (max-width: 767px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
    padding: var(--content-padding);
    margin-left: 0;
  }
  
  .main-content.sidebar-open {
    margin-left: var(--sidebar-collapsed-width);
  }
}

/* 平板端主内容区域 */
@media (min-width: 768px) and (max-width: 1023px) {
  .main-content {
    gap: 20px;
    padding: var(--content-padding);
  }
}

/* 桌面端及以上 */
@media (min-width: 1024px) {
  .main-content {
    gap: 24px;
  }
}

/* 约束条件面板 */
.constraints-panel {
  width: 320px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  flex-shrink: 0;
}

.panel-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24px;
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.right-content.full-width {
  width: 100%;
}

/* 页面标题区 - 响应式优化*/
.page-header {
  background: white;
  border-radius: var(--border-radius);
  padding: 20px 24px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow);
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 移动端页面标题栏 */
@media (max-width: 767px) {
  .page-header {
    padding: 16px 20px;
    margin-bottom: 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .page-title {
    text-align: center;
    font-size: 1.25rem;
  }
  
  .header-actions {
    justify-content: center;
    gap: 8px;
  }
}

/* 平板端页面标题栏 */
@media (min-width: 768px) and (max-width: 1023px) {
  .page-header {
    padding: 18px 22px;
    margin-bottom: 20px;
  }
  
  .page-title {
    font-size: 1.375rem;
  }
  
  .header-actions {
    gap: 10px;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn-secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn-secondary:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 🆕 实时更新提示横幅 */
.realtime-update-banner {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 2px solid #3b82f6;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  animation: pulse-border 2s ease-in-out infinite;
}

.update-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #1e40af;
  font-weight: 600;
  font-size: 14px;
}

.update-count {
  margin-left: auto;
  background: rgba(59, 130, 246, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  color: #2563eb;
  animation: countUpdate 0.3s ease-out;
}

@keyframes countUpdate {
  0% {
    transform: scale(1.1);
    background: rgba(59, 130, 246, 0.3);
  }
  100% {
    transform: scale(1);
    background: rgba(59, 130, 246, 0.1);
  }
}

.loading-dots {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #3b82f6;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-color: #93c5fd;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes table-updating {
  0%, 100% {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  50% {
    box-shadow: 0 8px 20px -4px rgba(59, 130, 246, 0.3), 0 4px 8px -2px rgba(59, 130, 246, 0.2);
  }
}

/* 表格容器 - 响应式优化*/
.table-container {
  background: white;
  border-radius: var(--border-radius);
  overflow: auto;
  box-shadow: var(--shadow);
  max-height: calc(100vh - 200px);
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  position: relative;
  transition: all 0.3s ease;
}

/* 🆕 表格更新中的样式 */
.table-container.updating {
  border: 2px solid #3b82f6;
  animation: table-updating 1.5s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* 实时更新行闪烁效果 */
@keyframes row-flash {
  0% {
    background-color: rgba(59, 130, 246, 0.2);
    transform: scale(1.01);
  }
  50% {
    background-color: rgba(34, 197, 94, 0.3);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}

.schedule-table tbody tr.realtime-update {
  animation: row-flash 1s ease-out;
}

.table-container::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.table-container::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.table-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 6px;
  border: 2px solid #f8fafc;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.table-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, #1d4ed8, #1e3a8a);
}

.table-container::-webkit-scrollbar-corner {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

/* 移动端表格容器*/
@media (max-width: 767px) {
  .table-container {
    max-height: calc(100vh - 160px);
    border-radius: var(--border-radius);
    margin: 0 -4px;
  }
  
  .table-container::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
}

/* 平板端表格容器*/
@media (min-width: 768px) and (max-width: 1023px) {
  .table-container {
    max-height: calc(100vh - 180px);
  }
  
  .table-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

/* 桌面端及以上 */
@media (min-width: 1024px) {
  .table-container {
    max-height: calc(100vh - 200px);
  }
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  min-width: 800px;
}

.schedule-table th {
  background: #f9fafb;
  padding: 16px 20px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.schedule-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
  vertical-align: top;
  transition: all 0.3s ease, background-color 0.5s ease; /* 🎬 添加过渡效果 */
}

/* 🎬 数据变化时的高亮效果 */
.schedule-table td.data-changed {
  background-color: rgba(59, 130, 246, 0.1) !important;
  animation: cellPulse 0.6s ease;
}

@keyframes cellPulse {
  0%, 100% { 
    background-color: transparent; 
  }
  50% { 
    background-color: rgba(59, 130, 246, 0.2); 
  }
}

/* 移动端表格样式*/
@media (max-width: 767px) {
  .schedule-table {
    font-size: 0.75rem;
    min-width: 600px;
  }
  
  .schedule-table th,
  .schedule-table td {
    padding: 12px 8px;
  }
  
  .schedule-table th {
    font-size: 0.75rem;
  }
  
  /* 隐藏部分列以适应小屏幕*/
  .schedule-table th:nth-child(n+8),
  .schedule-table td:nth-child(n+8) {
    display: none;
  }
}

/* 平板端表格样式*/
@media (min-width: 768px) and (max-width: 1023px) {
  .schedule-table {
    font-size: 0.8125rem;
    min-width: 700px;
  }
  
  .schedule-table th,
  .schedule-table td {
    padding: 14px 16px;
  }
}

/* 桌面端表格样式*/
@media (min-width: 1024px) {
  .schedule-table {
    font-size: 0.875rem;
  }
  
  .schedule-table th,
  .schedule-table td {
    padding: 16px 20px;
  }
}

.schedule-table td {
  height: 60px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.schedule-table tbody tr:hover {
  background: #f9fafb;
}

.schedule-table tbody tr:last-child td {
  border-bottom: none;
}

.department-cell,
.student-cell {
  vertical-align: middle;
  text-align: center;
  font-weight: 500;
  background: #f9fafb;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}

/* 预览弹窗更高层级 */
.preview-modal-overlay {
  z-index: 999999;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 1200px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 可拖拽弹窗样式*/
.draggable-modal {
  position: relative;
  transition: none;
}

.draggable-modal.dragging {
  transition: none;
}

/* 拖拽标题栏样式*/
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  border-radius: 16px 16px 0 0;
}

.draggable-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}



/* 导入步骤样式 */
.import-area {
  margin-bottom: 24px;
}

.upload-zone {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 48px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-zone:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.upload-icon {
  width: 48px;
  height: 48px;
  color: #9ca3af;
  margin: 0 auto 16px;
}

.upload-text {
  font-size: 18px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 14px;
  color: #6b7280;
}

/* 文件信息样式 */
.file-info {
  margin-bottom: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f3f4f6;
  border-radius: 8px;
}

.file-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #374151;
}

.view-icon,
.delete-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  cursor: pointer;
}

.delete-icon:hover {
  color: #ef4444;
}

.support-text {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
}

/* 时间选择区域 */
.time-selection-section {
  margin-top: 24px;
}

.exam-date-form {
  margin: 20px 0;
}

.date-description {
  margin-bottom: 20px;
  padding: 16px;
  background: #fefce8;
  border: 1px solid #fde047;
  border-radius: 8px;
}

.description-text {
  margin: 0;
  font-size: 14px;
  color: #a16207;
  line-height: 1.5;
}

.time-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.date-range-row {
  margin-bottom: 16px;
}

.time-label {
  min-width: 120px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.exam-date-picker {
  flex: 1;
  max-width: 300px;
}

.date-range-container {
  width: 100%;
  margin: 16px 0;
  overflow: visible;
  position: relative;
}

.exam-date-range-picker {
  width: 100%;
}

.time-tips {
  margin: 20px 0;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 8px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #0369a1;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 16px;
  height: 16px;
  color: #0284c7;
}

/* 下一步区域*/
.next-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.next-btn {
  background: #3b82f6;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.next-btn:hover {
  background: #2563eb;
}

/* 学员列表样式 */
.student-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.student-item {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
}

.student-item:last-child {
  border-bottom: none;
}

.student-department {
  color: #374151;
  font-weight: 500;
}

.student-name {
  color: #1f2937;
  font-weight: 600;
}

.exam-type {
  color: #6b7280;
}

/* 约束条件样式 */
.constraints-section {
  margin-bottom: 24px;
}

.constraints-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.constraint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.constraint-item:last-child {
  border-bottom: none;
}

.constraint-text {
  font-size: 14px;
  color: #374151;
}

/* 双列布局样式 */
.modal-layout {
  display: flex;
  min-height: 600px;
}

.left-panel {
  flex: 1;
  padding: 32px;
  border-right: 1px solid #f3f4f6;
  background: #fafbfc;
}

.right-panel {
  flex: 1;
  padding: 32px;
  background: white;
}

.panel-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24px;
}

/* 时间选择区域样式 */
.time-selection-area {
  margin-top: 32px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.time-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.time-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.time-input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #1f2937;
  transition: all 0.2s ease;
}

.time-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.generate-section {
  width: 100%;
}

.generate-btn {
  width: 100%;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generate-btn:hover:not(:disabled) {
  background: #2563eb;
}

.generate-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background: #6b7280;
}

.generate-btn.loading {
  background: #6b7280;
}

/* 进度条样式*/
.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  margin-top: 12px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 错误提示样式 */
.error-message {
  margin-top: 12px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  line-height: 1.4;
}

/* 约束条件样式 */
.constraint-group {
  margin-bottom: 32px;
}

.constraint-title {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.constraint-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.constraint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.constraint-text {
  font-size: 14px;
  color: #374151;
  flex: 1;
}

.constraint-footer {
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid #f3f4f6;
}

.constraint-note {
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
  margin: 0;
}

/* 文件预览弹窗样式 */
.preview-modal-content {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 900px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  margin: auto;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid #f3f4f6;
  background: #f8fafc;
}

.preview-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  padding: 8px;
  background: none;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.preview-body {
  padding: 24px 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.preview-info {
  margin-bottom: 20px;
}

.file-info-text {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.data-info-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.preview-table-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.preview-table th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.preview-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #1f2937;
}

.preview-table tbody tr:last-child td {
  border-bottom: none;
}

.preview-table tbody tr:hover {
  background: #f9fafb;
}

/* 开关样式*/
.toggle-switch {
  width: 44px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: background 0.2s ease;
  flex-shrink: 0;
}

.toggle-switch.active {
  background: #10b981;
}

.toggle-handle {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-switch.active .toggle-handle {
  transform: translateX(20px);
}

.constraints-note {
  font-size: 14px;
  color: #6b7280;
  margin: 24px 0 16px;
}

.start-schedule-btn {
  background: #3b82f6;
  color: white;
  padding: 12px 32px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.start-schedule-btn:hover {
  background: #2563eb;
}

/* 分步骤弹窗样式*/
.step-modal {
  width: 800px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
}

/* 步骤指示器样式*/
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  background: #e5e7eb;
  color: #6b7280;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #3b82f6;
  color: white;
}

.step-item.completed .step-number {
  background: #10b981;
  color: white;
}

.step-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}

.step-item.active .step-label {
  color: #3b82f6;
  font-weight: 600;
}

.step-item.completed .step-label {
  color: #10b981;
  font-weight: 600;
}

.step-divider {
  flex: 1;
  height: 2px;
  background: #e5e7eb;
  margin: 0 16px;
  max-width: 40px;
}

/* 步骤内容样式 */
.step-content {
  padding: 32px;
  min-height: 400px;
}

.step-title {
  text-align: center;
  margin-bottom: 32px;
}

.step-title h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.step-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* 文件上传区域样式 */
.file-upload-area {
  margin-bottom: 32px;
}

.upload-placeholder {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 48px 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-placeholder:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.upload-icon {
  color: #9ca3af;
  margin-bottom: 16px;
}

.upload-placeholder:hover .upload-icon {
  color: #3b82f6;
}

.upload-text {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.upload-subtext {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.file-icon {
  color: #3b82f6;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.file-size {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.change-file-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.change-file-btn:hover {
  background: #2563eb;
}

/* 学员预览样式 */
.student-preview {
  margin-top: 32px;
}

.preview-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.student-preview h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.show-more-btn, .show-less-btn {
  padding: 6px 12px;
  font-size: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.show-more-btn:hover, .show-less-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.preview-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.preview-header {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 1fr 1.5fr;
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.preview-header span {
  padding: 12px 16px;
  border-right: 1px solid #e5e7eb;
  font-size: 14px;
}

.preview-header span:last-child {
  border-right: none;
}

.preview-rows {
  background: white;
}

.preview-row {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 1fr 1.5fr;
  border-bottom: 1px solid #f3f4f6;
}

.preview-row:last-child {
  border-bottom: none;
}

.preview-row span {
  padding: 12px 16px;
  border-right: 1px solid #f3f4f6;
  color: #6b7280;
  font-size: 14px;
}

.preview-row span:last-child {
  border-right: none;
}

.recommended-examiners {
  font-size: 12px;
  color: #059669;
}

.preview-more {
  padding: 12px 16px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  background: #f9fafb;
  grid-column: 1 / -1;
}

.data-summary {
  display: flex;
  gap: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  font-weight: 500;
  color: #475569;
  font-size: 14px;
}

.summary-value {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

/* 快速日期选择样式 */
.quick-date-selection {
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16px;
  border: 1px solid #bfdbfe;
}

.quick-date-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-date-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-date-btn {
  padding: 10px 16px;
  background: white;
  border: 2px solid #e0f2fe;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #0369a1;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.quick-date-btn:hover {
  background: #f0f9ff;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.quick-date-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 日期选择样式 */
.date-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.date-label {
  font-weight: 600;
  color: #374151;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-label-tip {
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  font-style: italic;
}

.date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input {
  width: 100%;
  padding: 16px 50px 16px 16px;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  background: white;
  transition: all 0.2s ease;
}

.date-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.date-input-icon {
  position: absolute;
  right: 16px;
  font-size: 20px;
  color: #6b7280;
  pointer-events: none;
}

/* 智能建议样式 */
.date-suggestion {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 16px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.suggestion-icon {
  font-size: 20px;
}

.suggestion-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #92400e;
}

.suggestion-text {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #92400e;
  line-height: 1.5;
}

.suggestion-btn {
  padding: 8px 16px;
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
}

/* 增强的日期信息样式 */
.date-info-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
}

.date-info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.info-header-icon {
  font-size: 24px;
}

.info-header-title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.info-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-card.success {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.info-card.warning {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.info-card-icon {
  font-size: 24px;
  min-width: 24px;
}

.info-card.success .info-card-icon {
  color: #10b981;
}

.info-card.warning .info-card-icon {
  color: #f59e0b;
}

.info-card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-card-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-card-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

/* 日期详情样式 */
.date-details {
  margin-bottom: 24px;
}

.date-details-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.details-icon {
  font-size: 16px;
  color: #6b7280;
}

.details-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.details-count {
  font-size: 12px;
  color: #6b7280;
}

.date-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.date-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e5e7eb;
  background: white;
}

.date-tag.workday {
  background: #ecfdf5;
  border-color: #10b981;
  color: #065f46;
}

.date-tag.weekend {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.date-more {
  padding: 6px 12px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

/* 增强的容量评估样式 */
.capacity-assessment {
  padding: 24px;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border: 2px solid #eab308;
  border-radius: 20px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(234, 179, 8, 0.1);
}

.capacity-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.capacity-icon {
  font-size: 24px;
}

.capacity-title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #a16207;
  flex: 1;
}

.capacity-badge {
  padding: 4px 12px;
  background: #f59e0b;
  color: white;
  font-size: 12px;
  font-weight: 500;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.capacity-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 容量指标网格 */
.capacity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.capacity-metric {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #fbbf24;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.capacity-metric:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.2);
}

.metric-icon {
  font-size: 20px;
  min-width: 20px;
}

.metric-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  font-weight: 500;
  color: #92400e;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
}

.metric-value.theoretical {
  color: #7c3aed;
  font-weight: 800;
}

.metric-value.success {
  color: #065f46;
}

.metric-value.warning {
  color: #92400e;
}

.metric-value.danger {
  color: #dc2626;
}

.metric-value.info {
  color: #1e40af;
}

/* 容量利用率进度条 */
.capacity-utilization {
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #fbbf24;
  border-radius: 12px;
}

.utilization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.utilization-label {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.utilization-value {
  font-size: 16px;
  font-weight: 700;
}

.utilization-bar {
  width: 100%;
  height: 8px;
  background: #fef3c7;
  border-radius: 4px;
  overflow: hidden;
}

.utilization-fill {
  height: 100%;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.utilization-fill.success {
  background: linear-gradient(90deg, #10b981, #059669);
}

.utilization-fill.warning {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.utilization-fill.danger {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.utilization-fill.info {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

/* 约束条件分析 */
.constraint-analysis {
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #fbbf24;
  border-radius: 12px;
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.analysis-icon {
  font-size: 16px;
  color: #92400e;
}

.analysis-title {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.constraint-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.constraint-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.constraint-label {
  font-weight: 500;
  color: #92400e;
  min-width: 140px;
}

.constraint-value {
  font-weight: 600;
  color: #1f2937;
}

.constraint-bottleneck {
  font-weight: 600;
  color: #dc2626;
  flex: 1;
}

/* 容量状态 */
.capacity-status {
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  margin-top: 4px;
}

.capacity-status.success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #065f46;
  border: 2px solid #10b981;
}

.capacity-status.warning {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 2px solid #f59e0b;
}

.capacity-status.danger {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  color: #dc2626;
  border: 2px solid #ef4444;
}

.capacity-status.info {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border: 2px solid #3b82f6;
}

/* 日期提示样式 */
.date-tips {
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: 16px;
}

.tip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.tip-icon {
  font-size: 20px;
  color: #0ea5e9;
}

.tip-title {
  font-size: 16px;
  font-weight: 600;
  color: #0c4a6e;
}

.tip-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #0c4a6e;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-bullet {
  color: #0ea5e9;
  font-weight: bold;
  margin-top: 2px;
}

.tip-text strong {
  color: #0c4a6e;
  font-weight: 600;
}

/* 日期信息样式 */
.date-info {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item.warning {
  color: #d97706;
}

.info-label {
  font-weight: 500;
  color: #374151;
}

.info-item.warning .info-label {
  color: #d97706;
}

.info-value {
  font-weight: 600;
  color: #1f2937;
}

.info-item.warning .info-value {
  color: #d97706;
}

/* 步骤导航样式 */
.step-navigation {
  display: flex;
  align-items: center;
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.nav-spacer {
  flex: 1;
}

.nav-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn-secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.nav-btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.nav-btn-primary {
  background: #3b82f6;
  color: white;
}

.nav-btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.nav-btn-success {
  background: #10b981;
  color: white;
}

.nav-btn-success:hover:not(:disabled) {
  background: #059669;
}

/* 结果步骤样式 */
.result-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.result-table-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
}

.result-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.result-table th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.result-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
}

.result-table tbody tr:hover {
  background: #f9fafb;
}

/* 可编辑单元格样式 */
.editable-cell {
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
}

.editable-cell:hover {
  background-color: #f0f9ff;
  color: #1d4ed8;
}

.editable-cell:hover::after {
  content: '✏️';
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
}

/* 操作按钮样式 */
.action-cell {
  text-align: center;
  vertical-align: middle;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.edit-btn {
  background: #f0f9ff;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

.edit-btn:hover:not(:disabled) {
  background: #dbeafe;
  border-color: #93c5fd;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.delete-btn:hover:not(:disabled) {
  background: #fee2e2;
  border-color: #fca5a5;
}

.save-btn {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.save-btn:hover:not(:disabled) {
  background: #dcfce7;
  border-color: #86efac;
}

.export-btn {
  background: #fefce8;
  color: #ca8a04;
  border: 1px solid #fde047;
}

.export-btn:hover:not(:disabled) {
  background: #fef9c3;
  border-color: #facc15;
}

.primary-btn {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.primary-btn:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.secondary-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.secondary-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.action-btn-warning {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fbbf24;
}

.action-btn-warning:hover:not(:disabled) {
  background: #fde68a;
  border-color: #f59e0b;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn-info {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.action-btn-info:hover:not(:disabled) {
  background: #bfdbfe;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 结果操作区域样式 */
.result-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 20px 0;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.modification-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f59e0b;
  font-size: 14px;
  font-weight: 500;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  background: #f59e0b;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 编辑弹窗样式 */
.edit-modal {
  width: 500px;
  max-width: 90vw;
}

.edit-info {
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.edit-info p {
  margin: 8px 0;
  color: #374151;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.result-table tbody tr:last-child td {
  border-bottom: none;
}

.restart-btn {
  background: #3b82f6;
  color: white;
  padding: 12px 32px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.restart-btn:hover {
  background: #2563eb;
}

/* 重新排班按钮动画样式 */
.btn-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.action-btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  background: inherit;
  transform: none;
}

/* 约束配置样式 */
.constraint-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 算法选择样式 */
.algorithm-selection {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.algorithm-selection h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.algorithm-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.algorithm-option {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.algorithm-option:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.algorithm-option.active {
  border-color: #3b82f6;
  background: #f0f7ff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.algorithm-icon {
  font-size: 24px;
  margin-right: 16px;
  min-width: 40px;
  text-align: center;
}

.algorithm-info {
  flex: 1;
}

.algorithm-info h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.algorithm-info p {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.algorithm-features {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 2px 8px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.algorithm-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.recommended-badge {
  padding: 4px 8px;
  background: #4caf50;
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.experimental-badge {
  padding: 4px 8px;
  background: #ff9800;
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.constraint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.constraint-item:hover {
  background: #f1f3f4;
  border-color: #dee2e6;
}

.constraint-info {
  flex: 1;
}

.constraint-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.constraint-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.constraint-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.constraint-toggle {
  width: 44px;
  height: 24px;
  background: #ccc;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.constraint-toggle.active {
  background: #4CAF50;
}

.toggle-handle {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.constraint-toggle.active .toggle-handle {
  transform: translateX(20px);
}

.weight-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 120px;
}

.weight-control label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.weight-slider {
  width: 100px;
  height: 4px;
  background: #ddd;
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.weight-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
}

.weight-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* 确认执行页面样式 */
.summary-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summary-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.algorithm-desc {
  font-size: 12px;
  color: #666;
  margin: 4px 0 0 0;
}

.summary-item p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

/* 日期统计样式 */
.date-statistics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-statistics p {
  margin: 2px 0;
  font-size: 14px;
}

.workday-detail {
  color: #4CAF50;
  font-weight: 500;
}

.adjusted-workday {
  color: #FF9800;
  font-size: 12px;
  font-weight: normal;
}

.holiday-warning {
  color: #F44336;
  font-weight: 500;
}

.weekend-info {
  color: #9E9E9E;
}

.constraint-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-group {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.summary-group h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.summary-group ul {
  margin: 0;
  padding-left: 16px;
  list-style: none;
}

.summary-group li {
  margin: 4px 0;
  font-size: 13px;
  color: #666;
  position: relative;
}

.summary-group li::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: #4CAF50;
  border-radius: 50%;
}

/* 排班进度样式 */
.scheduling-progress {
  margin-top: 20px;
  padding: 20px;
  background: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #b3d9ff;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #4CAF50;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  margin: 0;
  font-size: 13px;
  color: #666;
  text-align: center;
}

/* 错误提示样式 */
.error-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
  padding: 16px;
  background: #fff5f5;
  border-radius: 8px;
  border: 1px solid #fed7d7;
}

.error-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.error-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #e53e3e;
}

.error-content p {
  margin: 0;
  font-size: 14px;
  color: #c53030;
}

/* 🎯 统一结果弹窗样式 */
.unified-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

.unified-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 标题栏 */
.modal-header {
  display: flex;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.header-icon {
  flex-shrink: 0;
  margin-right: 16px;
}

.success-icon, .warning-icon, .error-icon {
  width: 32px;
  height: 32px;
}

.success-icon { color: #10b981; }
.warning-icon { color: #f59e0b; }
.error-icon { color: #ef4444; }

.header-content {
  flex: 1;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.modal-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.close-button {
  flex-shrink: 0;
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.close-icon {
  width: 20px;
  height: 20px;
}

/* 主体内容 */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #1f2937;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s;
}

.stat-item.success {
  border-color: #10b981;
  background: #ecfdf5;
}

.stat-item.info {
  border-color: #3b82f6;
  background: #eff6ff;
}

.stat-item.warning {
  border-color: #f59e0b;
  background: #fffbeb;
}

.stat-item.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

/* 违反详情区域 */
.violations-section {
  margin-bottom: 24px;
}

.violations-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.violations-count {
  font-weight: 600;
  color: #dc2626;
}

.severity-breakdown {
  font-size: 14px;
  color: #6b7280;
}

.violations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.violation-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
  margin-bottom: 12px;
}

.violation-item.error {
  border-color: #fecaca;
  background: #fef2f2;
}

.violation-item.warning {
  border-color: #fed7aa;
  background: #fffbeb;
}

.violation-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.violation-icon {
  flex-shrink: 0;
  margin-right: 12px;
}

.error-icon-small, .warning-icon-small {
  width: 20px;
  height: 20px;
}

.error-icon-small { color: #dc2626; }
.warning-icon-small { color: #d97706; }

.violation-title {
  flex: 1;
  font-weight: 600;
  color: #1f2937;
}

.violation-count {
  font-size: 14px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.violation-details {
  margin-left: 32px;
}

.violation-description {
  color: #4b5563;
  margin: 0;
  line-height: 1.5;
}

.more-violations {
  text-align: center;
  padding: 12px;
  color: #6b7280;
  font-style: italic;
  border: 1px dashed #d1d5db;
  border-radius: 8px;
  margin-top: 12px;
}

/* 成功区域 */
.success-section {
  margin-bottom: 24px;
}

.success-message {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #ecfdf5;
  border: 1px solid #a7f3d0;
  border-radius: 8px;
}

.success-icon-large {
  width: 48px;
  height: 48px;
  color: #10b981;
  flex-shrink: 0;
  margin-right: 16px;
}

.success-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #065f46;
}

.success-content p {
  margin: 0;
  color: #047857;
  line-height: 1.5;
}

.success-detail {
  margin-top: 12px !important;
  font-size: 14px !important;
  color: #059669 !important;
  background: rgba(16, 185, 129, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #10b981;
}

/* 底部 */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.footer-info {
  font-size: 14px;
  color: #6b7280;
}

.engine-info {
  font-weight: 500;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.action-button.primary {
  background: #3b82f6;
  color: white;
}

.action-button.primary:hover {
  background: #2563eb;
}

/* 响应式 */
@media (max-width: 768px) {
  .unified-modal {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .violations-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
