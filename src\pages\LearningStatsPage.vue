<template>
  <div class="learning-stats-page">
    <div class="page-header">
      <h1 class="page-title">
        <Brain class="title-icon" />
        系统学习统计
      </h1>
      <p class="page-subtitle">
        基于人工修改记录的智能分析
      </p>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>加载统计数据中...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <AlertCircle class="error-icon" />
      <p>{{ error }}</p>
      <button class="retry-btn" @click="loadData">
        <RefreshCw class="w-4 h-4" />
        重试
      </button>
    </div>
    
    <!-- 数据展示 -->
    <div v-else class="stats-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon bg-blue-100 text-blue-600">
            <Edit class="w-6 h-6" />
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.totalEdits }}</div>
            <div class="stat-label">总修改次数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-green-100 text-green-600">
            <CheckCircle class="w-6 h-6" />
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.acceptanceRate }}%</div>
            <div class="stat-label">推荐接受率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-orange-100 text-orange-600">
            <AlertTriangle class="w-6 h-6" />
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.forcedEdits }}</div>
            <div class="stat-label">强制修改次数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-purple-100 text-purple-600">
            <TrendingUp class="w-6 h-6" />
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.recommendedSelected }}</div>
            <div class="stat-label">接受推荐次数</div>
          </div>
        </div>
      </div>
      
      <!-- Top修改原因 -->
      <div class="section-card">
        <h2 class="section-title">
          <FileText class="w-5 h-5" />
          Top 5 修改原因
        </h2>
        <div v-if="stats.topReasons.length === 0" class="empty-state">
          暂无数据
        </div>
        <div v-else class="reasons-list">
          <div 
            v-for="(item, index) in stats.topReasons" 
            :key="item.category"
            class="reason-item"
          >
            <div class="reason-rank">{{ index + 1 }}</div>
            <div class="reason-content">
              <span class="reason-name">{{ item.category }}</span>
              <div class="reason-bar">
                <div 
                  class="reason-fill" 
                  :style="{ width: (item.count / stats.totalEdits * 100) + '%' }"
                ></div>
              </div>
            </div>
            <div class="reason-count">{{ item.count }}次</div>
          </div>
        </div>
      </div>
      
      <!-- Top选择考官 -->
      <div class="section-card">
        <h2 class="section-title">
          <Users class="w-5 h-5" />
          Top 10 常选考官
        </h2>
        <div v-if="stats.topTeachers.length === 0" class="empty-state">
          暂无数据
        </div>
        <div v-else class="teachers-list">
          <div 
            v-for="(teacher, index) in stats.topTeachers" 
            :key="teacher.name"
            class="teacher-item"
          >
            <div class="teacher-rank" :class="`rank-${index + 1}`">
              {{ index + 1 }}
            </div>
            <div class="teacher-name">{{ teacher.name }}</div>
            <div class="teacher-count">{{ teacher.count }}次</div>
          </div>
        </div>
      </div>
      
      <!-- 数据范围信息 -->
      <div class="date-range-info">
        <Calendar class="w-4 h-4" />
        <span>
          统计周期: {{ stats.dateRange?.start }} 至 {{ stats.dateRange?.end }}
          (最近{{ stats.dateRange?.days }}天)
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { learningApi, type LearningStatistics } from '../services/learningApi'
import {
  Brain, Edit, CheckCircle, AlertTriangle, TrendingUp,
  FileText, Users, Calendar, AlertCircle, RefreshCw
} from 'lucide-vue-next'

const loading = ref(true)
const error = ref('')
const stats = ref<LearningStatistics>({
  totalEdits: 0,
  acceptanceRate: 0,
  forcedEdits: 0,
  recommendedSelected: 0,
  topReasons: [],
  topTeachers: [],
  dateRange: {
    start: '',
    end: '',
    days: 30
  }
})

const loadData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const data = await learningApi.getStatistics(30)
    stats.value = data
  } catch (err: any) {
    error.value = '加载失败: ' + (err.message || '未知错误')
    console.error('加载统计数据失败:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.learning-stats-page {
  @apply p-6 max-w-7xl mx-auto;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-3xl font-bold text-gray-900 flex items-center gap-3 mb-2;
}

.title-icon {
  @apply w-8 h-8 text-blue-600;
}

.page-subtitle {
  @apply text-gray-600;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  @apply flex flex-col items-center justify-center py-20;
}

.spinner {
  @apply w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.error-icon {
  @apply w-12 h-12 text-red-500 mb-4;
}

.retry-btn {
  @apply mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-body {
  @apply flex-1;
}

.stat-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.stat-label {
  @apply text-sm text-gray-600;
}

/* 区域卡片 */
.section-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6;
}

.section-title {
  @apply text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4;
}

.empty-state {
  @apply text-center text-gray-500 py-8;
}

/* 修改原因列表 */
.reasons-list {
  @apply space-y-3;
}

.reason-item {
  @apply flex items-center gap-3;
}

.reason-rank {
  @apply w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center font-semibold text-sm;
}

.reason-content {
  @apply flex-1;
}

.reason-name {
  @apply text-gray-900 font-medium mb-1 block;
}

.reason-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.reason-fill {
  @apply h-full bg-blue-500 transition-all;
}

.reason-count {
  @apply text-sm text-gray-600 font-medium;
}

/* 考官列表 */
.teachers-list {
  @apply grid grid-cols-1 md:grid-cols-2 gap-3;
}

.teacher-item {
  @apply flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors;
}

.teacher-rank {
  @apply w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm;
}

.teacher-rank.rank-1 {
  @apply bg-yellow-100 text-yellow-700;
}

.teacher-rank.rank-2 {
  @apply bg-gray-100 text-gray-700;
}

.teacher-rank.rank-3 {
  @apply bg-orange-100 text-orange-700;
}

.teacher-rank:not(.rank-1):not(.rank-2):not(.rank-3) {
  @apply bg-blue-100 text-blue-600;
}

.teacher-name {
  @apply flex-1 font-medium text-gray-900;
}

.teacher-count {
  @apply text-sm text-gray-600;
}

/* 日期范围信息 */
.date-range-info {
  @apply flex items-center justify-center gap-2 text-sm text-gray-600 mt-6 py-3 px-4 bg-gray-50 rounded-lg;
}
</style>

