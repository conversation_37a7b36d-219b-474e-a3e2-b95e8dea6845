# 依赖
node_modules/
/dist/
dist-electron/

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.log

# 编辑器目录和文件
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 打包输出（太大，不上传到Git）
build-*
build/

# 临时文件
*.tmp
*.temp
temp/
.temp/

# Java/Maven
optaplanner-service/target/
*.class
*.jar
!optaplanner-service/target/quarkus-app/*.jar

# JDK/JRE（太大，不上传到Git）
*.zip
jdk/
jre/

# 测试文件
result.json
