# 数据库配置
quarkus.datasource.db-kind=h2
quarkus.datasource.username=sa
quarkus.datasource.password=
quarkus.datasource.jdbc.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE

# PostgreSQL配置（生产环境）
%prod.quarkus.datasource.db-kind=h2
%prod.quarkus.datasource.username=sa
%prod.quarkus.datasource.password=
%prod.quarkus.datasource.jdbc.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE

# Flyway配置
quarkus.flyway.migrate-at-start=true
quarkus.flyway.locations=classpath:db/migration

# Hibernate配置
quarkus.hibernate-orm.database.generation=drop-and-create
quarkus.hibernate-orm.log.sql=false
# 禁用Hibernate模式验证以避免H2数据库类型不匹配问题
quarkus.hibernate-orm.validate-in-dev-mode=false
# 禁用模式验证
quarkus.hibernate-orm.sql-load-script=no-file

# HTTP配置
quarkus.http.port=8081
quarkus.http.cors=true
quarkus.http.cors.origins=http://localhost:3000,http://localhost:5173,http://localhost:5174
quarkus.http.cors.headers=accept,authorization,content-type,x-requested-with
quarkus.http.cors.methods=GET,PUT,POST,DELETE,OPTIONS

# 禁用开发数据导入（临时）
# %dev.quarkus.hibernate-orm.sql-load-script=import-dev.sql

# 禁用不需要的功能
quarkus.banner.enabled=false

# 日志配置 - 按调试建议开启详细DEBUG + 性能监控
quarkus.log.level=INFO
quarkus.log.category."com.examiner.scheduler".level=DEBUG
quarkus.log.category."org.optaplanner".level=DEBUG
quarkus.log.category."org.optaplanner.core.impl.solver".level=DEBUG
quarkus.log.category."org.optaplanner.core.impl.constructionheuristic".level=DEBUG
quarkus.log.category."org.optaplanner.core.impl.localsearch".level=DEBUG
quarkus.log.category."org.optaplanner.core.impl.score".level=DEBUG
# 🔍 按调试建议：监控得分计算速度
quarkus.log.category."org.optaplanner.core.impl.score.director".level=DEBUG
quarkus.log.category."org.optaplanner.core.impl.solver.DefaultSolver".level=DEBUG
quarkus.log.console.enable=true
quarkus.log.console.format=%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n
# 🔧 修复：Quarkus 2.x+ 不支持 console.encoding 和 console.charset 配置
# 字符编码应该通过 JVM 参数设置：-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8

# OptaPlanner配置 - 1分钟快速求解（移除无效配置）
quarkus.optaplanner.solver.termination.spent-limit=60s
quarkus.optaplanner.solver.termination.unimproved-spent-limit=10s
quarkus.optaplanner.solver.termination.best-score-limit=0hard/0soft
# 注释：step-count-limit 在 application.properties 中不被识别，改用 solverConfig.xml

# Drools配置 - 强制使用ECJ编译器（用于JRE环境）
drools.dialect.java.compiler=ECLIPSE
drools.dialect.java.compiler.lnglevel=17
drools.compiler.lnglevel=1.8
drools.mvel.compiler.lnglevel=1.8
org.drools.compiler.kie.builder.impl.KieModuleKieProject.CheckKieModuleIntegrity=false
kie.maven.settings.custom=/dev/null