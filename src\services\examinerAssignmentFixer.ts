/**
 * 考官分配修复服务
 * 专门用于修复约束违反问题，确保每个学员都有两名主考官
 */

export interface ExaminerAssignmentFixResult {
  success: boolean
  fixedCount: number
  remainingIssues: number
  details: string[]
  assignments: any[]
}

export class ExaminerAssignmentFixer {
  
  /**
   * 修复考官分配问题
   */
  public fixExaminerAssignments(
    assignments: any[], 
    teachers: any[]
  ): ExaminerAssignmentFixResult {
    const fixDetails: string[] = []
    let fixedCount = 0
    const fixedAssignments = [...assignments]
    
    console.log('🔧 开始修复考官分配问题...')
    
    for (let i = 0; i < fixedAssignments.length; i++) {
      const assignment = fixedAssignments[i]
      const studentName = assignment.studentName || assignment.student?.name || '未知学员'
      
      // 检查是否需要修复
      const needsExaminer1 = !assignment.examiner1 || assignment.examiner1 === '未分配'
      const needsExaminer2 = !assignment.examiner2 || assignment.examiner2 === '未分配'
      const hasDuplicateExaminers = assignment.examiner1 && assignment.examiner2 && 
        assignment.examiner1.id === assignment.examiner2.id
      
      if (!needsExaminer1 && !needsExaminer2 && !hasDuplicateExaminers) {
        continue // 该分配无需修复
      }
      
      console.log(`🔍 修复学员 ${studentName} 的考官分配...`)
      
      // 获取学员科室
      const studentDept = assignment.student?.department || assignment.studentDepartment
      
      // 分类考官
      const availableTeachers = teachers.filter(t => t.isActive !== false)
      const sameDeptTeachers = availableTeachers.filter(t => t.department === studentDept)
      const diffDeptTeachers = availableTeachers.filter(t => t.department !== studentDept)
      
      // 修复考官1（优先同科室）
      if (needsExaminer1 || hasDuplicateExaminers) {
        const examiner1 = this.selectBestExaminer1(sameDeptTeachers, diffDeptTeachers, assignment)
        if (examiner1) {
          assignment.examiner1 = examiner1
          fixDetails.push(`✅ 为${studentName}分配考官1: ${examiner1.name} (${examiner1.department})`)
          fixedCount++
        } else {
          fixDetails.push(`❌ 无法为${studentName}分配考官1`)
        }
      }
      
      // 修复考官2（优先不同科室）
      if (needsExaminer2 || hasDuplicateExaminers) {
        const examiner2 = this.selectBestExaminer2(
          diffDeptTeachers, 
          sameDeptTeachers, 
          assignment, 
          assignment.examiner1
        )
        if (examiner2) {
          assignment.examiner2 = examiner2
          fixDetails.push(`✅ 为${studentName}分配考官2: ${examiner2.name} (${examiner2.department})`)
          fixedCount++
        } else {
          fixDetails.push(`❌ 无法为${studentName}分配考官2`)
        }
      }
    }
    
    // 统计剩余问题
    const remainingIssues = fixedAssignments.filter(assignment => {
      const hasExaminer1 = assignment.examiner1 && assignment.examiner1 !== '未分配'
      const hasExaminer2 = assignment.examiner2 && assignment.examiner2 !== '未分配'
      const noDuplicates = !assignment.examiner1 || !assignment.examiner2 || 
        assignment.examiner1.id !== assignment.examiner2.id
      return !hasExaminer1 || !hasExaminer2 || !noDuplicates
    }).length
    
    console.log(`🎯 考官分配修复完成: 修复${fixedCount}个问题，剩余${remainingIssues}个问题`)
    
    return {
      success: remainingIssues === 0,
      fixedCount,
      remainingIssues,
      details: fixDetails,
      assignments: fixedAssignments
    }
  }
  
  /**
   * 选择最佳考官1（优先同科室）
   */
  private selectBestExaminer1(
    sameDeptTeachers: any[], 
    diffDeptTeachers: any[], 
    assignment: any
  ): any | null {
    // 优先选择同科室考官
    if (sameDeptTeachers.length > 0) {
      return this.selectLeastBusyTeacher(sameDeptTeachers)
    }
    
    // 如果没有同科室考官，选择其他科室考官
    if (diffDeptTeachers.length > 0) {
      return this.selectLeastBusyTeacher(diffDeptTeachers)
    }
    
    return null
  }
  
  /**
   * 选择最佳考官2（优先不同科室，避免与考官1重复）
   */
  private selectBestExaminer2(
    diffDeptTeachers: any[], 
    sameDeptTeachers: any[], 
    assignment: any,
    examiner1: any
  ): any | null {
    // 过滤掉考官1
    const examiner1Id = examiner1?.id
    const availableDiffDept = diffDeptTeachers.filter(t => t.id !== examiner1Id)
    const availableSameDept = sameDeptTeachers.filter(t => t.id !== examiner1Id)
    
    // 优先选择不同科室考官
    if (availableDiffDept.length > 0) {
      return this.selectLeastBusyTeacher(availableDiffDept)
    }
    
    // 如果没有不同科室考官，选择同科室考官（但不是考官1）
    if (availableSameDept.length > 0) {
      return this.selectLeastBusyTeacher(availableSameDept)
    }
    
    return null
  }
  
  /**
   * 选择工作负荷最轻的考官
   */
  private selectLeastBusyTeacher(teachers: any[]): any | null {
    if (teachers.length === 0) return null
    
    // 简单随机选择（在实际应用中可以根据工作负荷选择）
    return teachers[Math.floor(Math.random() * teachers.length)]
  }
}

// 导出单例
export const examinerAssignmentFixer = new ExaminerAssignmentFixer()