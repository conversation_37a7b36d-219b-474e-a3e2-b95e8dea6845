package com.examiner.scheduler.config;

import com.examiner.scheduler.domain.ExamSchedule;
import com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider;
import org.optaplanner.core.api.score.buildin.hardsoftlong.HardSoftLongScore;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.config.solver.termination.TerminationConfig;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.enterprise.context.ApplicationScoped;

/**
 * 自适应分级求解器配置
 * 实现闪电模式 → 标准模式 → 精细模式的自动升级策略
 */
@ApplicationScoped
public class AdaptiveSolverConfig {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AdaptiveSolverConfig.class);
    
    /**
     * Level 1: 闪电模式配置（3-5秒）
     * 目标：快速获得可用解
     * 适用：小规模问题（< 10个学员）
     */
    public SolverConfig createFlashConfig() {
        LOGGER.info("🚀 [闪电模式] 配置：最多5秒，2秒无改进停止");
        
        return new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withPhaseList(java.util.Arrays.asList(
                    createFastConstructionHeuristicConfig(),
                    createFastLocalSearchConfig()  // 🔧 添加LocalSearch阶段
                ))
                .withTerminationConfig(new TerminationConfig()
                        .withSecondsSpentLimit(5L)              // 最多5秒
                        .withUnimprovedSecondsSpentLimit(2L));  // 2秒无改进停止
                        // 🔧 移除 bestScoreLimit，允许充分优化
    }
    
    /**
     * Level 2: 标准模式配置（10-60秒）
     * 目标：获得良好解
     * 适用：中等规模（10-30个学员）
     * 🔧 优化：从40秒增加到60秒，提供更多优化时间
     */
    public SolverConfig createStandardConfig() {
        LOGGER.info("⚡ [标准模式] 配置：最多60秒，10秒无改进停止");
        
        return new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withPhaseList(java.util.Arrays.asList(
                    createStandardConstructionHeuristicConfig(),
                    createStandardLocalSearchConfig()  // 🔧 添加LocalSearch阶段
                ))
                .withTerminationConfig(new TerminationConfig()
                        .withSecondsSpentLimit(60L)             // 🔧 从40秒增加到60秒
                        .withUnimprovedSecondsSpentLimit(10L)); // 🔧 从8秒增加到10秒
                        // 🔧 移除 bestScoreLimit，允许充分优化软约束
    }
    
    /**
     * Level 3: 精细模式配置（30-120秒）
     * 目标：获得最优解
     * 适用：大规模（> 30个学员）或复杂约束
     * 🔧 优化：从90秒增加到120秒，提供更多优化时间
     */
    public SolverConfig createPreciseConfig() {
        LOGGER.info("🏆 [精细模式] 配置：最多120秒，20秒无改进停止");
        
        return new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withPhaseList(java.util.Arrays.asList(
                    createPreciseConstructionHeuristicConfig(),
                    createPreciseLocalSearchConfig()  // 🔧 添加LocalSearch阶段
                ))
                .withTerminationConfig(new TerminationConfig()
                        .withSecondsSpentLimit(120L)            // 🔧 从90秒增加到120秒
                        .withUnimprovedSecondsSpentLimit(20L)); // 🔧 从15秒增加到20秒
                        // 🔧 移除 bestScoreLimit，让求解器充分优化软约束
    }
    
    /**
     * 根据问题规模自动选择配置
     */
    public SolverConfig createAdaptiveConfig(int studentCount) {
        if (studentCount < 10) {
            LOGGER.info("📊 学员数量: {}, 选择闪电模式", studentCount);
            return createFlashConfig();
        } else if (studentCount < 30) {
            LOGGER.info("📊 学员数量: {}, 选择标准模式", studentCount);
            return createStandardConfig();
        } else {
            LOGGER.info("📊 学员数量: {}, 选择精细模式", studentCount);
            return createPreciseConfig();
        }
    }
    
    /**
     * 评估解的质量等级
     * @return 1=优秀, 2=良好, 3=可接受, 4=需改进
     */
    public int evaluateSolutionQuality(HardSoftLongScore score) {
        if (!score.isFeasible()) {
            return 4; // 硬约束未满足
        }
        
        // 使用 level numbers 获取 soft score，避免调用已废弃的 getSoftScore()
        long softScore;
        long[] levelNumbers = score.toLevelNumbers();
        if (levelNumbers != null && levelNumbers.length >= 2) {
            softScore = levelNumbers[1];
        } else {
            softScore = 0L;
        }
        
        // 🔧 修复：当前约束提供者使用了reward()而不是penalize()
        // 所以软约束得分是正数，需要反转判断逻辑
        // 正常情况下应该是负数，但现在是正数，所以需要特殊处理
        
        // 如果是正数（使用了reward），分数越高说明越需要优化（因为还有很多约束可以满足）
        if (softScore > 0) {
            // 正分系统（reward）- 分数越高越需要继续优化
            if (softScore >= 50000) {
                return 4; // 需改进（还有很多约束可以满足）
            } else if (softScore >= 20000) {
                return 3; // 可接受
            } else if (softScore >= 5000) {
                return 2; // 良好
            } else {
                return 1; // 优秀
            }
        } else {
            // 负分系统（penalize）- 正常逻辑
            if (softScore >= -20) {
                return 1; // 优秀（软约束几乎完美）
            } else if (softScore >= -100) {
                return 2; // 良好
            } else if (softScore >= -300) {
                return 3; // 可接受
            } else {
                return 4; // 需改进
            }
        }
    }
    
    /**
     * 判断是否需要升级到更高级别
     */
    public boolean shouldUpgrade(HardSoftLongScore currentScore, String currentLevel) {
        int quality = evaluateSolutionQuality(currentScore);
        
        switch (currentLevel) {
            case "flash":
                // 闪电模式结果质量 < 良好，需要升级
                return quality > 2;
            case "standard":
                // 标准模式结果质量 < 优秀，且为复杂问题，需要升级
                return quality > 1;
            default:
                return false;
        }
    }
    
    /**
     * 创建快速构造启发式配置（闪电模式）
     */
    private ConstructionHeuristicPhaseConfig createFastConstructionHeuristicConfig() {
        ConstructionHeuristicPhaseConfig config = new ConstructionHeuristicPhaseConfig();
        
        // 🔧 修复：使用FIRST_FIT，不配置sorter以避免需要难度比较器
        config.setConstructionHeuristicType(ConstructionHeuristicType.FIRST_FIT);
        
        // 使用默认配置，不显式配置选择器
        return config;
    }
    
    /**
     * 创建标准构造启发式配置（标准模式）
     */
    private ConstructionHeuristicPhaseConfig createStandardConstructionHeuristicConfig() {
        ConstructionHeuristicPhaseConfig config = new ConstructionHeuristicPhaseConfig();
        
        // 🔧 修复：使用FIRST_FIT，不配置sorter以避免需要难度比较器
        config.setConstructionHeuristicType(ConstructionHeuristicType.FIRST_FIT);
        
        // 使用默认配置，不显式配置选择器
        return config;
    }
    
    /**
     * 创建精确构造启发式配置（精确模式）
     */
    private ConstructionHeuristicPhaseConfig createPreciseConstructionHeuristicConfig() {
        ConstructionHeuristicPhaseConfig config = new ConstructionHeuristicPhaseConfig();
        
        // 🔧 修复：使用FIRST_FIT，不配置sorter以避免需要难度比较器
        config.setConstructionHeuristicType(ConstructionHeuristicType.FIRST_FIT);
        
        // 使用默认配置，不显式配置选择器
        return config;
    }
    
    /**
     * 创建快速LocalSearch配置（闪电模式）
     * 🔧 新增：添加LocalSearch阶段以真正优化软约束
     */
    private org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig createFastLocalSearchConfig() {
        org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig config = 
            new org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig();
        
        // 使用默认的移动选择器和接受器 - OptaPlanner会自动配置
        // 快速模式：简单高效
        
        return config;
    }
    
    /**
     * 创建标准LocalSearch配置（标准模式）
     * 🔧 新增：添加LocalSearch阶段以真正优化软约束
     */
    private org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig createStandardLocalSearchConfig() {
        org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig config = 
            new org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig();
        
        // 使用默认的移动选择器和接受器 - OptaPlanner会自动配置
        // 标准模式：平衡效率和质量
        
        return config;
    }
    
    /**
     * 创建精确LocalSearch配置（精细模式）
     * 🔧 新增：添加LocalSearch阶段以真正优化软约束
     */
    private org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig createPreciseLocalSearchConfig() {
        org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig config = 
            new org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig();
        
        // 使用默认的移动选择器和接受器 - OptaPlanner会自动配置
        // 精细模式：追求最优解
        
        return config;
    }
}