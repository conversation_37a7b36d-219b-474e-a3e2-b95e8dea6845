﻿<template>
  <div class="reports-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">报表统计</h1>
      </div>
      <div class="header-actions">
        <button class="btn-secondary" @click="exportReport">
          <Download class="w-4 h-4" />
          <span>导出报表</span>
        </button>
        <button class="btn-primary" @click="generateReport">
          <BarChart3 class="w-4 h-4" />
          <span>生成报表</span>
        </button>
      </div>
    </div>

    <!-- 时间筛选 -->
    <div class="filter-section">
      <div class="time-range-selector">
        <div class="quick-ranges">
          <button 
            v-for="range in quickRanges" 
            :key="range.key"
            class="range-btn"
            :class="{ active: selectedRange === range.key }"
            @click="selectQuickRange(range)"
          >
            {{ range.label }}
          </button>
        </div>
        <div class="custom-range">
          <DatePicker 
            v-model="dateRange.start" 
            placeholder="开始日期"
            :max-date="dateRange.end || new Date()"
          />
          <span class="range-separator">至</span>
          <DatePicker 
            v-model="dateRange.end" 
            placeholder="结束日期"
            :min-date="dateRange.start"
            :max-date="new Date()"
          />
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon bg-blue-100 text-blue-600">
            <Calendar class="w-6 h-6" />
          </div>
          <div class="stat-trend positive">
            <TrendingUp class="w-4 h-4" />
            <span>+12%</span>
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ reportStats.totalExams }}</div>
          <div class="stat-label">总考试场次</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon bg-green-100 text-green-600">
            <Users class="w-6 h-6" />
          </div>
          <div class="stat-trend positive">
            <TrendingUp class="w-4 h-4" />
            <span>+8%</span>
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ reportStats.totalTeachers }}</div>
          <div class="stat-label">参与考官数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon bg-orange-100 text-orange-600">
            <Clock class="w-6 h-6" />
          </div>
          <div class="stat-trend negative">
            <TrendingDown class="w-4 h-4" />
            <span>-5%</span>
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ reportStats.avgHours }}</div>
          <div class="stat-label">平均工作时长</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon bg-purple-100 text-purple-600">
            <Target class="w-6 h-6" />
          </div>
          <div class="stat-trend positive">
            <TrendingUp class="w-4 h-4" />
            <span>+3%</span>
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ reportStats.efficiency }}%</div>
          <div class="stat-label">排班效率</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 考试趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">考试场次趋势</h3>
            <div class="chart-controls">
              <select v-model="examTrendPeriod" class="chart-select">
                <option value="week">按周</option>
                <option value="month">按月</option>
                <option value="quarter">按季度</option>
              </select>
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <BarChart3 class="w-16 h-16 text-gray-300" />
              <p class="text-gray-500">考试场次趋势图表</p>
            </div>
          </div>
        </div>
        
        <!-- 考官工作量分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">考官工作量分布</h3>
            <div class="chart-controls">
              <button class="chart-btn" :class="{ active: workloadView === 'pie' }" @click="workloadView = 'pie'">
                <PieChartIcon class="w-4 h-4" />
              </button>
              <button class="chart-btn" :class="{ active: workloadView === 'bar' }" @click="workloadView = 'bar'">
                <BarChart3 class="w-4 h-4" />
              </button>
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <PieChartIcon class="w-16 h-16 text-gray-300" />
              <p class="text-gray-500">考官工作量分布图</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="chart-row">
        <!-- 科目分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">考试科目分布</h3>
          </div>
          <div class="chart-content">
            <div class="subject-stats">
              <div v-for="subject in subjectStats" :key="subject.name" class="subject-item">
                <div class="subject-info">
                  <span class="subject-name">{{ subject.name }}</span>
                  <span class="subject-count">{{ subject.count }}场</span>
                </div>
                <div class="subject-bar">
                  <div 
                    class="subject-progress" 
                    :style="{ width: `${(subject.count / maxSubjectCount) * 100}%` }"
                  ></div>
                </div>
                <span class="subject-percentage">{{ subject.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 时间段分析 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">考试时间段分析</h3>
          </div>
          <div class="chart-content">
            <div class="time-slot-stats">
              <div v-for="slot in timeSlotStats" :key="slot.time" class="time-slot-item">
                <div class="time-info">
                  <span class="time-label">{{ slot.time }}</span>
                  <span class="exam-count">{{ slot.count }}场考试</span>
                </div>
                <div class="utilization-bar">
                  <div 
                    class="utilization-fill"
                    :class="getUtilizationClass(slot.utilization)"
                    :style="{ width: `${slot.utilization}%` }"
                  ></div>
                </div>
                <span class="utilization-text">{{ slot.utilization }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细报表 -->
    <div class="detailed-reports">
      <div class="report-tabs">
        <button 
          v-for="tab in reportTabs" 
          :key="tab.key"
          class="tab-btn"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          <component :is="tab.icon" class="w-4 h-4" />
          <span>{{ tab.label }}</span>
        </button>
      </div>
      
      <div class="report-content">
        <!-- 考官报表 -->
        <div v-if="activeTab === 'teachers'" class="report-table">
          <table class="table">
            <thead>
              <tr>
                <th>姓名</th>
                <th>所属部门</th>
                <th>监考场次</th>
                <th>工作时长</th>
                <th>平均评分</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="teacher in teacherReports" :key="teacher.id">
                <td>
                  <div class="teacher-info">
                    <span class="teacher-name">{{ teacher.name }}</span>
                  </div>
                </td>
                <td>{{ teacher.department }}</td>
                <td>{{ teacher.examCount }}</td>
                <td>{{ teacher.workHours }}小时</td>
                <td>
                  <div class="rating">
                    <Star class="w-4 h-4 text-yellow-400 fill-current" />
                    <span>{{ teacher.rating }}</span>
                  </div>
                </td>
                <td>
                  <span class="status-badge" :class="teacher.status">
                    {{ getTeacherStatusText(teacher.status) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 考试报表 -->
        <div v-if="activeTab === 'exams'" class="report-table">
          <table class="table">
            <thead>
              <tr>
                <th>考试科目</th>
                <th>考试日期</th>
                <th>考场</th>
                <th>考生人数</th>
                <th>监考人员</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="exam in examReports" :key="exam.id">
                <td>{{ exam.subject }}</td>
                <td>{{ formatDate(exam.date) }}</td>
                <td>{{ exam.room }}</td>
                <td>{{ exam.studentCount }}</td>
                <td>
                  <div class="teachers-list">
                    <span 
                      v-for="teacher in exam.teachers" 
                      :key="teacher.id"
                      class="teacher-tag"
                    >
                      {{ teacher.name }}
                    </span>
                  </div>
                </td>
                <td>
                  <span class="status-badge" :class="exam.status">
                    {{ getExamStatusText(exam.status) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 部门报表 -->
        <div v-if="activeTab === 'departments'" class="report-table">
          <table class="table">
            <thead>
              <tr>
                <th>部门名称</th>
                <th>考官数量</th>
                <th>参与场次</th>
                <th>总工作时长</th>
                <th>平均工作量</th>
                <th>效率评级</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="dept in departmentReports" :key="dept.id">
                <td>{{ dept.name }}</td>
                <td>{{ dept.teacherCount }}</td>
                <td>{{ dept.examCount }}</td>
                <td>{{ dept.totalHours }}小时</td>
                <td>{{ dept.avgWorkload }}小时/人</td>
                <td>
                  <div class="efficiency-rating" :class="dept.efficiency">
                    {{ getEfficiencyText(dept.efficiency) }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { BarChart, LineChart, PieChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { apiService } from '@/services/api-service'
import type { PerformanceMetrics } from '@/services/api-service'
import { 
    Download, BarChart3, Calendar, Users, Clock, Target, 
    TrendingUp, TrendingDown, PieChart as PieChartIcon, Star 
  } from 'lucide-vue-next'
import DatePicker from '../components/DatePicker.vue'

// 注册 ECharts 组件
use([
  BarChart,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  CanvasRenderer
])

interface QuickRange {
  key: string
  label: string
  days: number
}

interface SubjectStat {
  name: string
  count: number
  percentage: number
}

interface TimeSlotStat {
  time: string
  count: number
  utilization: number
}

const selectedRange = ref('month')
const dateRange = ref({ start: '', end: '' })
const examTrendPeriod = ref('month')
const workloadView = ref('pie')
const activeTab = ref('teachers')
const loading = ref(false)
const error = ref<string | null>(null)
const performanceData = ref<PerformanceMetrics | null>(null)

const quickRanges: QuickRange[] = [
  { key: 'week', label: '近一周', days: 7 },
  { key: 'month', label: '近一月', days: 30 },
  { key: 'quarter', label: '近三月', days: 90 },
  { key: 'year', label: '近一年', days: 365 }
]

const reportTabs = [
  { key: 'teachers', label: '考官报表', icon: Users },
  { key: 'exams', label: '考试报表', icon: Calendar },
  { key: 'departments', label: '部门报表', icon: BarChart3 }
]

const reportStats = ref({
  totalExams: 156,
  totalTeachers: 48,
  avgHours: 12.5,
  efficiency: 87
})

const subjectStats = ref<SubjectStat[]>([
  { name: '高等数学', count: 45, percentage: 28.8 },
  { name: '大学英语', count: 38, percentage: 24.4 },
  { name: '计算机基础', count: 32, percentage: 20.5 },
  { name: '大学物理', count: 25, percentage: 16.0 },
  { name: '其他科目', count: 16, percentage: 10.3 }
])

const timeSlotStats = ref<TimeSlotStat[]>([
  { time: '08:00-10:00', count: 42, utilization: 85 },
  { time: '10:30-12:30', count: 38, utilization: 76 },
  { time: '14:00-16:00', count: 45, utilization: 90 },
  { time: '16:30-18:30', count: 31, utilization: 62 }
])

const teacherReports = ref([
  {
    id: '1',
    name: '张教授',
    department: '数学系',
    examCount: 12,
    workHours: 24,
    rating: 4.8,
    status: 'active'
  },
  {
    id: '2',
    name: '李老师',
    department: '外语系',
    examCount: 8,
    workHours: 16,
    rating: 4.6,
    status: 'active'
  }
])

const examReports = ref([
  {
    id: '1',
    subject: '高等数学',
    date: '2024-01-15',
    room: 'A101',
    studentCount: 120,
    teachers: [{ id: '1', name: '张教授' }],
    status: 'completed'
  }
])

const departmentReports = ref([
  {
    id: '1',
    name: '数学系',
    teacherCount: 15,
    examCount: 45,
    totalHours: 180,
    avgWorkload: 12,
    efficiency: 'high'
  }
])

const maxSubjectCount = computed(() => {
  return Math.max(...subjectStats.value.map(s => s.count))
})

const selectQuickRange = (range: QuickRange) => {
  selectedRange.value = range.key
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - range.days)
  
  dateRange.value.start = startDate.toISOString().split('T')[0]
  dateRange.value.end = endDate.toISOString().split('T')[0]
}

const getUtilizationClass = (utilization: number) => {
  if (utilization >= 80) return 'high'
  if (utilization >= 60) return 'medium'
  return 'low'
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const getTeacherStatusText = (status: string) => {
  const statusMap = {
    active: '活跃',
    inactive: '不活跃',
    busy: '繁忙'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getExamStatusText = (status: string) => {
  const statusMap = {
    completed: '已完成',
    'in-progress': '进行中',
    scheduled: '已安排'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getEfficiencyText = (efficiency: string) => {
  const efficiencyMap = {
    high: '优秀',
    medium: '良好',
    low: '一般'
  }
  return efficiencyMap[efficiency as keyof typeof efficiencyMap] || efficiency
}

// API方法
const fetchPerformanceData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await apiService.getPerformanceMetrics()
    if (response.success && response.data) {
      performanceData.value = response.data
    } else {
      error.value = response.error?.message || '获取性能数据失败'
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
    console.error('获取性能数据失败:', err)
  } finally {
    loading.value = false
  }
}

const fetchReportData = async () => {
  loading.value = true
  error.value = null
  
  try {
    // 获取排班历史数据用于统计
    const startDate = new Date(dateRange.value.start)
    const endDate = new Date(dateRange.value.end)
    const response = await apiService.getScheduleHistory({
      page: 1,
      pageSize: 100,
      startDate: dateRange.value.start,
      endDate: dateRange.value.end
    })
    
    if (response.success && response.data) {
      // 处理统计数据
      console.log('报告数据:', response.data)
    } else {
      error.value = response.error?.message || '获取报告数据失败'
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
    console.error('获取报告数据失败:', err)
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  console.log('导出报表')
}

const generateReport = () => {
  console.log('生成报表')
}

const refreshData = () => {
  fetchPerformanceData()
  fetchReportData()
}

onMounted(() => {
  selectQuickRange(quickRanges[1]) // 默认选择近一月
  fetchPerformanceData()
  fetchReportData()
})
</script>

<style scoped>
.reports-page {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.page-header {
  @apply flex items-center justify-between bg-white rounded-lg shadow-sm p-6;
}

.header-content {
  @apply flex-1;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-1;
}

.page-description {
  @apply text-gray-600;
}

.header-actions {
  @apply flex space-x-3;
}

.btn-primary {
  @apply flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors;
}

.btn-secondary {
  @apply flex items-center space-x-2 bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors;
}

.filter-section {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.time-range-selector {
  @apply flex items-center justify-between flex-wrap gap-4;
}

.quick-ranges {
  @apply flex space-x-2;
}

.range-btn {
  @apply px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors;
}

.range-btn.active {
  @apply bg-blue-600 text-white;
}

.custom-range {
  @apply flex items-center space-x-3;
}

.range-separator {
  @apply text-gray-500;
}

.stats-overview {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.stat-header {
  @apply flex items-center justify-between mb-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-trend {
  @apply flex items-center space-x-1 text-sm font-medium;
}

.stat-trend.positive {
  @apply text-green-600;
}

.stat-trend.negative {
  @apply text-red-600;
}

.stat-content {
  @apply space-y-1;
}

.stat-value {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600;
}

.charts-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-row {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.chart-card {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.chart-header {
  @apply flex items-center justify-between mb-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900;
}

.chart-controls {
  @apply flex items-center space-x-2;
}

.chart-select {
  @apply px-3 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.chart-btn {
  @apply p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors;
}

.chart-btn.active {
  @apply bg-blue-100 text-blue-600;
}

.chart-content {
  @apply h-64;
}

.chart-placeholder {
  @apply h-full flex flex-col items-center justify-center space-y-2;
}

.subject-stats {
  @apply space-y-4;
}

.subject-item {
  @apply flex items-center space-x-4;
}

.subject-info {
  @apply flex-1 flex items-center justify-between;
}

.subject-name {
  @apply font-medium text-gray-900;
}

.subject-count {
  @apply text-sm text-gray-600;
}

.subject-bar {
  @apply flex-1 mx-4 h-2 bg-gray-200 rounded-full overflow-hidden;
}

.subject-progress {
  @apply h-full bg-blue-600 transition-all duration-300;
}

.subject-percentage {
  @apply text-sm font-medium text-gray-900 w-12 text-right;
}

.time-slot-stats {
  @apply space-y-4;
}

.time-slot-item {
  @apply flex items-center space-x-4;
}

.time-info {
  @apply w-32;
}

.time-label {
  @apply block font-medium text-gray-900;
}

.exam-count {
  @apply block text-sm text-gray-600;
}

.utilization-bar {
  @apply flex-1 h-3 bg-gray-200 rounded-full overflow-hidden;
}

.utilization-fill {
  @apply h-full transition-all duration-300;
}

.utilization-fill.high {
  @apply bg-green-500;
}

.utilization-fill.medium {
  @apply bg-yellow-500;
}

.utilization-fill.low {
  @apply bg-red-500;
}

.utilization-text {
  @apply text-sm font-medium text-gray-900 w-12 text-right;
}

.detailed-reports {
  @apply bg-white rounded-lg shadow-sm;
}

.report-tabs {
  @apply flex border-b border-gray-200;
}

.tab-btn {
  @apply flex items-center space-x-2 px-6 py-4 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 border-b-2 border-transparent transition-colors;
}

.tab-btn.active {
  @apply text-blue-600 border-blue-600 bg-blue-50;
}

.report-content {
  @apply p-6;
}

.report-table {
  @apply overflow-x-auto;
}

.table {
  @apply w-full;
}

.table th {
  @apply px-4 py-3 text-left text-sm font-medium text-gray-700 border-b border-gray-200;
}

.table td {
  @apply px-4 py-4 text-sm border-b border-gray-100;
}

.teacher-info {
  @apply flex items-center space-x-3;
}

.teacher-name {
  @apply font-medium text-gray-900;
}

.rating {
  @apply flex items-center space-x-1;
}

.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.status-badge.active {
  @apply bg-green-100 text-green-800;
}

.status-badge.inactive {
  @apply bg-gray-100 text-gray-800;
}

.status-badge.busy {
  @apply bg-orange-100 text-orange-800;
}

.status-badge.completed {
  @apply bg-blue-100 text-blue-800;
}

.status-badge.in-progress {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge.scheduled {
  @apply bg-purple-100 text-purple-800;
}

.teachers-list {
  @apply flex flex-wrap gap-1;
}

.teacher-tag {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs;
}

.efficiency-rating {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.efficiency-rating.high {
  @apply bg-green-100 text-green-800;
}

.efficiency-rating.medium {
  @apply bg-yellow-100 text-yellow-800;
}

.efficiency-rating.low {
  @apply bg-red-100 text-red-800;
}
</style>