<template>
  <div class="app-container">
    <!-- 侧边栏-->
    <aside class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo-container">
          <div class="logo-icon">
            <img src="/icon.png" alt="系统图标" class="logo-img" />
          </div>
          <div class="logo-text" v-show="!sidebarCollapsed">
            <h1 class="system-title">考试自动排班助手</h1>
            <p class="system-subtitle">Examiner Assignment Assistant</p>
          </div>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-items">
          <router-link to="/" class="nav-item">
            <Home class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">首页</span>
          </router-link>
          <router-link to="/teachers" class="nav-item nav-item-active">
            <Users class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">考官管理</span>
          </router-link>
          <router-link to="/schedules" class="nav-item">
            <Calendar class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">排班管理</span>
          </router-link>
          <!-- 隐藏数据统计页面 -->
          <!-- <router-link to="/statistics" class="nav-item">
            <BarChart class="nav-icon" />
            <span v-show="!sidebarCollapsed" class="nav-text">数据统计</span>
          </router-link> -->
        </div>
      </nav>
      
      <!-- 侧边栏收缩按钮-->
      <div class="sidebar-toggle" @click="toggleSidebar">
        <ChevronLeft class="toggle-icon" :class="{ 'rotated': sidebarCollapsed }" />
      </div>
    </aside>

    <!-- 主内容区域-->
    <div class="main-content">
      <!-- 页面标题区-->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">考官管理</h1>
          <div class="storage-info">
            <div class="storage-stats" v-if="storageStats.cacheHits !== undefined">
              <span class="stats-item">缓存命中: {{ storageStats.cacheHits }}</span>
              <span class="stats-item">数据大小: {{ storageStats.dataSize }}</span>
              <span class="stats-item">
                命中率: {{ storageStats.cacheHitRate }}
              </span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button 
            class="action-btn action-btn-secondary" 
            :class="{ 'disabled': !hasSelectedTeachers }"
            @click="deleteSelectedTeachers"
          >
            <Trash2 class="btn-icon" />
            <span>删除</span>
          </button>
          <button class="action-btn action-btn-secondary" @click="importTeachers">
            <Download class="btn-icon" />
            <span>导入</span>
          </button>
          <button class="action-btn action-btn-secondary" @click="exportTeachers">
            <Upload class="btn-icon" />
            <span>导出</span>
          </button>
          <button class="action-btn action-btn-primary" @click="showAddTeacherModal">
            <Plus class="btn-icon" />
            <span>新增考官</span>
          </button>
        </div>
      </div>

      <!-- 隐藏的文件输入-->
      <input 
        ref="fileInput" 
        type="file" 
        accept=".xlsx,.xls,.csv" 
        style="display: none" 
        @change="handleFileUpload"
      />

      <!-- 考官表格 -->
      <div class="table-container">
        <table class="teachers-table">
          <thead>
            <tr>
              <th class="checkbox-column">
                <input 
                  type="checkbox" 
                  @change="toggleSelectAll"
                  :checked="teachers.length > 0 && teachers.every(t => t.selected)"
                />
              </th>
              <th>姓名</th>
              <th>所在科室</th>
              <th>所在班组</th>
              <th>当日班次</th>
              <!-- <th>状态</th> -->
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="teacher in teachers" :key="teacher.id" :class="{ 'selected': teacher.selected }">
              <td class="checkbox-column">
                <input 
                  type="checkbox" 
                  v-model="teacher.selected"
                  @change="toggleTeacherSelect(teacher)"
                />
              </td>
              <td>{{ teacher.name }}</td>
              <td>{{ teacher.department }}</td>
              <td>{{ teacher.group }}</td>
              <td>{{ teacher.shift }}</td>
              <!-- <td>
                <div class="status-dropdown">
                  <span 
                    class="status-text" 
                    :class="{
                      'status-available': teacher.status === '可用',
                      'status-unavailable': teacher.status === '不可用'
                    }"
                  >
                    {{ teacher.status }}
                  </span>
                  <div class="status-actions">
                    <button 
                      v-if="teacher.status === '不可用'"
                      @click="updateTeacherStatus(teacher, '可用')"
                      class="status-btn status-btn-enable"
                      title="设为可用"
                    >
                      <Check class="status-icon" />
                    </button>
                    <button 
                      v-if="teacher.status === '可用'"
                      @click="updateTeacherStatus(teacher, '不可用')"
                      class="status-btn status-btn-disable"
                      title="设为不可用"
                    >
                      <X class="status-icon" />
                    </button>
                  </div>
                </div>
              </td> -->
              <td>
                <div class="action-buttons">
                  <button 
                    class="action-btn-small action-btn-danger"
                    @click="deleteSingleTeacher(teacher)"
                    title="删除考官"
                  >
                    <Trash2 class="action-icon" />
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="teachers.length === 0">
              <td colspan="6" class="empty-state">
                <div class="empty-content">
                  <Users class="empty-icon" />
                  <p>暂无考官数据</p>
                  <button class="action-btn action-btn-primary" @click="showAddTeacherModal">
                    <Plus class="btn-icon" />
                    <span>添加第一个考官</span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 新增考官弹窗 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeAddModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">新增考官</h2>
          <button class="modal-close" @click="closeAddModal">
            <X class="close-icon" />
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveNewTeacher">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">姓名 *</label>
                <input 
                  type="text" 
                  v-model="newTeacher.name" 
                  class="form-input" 
                  placeholder="请输入考官姓名"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">所在科室 *</label>
                <select v-model="newTeacher.department" class="form-select" required>
                  <option value="">请选择科室</option>
                  <option value="区域一室">区域一室</option>
                  <option value="区域二室">区域二室</option>
                  <option value="区域三室">区域三室</option>
                  <option value="区域四室">区域四室</option>
                  <option value="区域五室">区域五室</option>
                  <option value="区域六室">区域六室</option>
                  <option value="区域七室">区域七室</option>
                </select>
              </div>
            </div>
            <div class="form-row">
               <div class="form-group">
                 <label class="form-label">所在班组</label>
                 <select v-model="newTeacher.group" class="form-select" @change="updateShiftForGroup">
                   <option value="">请选择班组</option>
                   <option value="一组">一组</option>
                   <option value="二组">二组</option>
                   <option value="三组">三组</option>
                   <option value="四组">四组</option>
                 </select>
               </div>
               <div class="form-group">
                 <label class="form-label">当日班次</label>
                 <input 
                   type="text" 
                   :value="newTeacher.shift" 
                   class="form-input" 
                   placeholder="根据班组自动显示"
                   readonly
                   disabled
                 />
               </div>
             </div>
            <div class="form-row">
              <div class="form-group full-width">
                <label class="form-label">状态</label>
                <div class="radio-group">
                  <label class="radio-item">
                    <input type="radio" v-model="newTeacher.status" value="可用" />
                    <span class="radio-text">可用</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="newTeacher.status" value="不可用" />
                    <span class="radio-text">不可用</span>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="action-btn action-btn-secondary" @click="closeAddModal">
            取消
          </button>
          <button type="button" class="action-btn action-btn-primary" @click="saveNewTeacher">
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content modal-small" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">确认删除</h2>
          <button class="modal-close" @click="cancelDelete">
            <X class="close-icon" />
          </button>
        </div>
        <div class="modal-body">
          <div class="confirm-content">
            <AlertCircle class="confirm-icon" />
            <p class="confirm-text">
              {{ deleteTarget ? 
                `确定要删除考官「${deleteTarget.name}」吗？` : 
                `确定要删除选中的 ${selectedTeachers.length} 个考官吗？`
              }}
            </p>
            <p class="confirm-warning">此操作不可撤销，请谨慎操作。</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="action-btn action-btn-secondary" @click="cancelDelete">
            取消
          </button>
          <button type="button" class="action-btn action-btn-danger" @click="confirmDelete">
            确认删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { 
  Home, 
  Users, 
  Calendar, 
  Settings,
  ChevronLeft,
  ChevronDown,
  Trash2,
  Download,
  Upload,
  Plus,
  X,
  Check,
  AlertCircle,
  BarChart
} from 'lucide-vue-next'
import { 
  calculateDutySchedule, 
  getGroupDutySchedule, 
  updateTeacherShift,
  type Teacher,
  type DutySchedule 
} from '../utils/scheduleService'
import { unifiedStorageService, type ExtendedTeacher, type UnifiedStorageConfig, type StorageStats } from '../services/unifiedStorageService'
import { useSidebarAutoCollapse } from '../composables/useSidebarAutoCollapse'
import { useResponsive } from '../composables/useResponsive'

// 使用响应式功能
const { isMobile, isTablet, isDesktop, modalConfig } = useResponsive()

// 考官数据类型定义 - 现在使用集中化的类型定义

// 响应式数据
const sidebarCollapsed = ref(false)
const dropdownOpen = ref(false)
const selectedStatus = ref('可用')
const showAddModal = ref(false)
const showDeleteConfirm = ref(false)
const deleteTarget = ref<Teacher | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)

// 自动收缩侧边栏功能
const { checkContentOverflowDelayed, triggerCheck } = useSidebarAutoCollapse(
  '.app-container',
  '.teachers-table',
  () => sidebarCollapsed.value,
  (collapsed: boolean) => { sidebarCollapsed.value = collapsed },
  {
    enableLogging: false
  }
)

// 存储配置
const storageStats = ref<StorageStats>({
  environment: 'web',
  primary: 'localStorage',
  cacheHits: 0,
  cacheMisses: 0,
  cacheHitRate: '0%',
  avgResponseTime: 0,
  dataSize: '0 B'
})

// 初始化存储服务
const initStorageService = async () => {
  try {
    // 统一存储服务已经是单例，直接初始化
    await unifiedStorageService.init()
    
    // 获取存储统计信息
    storageStats.value = unifiedStorageService.getStorageStats()
    console.log('统一存储服务初始化完成', storageStats.value)
  } catch (error) {
    console.error('存储服务初始化失败', error)
  }
}

// 从存储加载考官数据
const loadTeachersFromStorage = async (): Promise<Teacher[]> => {
  try {
    const teachers = await unifiedStorageService.loadTeachers()
    console.log('从存储加载考官数据', teachers.length, '条记录')
    // 使用集中化服务更新所有考官的班次（确保班次是最新的）
    return teachers.map((teacher: ExtendedTeacher) => updateTeacherShift(teacher as Teacher))
  } catch (error) {
    console.error('加载考官数据失败:', error)
    return []
  }
}

// 保存考官数据到存储
const saveTeachersToStorage = async (teacherList: ExtendedTeacher[]) => {
  try {
    await unifiedStorageService.saveTeachers(teacherList)
    // 更新存储统计信息
    storageStats.value = unifiedStorageService.getStorageStats()
  } catch (error) {
    console.error('保存考官数据失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    alert('数据保存失败: ' + errorMessage)
  }
}

// 考官列表数据 - 异步加载
const teachers = ref<Teacher[]>([])
const isLoading = ref(true)

// 初始化考官数据
const initializeTeachers = async () => {
  isLoading.value = true
  try {
    await initStorageService()
    const loadedTeachers = await loadTeachersFromStorage()
    teachers.value = loadedTeachers
    
    // 数据加载完成后检查是否需要自动收缩侧边栏
    checkContentOverflowDelayed(300)
  } catch (error) {
    console.error('初始化考官数据失败', error)
  } finally {
    isLoading.value = false
  }
}

// 防抖保存函数 - 避免频繁保存影响性能
let saveTimeout: NodeJS.Timeout | null = null
const debouncedSave = async (teacherList: Teacher[]) => {
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
  saveTimeout = setTimeout(async () => {
    await saveTeachersToStorage(teacherList)
  }, 500) // 500ms防抖延迟
}

// 监听考官数据变化，使用防抖机制自动保存
watch(teachers, (newTeachers) => {
  if (!isLoading.value) { // 避免初始化时触发保存
    debouncedSave(newTeachers)
  }
}, { deep: true })

// 数据存储状态检查
const checkDataStorage = () => {
  console.log('=== 数据存储状态检查 ===')
  console.log('当前考官数据数量:', teachers.value.length)
  console.log('考官数据详情:', teachers.value)
  
  // 检查localStorage中是否有相关数据
  const localStorageKeys = Object.keys(localStorage).filter(key => 
    key.includes('teacher') || key.includes('examiner') || key.includes('schedule')
  )
  console.log('localStorage相关数据:', localStorageKeys)
  
  // 检查sessionStorage中是否有相关数据
  const sessionStorageKeys = Object.keys(sessionStorage).filter(key => 
    key.includes('teacher') || key.includes('examiner') || key.includes('schedule')
  )
  console.log('sessionStorage相关数据:', sessionStorageKeys)
  
  console.log('=== 数据存储检查完成 ===')
}

// 清除所有测试数据
const clearAllData = () => {
  console.log('=== 开始清除测试数据 ===')
  
  // 清空考官列表（这会触发watch自动保存空数组到localStorage）
  teachers.value = []
  
  // 额外确保清除localStorage中的考官数据
  localStorage.removeItem('examiner_teachers')
  console.log('已清除localStorage中的考官数据')
  
  // 清除其他相关数据
  const localStorageKeys = Object.keys(localStorage).filter(key => 
    key.includes('teacher') || key.includes('examiner') || key.includes('schedule')
  )
  localStorageKeys.forEach(key => {
    localStorage.removeItem(key)
    console.log(`已清除localStorage数据: ${key}`)
  })
  
  // 清除sessionStorage中的相关数据
  const sessionStorageKeys = Object.keys(sessionStorage).filter(key => 
    key.includes('teacher') || key.includes('examiner') || key.includes('schedule')
  )
  sessionStorageKeys.forEach(key => {
    sessionStorage.removeItem(key)
    console.log(`已清除sessionStorage数据: ${key}`)
  })
  
  console.log('=== 测试数据清除完成 ===')
  console.log('当前考官数据数量:', teachers.value.length)
  
  alert('所有数据已清除完毕！数据将不再持久化保存')
}

// 新增考官表单数据
const newTeacher = ref<Partial<Teacher>>({
  name: '',
  department: '',
  group: '',
  shift: '',
  status: '可用',
  specialties: []
})

// 计算属性
const selectedTeachers = computed(() => 
  teachers.value.filter(teacher => teacher.selected)
)

const hasSelectedTeachers = computed(() => 
  selectedTeachers.value.length > 0
)

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 切换下拉菜单
const toggleDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value
}

// 选择状态
const selectStatus = (status: string) => {
  selectedStatus.value = status
  dropdownOpen.value = false
}

// 全选/取消全选
const toggleSelectAll = () => {
  const allSelected = teachers.value.every(teacher => teacher.selected)
  teachers.value.forEach(teacher => {
    teacher.selected = !allSelected
  })
}

// 切换单个考官选择状态
const toggleTeacherSelect = (teacher: Teacher) => {
  teacher.selected = !teacher.selected
}

// 显示新增考官弹窗
const showAddTeacherModal = () => {
  newTeacher.value = {
    name: '',
    department: '',
    group: '',
    shift: '',
    status: '可用',
    specialties: []
  }
  showAddModal.value = true
}

// 根据班组更新当日班次
const updateShiftForGroup = () => {
  if (!newTeacher.value.group) {
    newTeacher.value.shift = ''
    return
  }
  
  // 使用集中化的调度服务计算当日班次
  const today = new Date()
  const groupSchedule = getGroupDutySchedule(today, newTeacher.value.group)
  
  if (groupSchedule) {
    newTeacher.value.shift = groupSchedule.status
  } else {
    newTeacher.value.shift = '未排班'
  }
}

// 关闭新增考官弹窗
const closeAddModal = () => {
  showAddModal.value = false
}

// 保存新增考官
const saveNewTeacher = () => {
  if (!newTeacher.value.name || !newTeacher.value.department || !newTeacher.value.group) {
    alert('请填写必要信息（姓名、科室、班组）')
    return
  }
  
  const baseTeacher: Teacher = {
    id: Date.now().toString(),
    name: newTeacher.value.name!,
    department: newTeacher.value.department!,
    group: newTeacher.value.group!,
    shift: '',
    status: newTeacher.value.status || '可用',
    specialties: newTeacher.value.specialties || [],
    selected: false
  }
  
  // 使用集中化服务确保班次计算一致性
  const teacher = updateTeacherShift(baseTeacher)
  
  teachers.value.push(teacher)
  closeAddModal()
  alert('考官添加成功')
}

// 删除选中的考官
const deleteSelectedTeachers = () => {
  if (!hasSelectedTeachers.value) {
    alert('请先选择要删除的考官')
    return
  }
  
  showDeleteConfirm.value = true
}

// 删除单个考官
const deleteSingleTeacher = (teacher: Teacher) => {
  deleteTarget.value = teacher
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = () => {
  if (deleteTarget.value) {
    // 删除单个考官
    const index = teachers.value.findIndex(t => t.id === deleteTarget.value!.id)
    if (index > -1) {
      teachers.value.splice(index, 1)
    }
  } else {
    // 删除选中的考官
    teachers.value = teachers.value.filter(teacher => !teacher.selected)
  }
  
  showDeleteConfirm.value = false
  deleteTarget.value = null
  alert('删除成功')
}

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false
  deleteTarget.value = null
}

// 导入考官数据
const importTeachers = () => {
  fileInput.value?.click()
}

// 处理文件上传（支持XLSX和CSV格式）
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // 支持XLSX和CSV文件导入
  const isXLSX = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
  const isCSV = file.name.endsWith('.csv')
  
  if (!isXLSX && !isCSV) {
    alert('请选择Excel文件(.xlsx, .xls)或CSV文件')
    return
  }
  
  try {
    let importedTeachers: Teacher[] = []
    
    if (isXLSX) {
      // 处理Excel文件
      importedTeachers = await handleXLSXImport(file)
    } else {
      // 处理CSV文件
      importedTeachers = await handleCSVImport(file)
    }
    
    if (importedTeachers.length > 0) {
      teachers.value.push(...importedTeachers)
      alert(`成功导入 ${importedTeachers.length} 条考官数据！`)
    } else {
      alert('没有有效的数据可导入')
    }
    
  } catch (error) {
    console.error('导入文件时出错', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    alert('导入失败，请检查文件格式是否正确：' + errorMessage)
  }
  
  target.value = '' // 清空文件输入
}

// 处理XLSX文件导入
const handleXLSXImport = async (file: File): Promise<Teacher[]> => {
  const XLSX = await import('xlsx')
  
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        
        // 读取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        
        // 转换为JSON数据
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        if (jsonData.length < 2) {
          reject(new Error('Excel文件格式不正确，至少需要标题行和一行数据'))
          return
        }
        
        const rawHeaders = jsonData[0] as any[]
          const headers = rawHeaders.map(h => String(h || '').trim()).filter(h => h)
          const expectedHeaders = ['姓名', '所在科室', '所在班组', '状态']
          
          console.log('Excel导入调试信息:')
          console.log('原始标题:', rawHeaders)
          console.log('处理后标题', headers)
          
          // 创建列标题映射，支持多种可能的列名
          const headerMapping: { [key: string]: string[] } = {
            '姓名': ['姓名', '名字', 'name', '考官姓名', '员工姓名', '人员姓名'],
            '所在科室': ['所在科室', '科室', '部门', 'department', '所属科室', '工作科室'],
            '所在班组': ['所在班组', '班组', 'group', '组别', '小组', '工作组'],
            '状态': ['状态', 'status', '可用状态', '考官状态', '工作状态']
          }
          
          // 查找实际的列索引
          const columnIndexes: { [key: string]: number } = {}
          Object.keys(headerMapping).forEach(standardHeader => {
            const possibleNames = headerMapping[standardHeader]
            for (let i = 0; i < headers.length; i++) {
              const header = headers[i].toLowerCase().trim()
              const found = possibleNames.some(name => {
                const nameLower = name.toLowerCase()
                return header === nameLower || 
                       header.includes(nameLower) || 
                       nameLower.includes(header)
              })
              if (found) {
                columnIndexes[standardHeader] = i
                console.log(`找到列映射: ${standardHeader} -> 第${i}列(${headers[i]})`)
                break
              }
            }
          })
          
          console.log('列索引映射', columnIndexes)
          
          // 验证必要的列是否存在
          const requiredHeaders = ['姓名', '所在科室', '所在班组']
          const missingHeaders = requiredHeaders.filter(header => columnIndexes[header] === undefined)
          if (missingHeaders.length > 0) {
            const actualHeaders = headers.join(', ')
            const debugInfo = `\n\n调试信息:\n- 实际列数: ${headers.length}\n- 实际列标题: [${actualHeaders}]\n- 缺少的列: [${missingHeaders.join(', ')}]\n- 支持的姓名列格式: ${headerMapping['姓名'].join(', ')}`
            reject(new Error(`Excel文件缺少必要的列：${missingHeaders.join(', ')}${debugInfo}\n\n提示：请确保Excel文件包含必要的列，列标题可以是中文或英文`))
            return
          }
        
        const importedTeachers: Teacher[] = []
        
        // 处理数据行
         for (let i = 1; i < jsonData.length; i++) {
           const row = jsonData[i] as any[]
           if (!row || row.length === 0) continue
           
           // 使用列索引映射提取数据
           const name = String(row[columnIndexes['姓名']] || '').trim()
           const rawDepartment = String(row[columnIndexes['所在科室']] || '').trim()
           const group = String(row[columnIndexes['所在班组']] || '').trim()
           const status = String(row[columnIndexes['状态']] || '').trim()
           
           // 规范化科室名称为"区域X室"格式
           const department = normalizeDepartmentName(rawDepartment)
           
           // 验证必要字段
           if (!name || !department || !group) {
             console.warn(`第${i + 1}行数据不完整，跳过：姓名=${name}, 科室=${department}, 班组=${group}`)
             continue
           }
           
           const baseTeacher: Teacher = {
             id: Date.now().toString() + '_' + i,
             name: name,
             department: department,
             group: group,
             shift: '', // 将通过updateTeacherShift自动计算
             status: (status === '不可用' ? '不可用' : '可用') as '可用' | '不可用',
             specialties: [],
             selected: false
           }
           
           // 使用集中化服务更新班次
           const teacher = updateTeacherShift(baseTeacher)
           importedTeachers.push(teacher)
         }
        
        resolve(importedTeachers)
        
      } catch (error) {
        reject(error instanceof Error ? error : new Error(String(error)))
      }
    }
    
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsArrayBuffer(file)
  })
}

// 处理CSV文件导入
const handleCSVImport = async (file: File): Promise<Teacher[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string
        if (!text) {
          reject(new Error('文件内容为空'))
          return
        }
        
        // 解析CSV内容
        const lines = text.split('\n').filter(line => line.trim())
        if (lines.length < 2) {
          reject(new Error('CSV文件格式不正确，至少需要标题行和一行数据'))
          return
        }
        
        // 解析标题行
        const headers = parseCSVLine(lines[0])
        const expectedHeaders = ['姓名', '所在科室', '所在班组', '状态']
        
        // 验证必要的列是否存在
        const requiredHeaders = ['姓名', '所在科室', '所在班组']
        const missingHeaders = requiredHeaders.filter(header => !headers.includes(header))
        if (missingHeaders.length > 0) {
          reject(new Error(`CSV文件缺少必要的列：${missingHeaders.join(', ')}\n\n期望的列：${expectedHeaders.join(', ')}`))
          return
        }
        
        // 解析数据行
        const importedTeachers: Teacher[] = []
        
        for (let i = 1; i < lines.length; i++) {
          try {
            const values = parseCSVLine(lines[i])
            if (values.length === 0) continue
            
            // 创建考官对象
            const teacherData: any = {}
            headers.forEach((header, index) => {
              teacherData[header] = values[index] || ''
            })
            
            // 验证必要字段
            if (!teacherData['姓名'] || !teacherData['所在科室'] || !teacherData['所在班组']) {
              console.warn(`第${i + 1}行数据不完整，跳过`)
              continue
            }
            
            // 规范化科室名称
            const rawDepartment = teacherData['所在科室'].trim()
           const normalizedDepartment = normalizeDepartmentName(rawDepartment)
           
           const baseTeacher: Teacher = {
             id: Date.now().toString() + '_' + i,
             name: teacherData['姓名'].trim(),
             department: normalizedDepartment,
             group: teacherData['所在班组'].trim(),
             shift: '', // 将通过updateTeacherShift自动计算
             status: (teacherData['状态'] === '不可用' ? '不可用' : '可用') as '可用' | '不可用',
             specialties: [],
             selected: false
           }
            
            // 使用集中化服务更新班次
            const teacher = updateTeacherShift(baseTeacher)
            importedTeachers.push(teacher)
            
          } catch (error) {
            console.error(`解析第${i + 1}行数据时出错:`, error)
          }
        }
        
        resolve(importedTeachers)
        
      } catch (error) {
        reject(error instanceof Error ? error : new Error(String(error)))
      }
    }
    
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsText(file, 'utf-8')
  })
}

// 规范化科室名称为"区域X室"格式
const normalizeDepartmentName = (rawName: string): string => {
  if (!rawName || !rawName.trim()) {
    return ''
  }
  
  const name = rawName.trim().toLowerCase()
  
  // 🔧 新增：检测非法科室名称（考试科目）
  const illegalKeywords = ['模拟机', '现场', '口试', '理论', '实操', '实践', '笔试']
  for (const keyword of illegalKeywords) {
    if (name.includes(keyword.toLowerCase())) {
      console.error(`🚨 [数据错误] 考官科室不能是考试科目: "${rawName}"`)
      throw new Error(`数据错误：考官科室 "${rawName}" 是考试科目，不是科室名称！\n请检查CSV文件中"所在科室"列的数据。`)
    }
  }
  
  // 定义科室名称映射规则
  const departmentMapping: { [key: string]: string } = {
    // 标准格式（已经是正确格式）
    '区域一室': '区域一室',
    '区域二室': '区域二室', 
    '区域三室': '区域三室',
    '区域四室': '区域四室',
    '区域五室': '区域五室',
    '区域六室': '区域六室',
    '区域七室': '区域七室',
    '区域八室': '区域八室',
    '区域九室': '区域九室',
    '区域十室': '区域十室',
    
    // 数字格式变体
    '区域1室': '区域一室',
    '区域2室': '区域二室',
    '区域3室': '区域三室', 
    '区域4室': '区域四室',
    '区域5室': '区域五室',
    '区域6室': '区域六室',
    '区域7室': '区域七室',
    '区域8室': '区域八室',
    '区域9室': '区域九室',
    '区域10室': '区域十室',
    
    // 简化格式
    '一室': '区域一室',
    '二室': '区域二室',
    '三室': '区域三室',
    '四室': '区域四室',
    '五室': '区域五室',
    '六室': '区域六室',
    '七室': '区域七室',
    '八室': '区域八室',
    '九室': '区域九室',
    '十室': '区域十室',
    
    // 英文数字格式
    '区域1': '区域一室',
    '区域2': '区域二室',
    '区域3': '区域三室',
    '区域4': '区域四室',
    '区域5': '区域五室',
    '区域6': '区域六室',
    '区域7': '区域七室',
    '区域8': '区域八室',
    '区域9': '区域九室',
    '区域10': '区域十室',
    
    // 部门格式
    '第一区域': '区域一室',
    '第二区域': '区域二室',
    '第三区域': '区域三室',
    '第四区域': '区域四室',
    '第五区域': '区域五室',
    '第六区域': '区域六室',
    '第七区域': '区域七室',
    '第八区域': '区域八室',
    '第九区域': '区域九室',
    '第十区域': '区域十室',
    
    // 其他可能的格式
    '1区': '区域一室',
    '2区': '区域二室',
    '3区': '区域三室',
    '4区': '区域四室',
    '5区': '区域五室',
    '6区': '区域六室',
    '7区': '区域七室',
    '8区': '区域八室',
    '9区': '区域九室',
    '10区': '区域十室',
    
    '一区': '区域一室',
    '二区': '区域二室',
    '三区': '区域三室',
    '四区': '区域四室',
    '五区': '区域五室',
    '六区': '区域六室',
    '七区': '区域七室',
    '八区': '区域八室',
    '九区': '区域九室',
    '十区': '区域十室'
  }
  
  // 直接匹配
  const directMatch = departmentMapping[name]
  if (directMatch) {
    return directMatch
  }
  
  // 模糊匹配 - 提取数字或中文数字
  const numberPatterns = [
    { pattern: /区域?[一1]/, target: '区域一室' },
    { pattern: /区域?[二2]/, target: '区域二室' },
    { pattern: /区域?[三3]/, target: '区域三室' },
    { pattern: /区域?[四4]/, target: '区域四室' },
    { pattern: /区域?[五5]/, target: '区域五室' },
    { pattern: /区域?[六6]/, target: '区域六室' },
    { pattern: /区域?[七7]/, target: '区域七室' },
    { pattern: /区域?[八8]/, target: '区域八室' },
    { pattern: /区域?[九9]/, target: '区域九室' },
    { pattern: /区域?[十10]/, target: '区域十室' }
  ]
  
  for (const { pattern, target } of numberPatterns) {
    if (pattern.test(name)) {
      console.log(`科室名称规范化: "${rawName}" -> "${target}"`)
      return target
    }
  }
  
  // 如果无法识别，返回原始值并记录日志
  console.warn(`无法识别的科室名称格式: "${rawName}"，保持原样`)
  return rawName.trim()
}

// 解析CSV行（处理引号和逗号）
const parseCSVLine = (line: string): string[] => {
  const result: string[] = []
  let current = ''
  let inQuotes = false
  let i = 0
  
  while (i < line.length) {
    const char = line[i]
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // 双引号转义
        current += '"'
        i += 2
      } else {
        // 切换引号状态
        inQuotes = !inQuotes
        i++
      }
    } else if (char === ',' && !inQuotes) {
      // 字段分隔符
      result.push(current.trim())
      current = ''
      i++
    } else {
      current += char
      i++
    }
  }
  
  // 添加最后一个字段
  result.push(current.trim())
  
  return result
}

// 导出考官数据为XLSX格式
const exportTeachers = async () => {
  if (teachers.value.length === 0) {
    alert('没有考官数据可导出！')
    return
  }
  
  try {
    // 动态导入xlsx库
    const XLSX = await import('xlsx')
    
    // 准备导出数据（移除专业特长字段）
    const exportData = teachers.value.map(teacher => ({
      '姓名': teacher.name,
      '所在科室': teacher.department,
      '所在班组': teacher.group,
      '当日班次': teacher.shift,
      '状态': teacher.status
    }))
    
    // 创建工作簿和工作表
    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.json_to_sheet(exportData)
    
    // 自动调整列宽
    const columnWidths: Array<{ wch: number }> = []
    const headers = Object.keys(exportData[0] || {})
    
    headers.forEach((header, index) => {
      // 计算标题长度
      let maxWidth = header.length * 2 // 中文字符宽度估算
      
      // 计算数据列的最大宽度
      exportData.forEach(row => {
        const cellValue = String(row[header as keyof typeof row] || '')
        const cellWidth = cellValue.length * (cellValue.match(/[\u4e00-\u9fa5]/g) ? 2 : 1)
        maxWidth = Math.max(maxWidth, cellWidth)
      })
      
      // 设置合理的列宽范围
      columnWidths.push({ wch: Math.min(Math.max(maxWidth, 10), 30) })
    })
    
    worksheet['!cols'] = columnWidths
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '考官数据')
    
    // 生成文件并下载
    const fileName = `考官数据_${new Date().toISOString().split('T')[0]}.xlsx`
    XLSX.writeFile(workbook, fileName)
    
    alert('考官数据导出成功！')
    
  } catch (error) {
    console.error('导出失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    alert('导出失败，请稍后重试: ' + errorMessage)
  }
}

// 更新考官状态
const updateTeacherStatus = (teacher: Teacher, status: '可用' | '不可用') => {
  teacher.status = status
}

// 刷新所有考官的班次信息以确保数据同步
const refreshAllTeacherShifts = () => {
  teachers.value = teachers.value.map(teacher => updateTeacherShift(teacher))
  console.log('所有考官班次已刷新以确保数据同步')
}

// 定时器变量
let shiftUpdateInterval: NodeJS.Timeout | null = null

// 组件挂载时初始化数据和检查存储状态
onMounted(async () => {
  await initializeTeachers()
  refreshAllTeacherShifts()
  checkDataStorage()
  
  // 每小时更新考官班次信息（以防跨日变化）
  shiftUpdateInterval = setInterval(refreshAllTeacherShifts, 60 * 60 * 1000)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (shiftUpdateInterval) {
    clearInterval(shiftUpdateInterval)
    shiftUpdateInterval = null
  }
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 主容器 */
.app-container {
  width: 100%;
  max-width: 100vw;
  height: 100%;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
}

/* 侧边栏样式 */
.sidebar {
  width: var(--sidebar-width);
  height: 100%;
  background: linear-gradient(180deg, #1e3a5f 0%, #2c5282 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
}

.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-text {
  flex: 1;
}

.system-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.system-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 导航样式 */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 20px;
}

.sidebar-collapsed .nav-items {
  padding: 0 10px;
}

.sidebar-collapsed .nav-item {
  justify-content: center;
  padding: 12px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item-active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;
}

.sidebar-toggle:hover {
  transform: translateY(-50%) scale(1.1);
}

.toggle-icon {
  width: 16px;
  height: 16px;
  color: #374151;
  transition: transform 0.3s ease;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  height: 100%;
  background: #f5f7fa;
  padding: 32px;
  overflow-y: auto;
}

/* 页面标题区 */
.page-header {
  background: white;
  border-radius: 16px;
  padding: 20px 24px;
  margin-bottom: 24px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

/* 存储信息区域 */
.storage-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.storage-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #6b7280;
}

.stats-item {
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 4px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn-secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn-secondary:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.teachers-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.teachers-table th {
  background: #f9fafb;
  padding: 16px 20px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.teachers-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
}

.teachers-table tbody tr:hover {
  background: #f9fafb;
}

.teachers-table tbody tr:last-child td {
  border-bottom: none;
}

.teachers-table tr.selected {
  background: #eff6ff;
}

.checkbox-column {
  width: 50px;
  text-align: center;
}

.checkbox-column input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 空状态样式*/
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 48px;
  height: 48px;
  color: #9ca3af;
}

.empty-content p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

/* 状态相关样式*/
.status-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-text.status-available {
  background: #dcfce7;
  color: #166534;
}

.status-text.status-unavailable {
  background: #fee2e2;
  color: #991b1b;
}

.status-actions {
  display: flex;
  gap: 4px;
}

.status-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-btn-enable {
  background: #dcfce7;
  color: #166534;
}

.status-btn-enable:hover {
  background: #bbf7d0;
}

.status-btn-disable {
  background: #fee2e2;
  color: #991b1b;
}

.status-btn-disable:hover {
  background: #fecaca;
}

.status-icon {
  width: 14px;
  height: 14px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn-small {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn-danger {
  background: #fee2e2;
  color: #991b1b;
}

.action-btn-danger:hover {
  background: #fecaca;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* 禁用状态*/
.action-btn-secondary.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  margin: auto;
}

.modal-small {
  max-width: 450px;
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.modal-close:hover {
  background: #e5e7eb;
}

.close-icon {
  width: 18px;
  height: 18px;
  color: #6b7280;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 表单样式 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  flex: none;
  width: 100%;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
   color: #9ca3af;
 }
 
 .form-input:disabled {
   background-color: #f9fafb;
   color: #6b7280;
   cursor: not-allowed;
 }

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.radio-text {
  font-size: 14px;
  color: #374151;
}

/* 确认弹窗样式 */
.confirm-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
}

.confirm-icon {
  width: 48px;
  height: 48px;
  color: #f59e0b;
}

.confirm-text {
  font-size: 16px;
  color: #1f2937;
  margin: 0;
}

.confirm-warning {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .app-container {
    width: 100%;
    max-width: 100vw;
  }
  
  .main-content {
    padding: 24px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: flex-end;
    flex-wrap: wrap;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar {
    width: 240px;
  }
  
  .sidebar-collapsed {
    width: 70px;
  }
  
  .main-content {
    padding: 20px;
  }
  
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .teachers-table {
    min-width: 700px;
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .sidebar {
    width: 240px;
  }
  
  .sidebar-collapsed {
    width: 60px;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px 20px;
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .header-actions {
    justify-content: flex-start;
    gap: 8px;
  }
  
  .action-btn {
    padding: 8px 12px;
    font-size: 13px;
    flex: 1;
    min-width: fit-content;
  }
  
  .btn-icon {
    width: 14px;
    height: 14px;
  }
  
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 12px;
  }
  
  .teachers-table {
    min-width: 650px;
  }
  
  .teachers-table th,
  .teachers-table td {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .checkbox-column {
    width: 40px;
  }
  
  .modal-content {
    max-width: 95%;
    width: 95%;
    margin: 0;
    border-radius: 16px;
  }
  
  .modal-header {
    padding: 20px 20px 0;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-footer {
    padding: 0 20px 20px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .storage-stats {
    flex-direction: column;
    gap: 8px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .app-container {
    font-size: 13px;
  }
  
  .sidebar {
    width: 200px;
  }
  
  .main-content {
    padding: 12px;
  }
  
  .page-header {
    padding: 12px 16px;
    margin-bottom: 12px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .storage-stats {
    font-size: 10px;
  }
  
  .header-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .action-btn {
    justify-content: center;
    padding: 8px 12px;
    font-size: 12px;
    flex: 1;
    min-width: calc(50% - 3px);
  }
  
  .teachers-table {
    min-width: 600px;
  }
  
  .teachers-table th,
  .teachers-table td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .checkbox-column {
    width: 36px;
  }
  
  .action-btn-small {
    width: 28px;
    height: 28px;
  }
  
  .action-icon {
    width: 14px;
    height: 14px;
  }
  
  .modal-content {
    max-width: 98%;
    width: 98%;
    margin: 0;
    max-height: 95vh;
    border-radius: 12px;
  }
  
  .modal-header {
    padding: 16px 16px 0;
  }
  
  .modal-title {
    font-size: 18px;
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .modal-footer {
    padding: 0 16px 16px;
    flex-direction: row;
    gap: 8px;
  }
  
  .modal-footer .action-btn {
    flex: 1;
  }
  
  .form-input,
  .form-select {
    padding: 10px 12px;
    font-size: 14px;
  }
  
  .form-label {
    font-size: 13px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .action-btn,
  .nav-item,
  .sidebar-toggle {
    min-height: 44px;
  }
  
  .action-btn-small {
    min-width: 44px;
    min-height: 44px;
  }
  
  .checkbox-column input[type="checkbox"] {
    width: 20px;
    height: 20px;
  }
}

/* 横屏手机优化 */
@media (max-width: 920px) and (orientation: landscape) {
  .modal-content {
    max-height: 85vh;
  }
  
  .main-content {
    padding: 16px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1536px) {
  .page-header {
    padding: 24px 28px;
  }
  
  .page-title {
    font-size: 26px;
  }
  
  .action-btn {
    padding: 10px 20px;
    font-size: 15px;
  }
  
  .teachers-table th,
  .teachers-table td {
    padding: 18px 24px;
    font-size: 15px;
  }
}
</style>
