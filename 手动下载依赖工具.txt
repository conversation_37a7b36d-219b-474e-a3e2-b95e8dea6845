================================================================
        如何手动下载electron-builder依赖工具
================================================================

如果自动下载失败（网络问题或权限问题），可以手动下载：

【步骤1: 下载winCodeSign工具】
===============================

1. 下载地址:
   https://github.com/electron-userland/electron-builder-binaries/releases/download/winCodeSign-2.6.0/winCodeSign-2.6.0.7z

2. 手动下载后，放到以下目录:
   C:\Users\<USER>\AppData\Local\electron-builder\Cache\winCodeSign\winCodeSign-2.6.0.7z

3. 注意事项:
   - 不要解压，直接放7z文件
   - 确保文件名正确: winCodeSign-2.6.0.7z
   - 确保目录存在，不存在请手动创建


【步骤2: 清理之前的失败缓存】
===============================

删除以下目录中的所有内容:
C:\Users\<USER>\AppData\Local\electron-builder\Cache\winCodeSign\

注意: 只删除winCodeSign目录下的内容，不要删除Cache目录本身


【步骤3: 重新打包】
===============================

以管理员权限运行:
  完整打包-管理员权限.bat


【解决符号链接权限问题】
===============================

方法1: 以管理员权限运行（推荐）
   右键点击批处理文件 -> 以管理员身份运行

方法2: 启用开发者模式（Windows 10/11）
   设置 -> 更新和安全 -> 开发者选项 -> 开发人员模式
   重启电脑后符号链接权限会自动获得

方法3: 修改组策略
   1. Win+R 运行 gpedit.msc
   2. 计算机配置 -> Windows设置 -> 安全设置 -> 本地策略 -> 用户权限分配
   3. 找到"创建符号链接"
   4. 添加你的用户账户
   5. 重启电脑


【其他依赖工具】
===============================

如果还需要下载其他工具，可能的位置：

1. nsis (安装程序制作工具)
   下载: https://github.com/electron-userland/electron-builder-binaries/releases/
   
2. app-builder
   自动下载，通常不需要手动处理


【验证下载成功】
===============================

检查以下文件是否存在:
C:\Users\<USER>\AppData\Local\electron-builder\Cache\winCodeSign\winCodeSign-2.6.0.7z

文件大小应该是: 5.6 MB (5,635,384 字节)


【仍然失败？】
===============================

如果手动下载后仍然失败，可能的原因:

1. 杀毒软件拦截
   解决: 暂时关闭杀毒软件或添加白名单

2. 磁盘空间不足
   解决: 清理磁盘空间，至少保留 5GB 可用空间

3. 文件损坏
   解决: 重新下载winCodeSign-2.6.0.7z

4. 网络代理问题
   解决: 设置npm代理或使用VPN


================================================================
                    需要更多帮助？
================================================================

查看完整日志:
  打包时的完整输出会显示具体错误原因

查看缓存目录:
  C:\Users\<USER>\AppData\Local\electron-builder\Cache\

强制重新下载:
  删除整个Cache目录，重新运行打包脚本

================================================================
