package com.examiner.scheduler.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Objects;

/**
 * 考官实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Teacher {
    
    private String id;
    private String name;
    private String department; // 一、二、三、四、五、六、七
    private String group;      // 一组、二组、三组、四组、无
    private int workload;
    private int consecutiveDays;
    
    // 构造函数
    public Teacher() {}
    
    public Teacher(String id, String name, String department, String group) {
        this.id = id;
        this.name = name;
        this.department = department;
        this.group = group;
        this.workload = 0;
        this.consecutiveDays = 0;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getGroup() {
        return group;
    }
    
    public void setGroup(String group) {
        this.group = group;
    }
    
    public int getWorkload() {
        return workload;
    }
    
    public void setWorkload(int workload) {
        this.workload = workload;
    }
    
    public int getConsecutiveDays() {
        return consecutiveDays;
    }
    
    public void setConsecutiveDays(int consecutiveDays) {
        this.consecutiveDays = consecutiveDays;
    }
    
    /**
     * 检查考官是否可用（不是白班）
     */
    public boolean isAvailableForDate(String date, DutySchedule dutySchedule) {
        // 无班组的考官始终可用
        if ("无".equals(this.group)) {
            return true;
        }
        
        // 白班考官不可用作考官
        return !this.group.equals(dutySchedule.getDayShift());
    }
    
    /**
     * 获取考官在指定日期的优先级
     * 晚班 > 休息 > 无班组 > 白班（不可用）
     */
    public int getPriorityForDate(String date, DutySchedule dutySchedule) {
        if ("无".equals(this.group)) {
            return 20; // 无班组考官中等优先级
        }
        
        if (this.group.equals(dutySchedule.getDayShift())) {
            return 0; // 白班不可用
        }
        
        if (this.group.equals(dutySchedule.getNightShift())) {
            return 40; // 晚班最高优先级
        }
        
        if (dutySchedule.getRestGroups().contains(this.group)) {
            return 30; // 休息班组次高优先级
        }
        
        return 10; // 默认优先级
    }
    
    /**
     * 检查是否与学员同科室
     */
    public boolean isSameDepartment(Student student) {
        return Objects.equals(this.department, student.getDepartment());
    }
    
    /**
     * 检查是否与学员不同科室
     */
    public boolean isDifferentDepartment(Student student) {
        return !Objects.equals(this.department, student.getDepartment());
    }
    
    /**
     * 获取考官的班次类型
     * 根据班组信息推断班次类型
     */
    public String getShiftType() {
        if ("无".equals(this.group)) {
            return "无班组";
        }
        // 这里需要根据实际的值班安排来确定班次
        // 暂时返回默认值，实际应该查询DutySchedule
        return "未知";
    }
    
    /**
     * 获取考官的工作状态
     * 用于约束检查，区分行政班和其他班次
     */
    public String getWorkStatus() {
        if ("无".equals(this.group)) {
            return "无班组";
        }
        // 根据班组信息判断是否为行政班
        // 这里需要根据实际业务逻辑来确定行政班的判断标准
        // 暂时返回默认值，实际应该根据具体的班组配置
        return "普通班";
    }
    
    /**
     * 检查是否可以作为考官1（必须同科室）
     */
    public boolean canBeExaminer1(Student student, DutySchedule dutySchedule) {
        return isAvailableForDate(dutySchedule.getDate(), dutySchedule) && 
               isSameDepartment(student);
    }
    
    /**
     * 检查是否可以作为考官2（必须不同科室）
     */
    public boolean canBeExaminer2(Student student, DutySchedule dutySchedule) {
        return isAvailableForDate(dutySchedule.getDate(), dutySchedule) && 
               isDifferentDepartment(student);
    }
    
    /**
     * 检查是否可以作为备份考官（必须不同科室）
     */
    public boolean canBeBackupExaminer(Student student, DutySchedule dutySchedule) {
        return isAvailableForDate(dutySchedule.getDate(), dutySchedule) && 
               isDifferentDepartment(student);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Teacher teacher = (Teacher) o;
        return Objects.equals(id, teacher.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Teacher{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", department='" + department + '\'' +
                ", group='" + group + '\'' +
                ", workload=" + workload +
                '}';
    }
}