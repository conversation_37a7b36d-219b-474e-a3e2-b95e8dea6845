package com.examiner.scheduler.websocket;

import javax.enterprise.context.ApplicationScoped;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * WebSocket端点，用于实时推送排班统计信息
 */
@ServerEndpoint("/ws/statistics")
@ApplicationScoped
public class StatisticsWebSocket {
    
    private static final Logger LOGGER = Logger.getLogger(StatisticsWebSocket.class.getName());
    private static final Set<Session> sessions = Collections.synchronizedSet(new HashSet<>());
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    static {
        // 启动定时任务，每5秒发送一次统计信息
        scheduler.scheduleAtFixedRate(() -> {
            try {
                broadcastStatistics();
            } catch (Exception e) {
                LOGGER.severe("发送统计信息失败: " + e.getMessage());
            }
        }, 0, 5, TimeUnit.SECONDS);
    }
    
    @OnOpen
    public void onOpen(Session session) {
        sessions.add(session);
        LOGGER.info("WebSocket连接已建立，当前连接数: " + sessions.size());
        
        // 立即发送当前统计信息
        try {
            sendStatistics(session);
        } catch (IOException e) {
            LOGGER.warning("发送初始统计信息失败: " + e.getMessage());
        }
    }
    
    @OnClose
    public void onClose(Session session) {
        sessions.remove(session);
        LOGGER.info("WebSocket连接已关闭，当前连接数: " + sessions.size());
    }
    
    @OnError
    public void onError(Session session, Throwable throwable) {
        LOGGER.severe("WebSocket错误: " + throwable.getMessage());
        sessions.remove(session);
    }
    
    @OnMessage
    public void onMessage(String message, Session session) {
        LOGGER.info("收到WebSocket消息: " + message);
        // 可以根据消息类型处理不同的请求
        try {
            sendStatistics(session);
        } catch (IOException e) {
            LOGGER.warning("响应消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 向所有连接的客户端广播统计信息
     */
    private static void broadcastStatistics() {
        if (sessions.isEmpty()) {
            return;
        }
        
        try {
            ObjectNode statistics = generateStatistics();
            String message = objectMapper.writeValueAsString(statistics);
            
            synchronized (sessions) {
                sessions.removeIf(session -> {
                    try {
                        if (session.isOpen()) {
                            session.getBasicRemote().sendText(message);
                            return false;
                        } else {
                            return true;
                        }
                    } catch (IOException e) {
                        LOGGER.warning("发送消息到会话失败: " + e.getMessage());
                        return true;
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.severe("生成统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 向指定会话发送统计信息
     */
    private void sendStatistics(Session session) throws IOException {
        if (session.isOpen()) {
            ObjectNode statistics = generateStatistics();
            String message = objectMapper.writeValueAsString(statistics);
            session.getBasicRemote().sendText(message);
        }
    }
    
    /**
     * 生成模拟的统计信息
     */
    private static ObjectNode generateStatistics() {
        ObjectNode stats = objectMapper.createObjectNode();
        
        // 基本统计信息
        stats.put("timestamp", System.currentTimeMillis());
        stats.put("activeConnections", sessions.size());
        stats.put("serverStatus", "RUNNING");
        
        // 排班统计信息
        ObjectNode scheduling = objectMapper.createObjectNode();
        scheduling.put("totalStudents", 25);
        scheduling.put("assignedStudents", (int)(Math.random() * 25) + 20);
        scheduling.put("totalTeachers", 15);
        scheduling.put("activeTeachers", (int)(Math.random() * 15) + 10);
        scheduling.put("successRate", Math.round((Math.random() * 20 + 80) * 100) / 100.0);
        scheduling.put("averageWorkload", Math.round((Math.random() * 10 + 15) * 100) / 100.0);
        
        stats.set("scheduling", scheduling);
        
        // 约束统计信息
        ObjectNode constraints = objectMapper.createObjectNode();
        constraints.put("hardConstraintViolations", (int)(Math.random() * 3));
        constraints.put("softConstraintViolations", (int)(Math.random() * 10) + 5);
        constraints.put("totalConstraints", 18);
        constraints.put("satisfiedConstraints", 18 - (int)(Math.random() * 3));
        
        stats.set("constraints", constraints);
        
        // 性能统计信息
        ObjectNode performance = objectMapper.createObjectNode();
        performance.put("lastSolveTime", (int)(Math.random() * 5000) + 1000);
        performance.put("averageSolveTime", (int)(Math.random() * 3000) + 2000);
        performance.put("memoryUsage", Math.round((Math.random() * 30 + 40) * 100) / 100.0);
        performance.put("cpuUsage", Math.round((Math.random() * 20 + 30) * 100) / 100.0);
        
        stats.set("performance", performance);
        
        return stats;
    }
    
    /**
     * 手动触发统计信息广播（供其他服务调用）
     */
    public static void triggerStatisticsBroadcast() {
        broadcastStatistics();
    }
    
    /**
     * 获取当前连接数
     */
    public static int getActiveConnections() {
        return sessions.size();
    }
}