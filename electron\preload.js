const { contextBridge, ipc<PERSON>enderer } = require('electron')

/**
 * 安全的API接口
 * 通过contextBridge暴露给渲染进程
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // ====== 文件操作 ======
  
  /**
   * 保存备份文件
   */
  saveBackup: async (filename, data) => {
    return await ipcRenderer.invoke('save-backup', filename, data)
  },

  /**
   * 加载备份文件
   */
  loadBackup: async () => {
    return await ipcRenderer.invoke('load-backup')
  },

  /**
   * 导出Excel文件
   */
  exportExcel: async (filename, data) => {
    return await ipcRenderer.invoke('export-excel', filename, data)
  },

  // ====== 应用信息 ======
  
  /**
   * 获取应用信息
   */
  getAppInfo: async () => {
    return await ipcRenderer.invoke('get-app-info')
  },

  /**
   * 显示消息框
   */
  showMessageBox: async (options) => {
    return await ipcRenderer.invoke('show-message-box', options)
  },

  // ====== 窗口控制 ======
  
  /**
   * 最小化窗口
   */
  minimizeWindow: () => {
    ipcRenderer.invoke('minimize-window')
  },

  /**
   * 最大化/还原窗口
   */
  toggleMaximize: () => {
    ipcRenderer.invoke('toggle-maximize')
  },

  /**
   * 关闭窗口
   */
  closeWindow: () => {
    ipcRenderer.invoke('close-window')
  },

  /**
   * 重启应用
   */
  restartApp: () => {
    ipcRenderer.invoke('restart-app')
  },

  // ====== 环境检测 ======
  
  /**
   * 检测是否为Electron环境
   */
  isElectron: true,

  /**
   * 获取平台信息
   */
  platform: process.platform,

  /**
   * 获取架构信息
   */
  arch: process.arch,

  // ====== 实用工具 ======
  
  /**
   * 打开外部链接
   */
  openExternal: (url) => {
    ipcRenderer.invoke('open-external', url)
  },

  /**
   * 显示通知
   */
  showNotification: (title, body, options = {}) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, { body, ...options })
    }
    return null
  },

  /**
   * 请求通知权限
   */
  requestNotificationPermission: async () => {
    if ('Notification' in window) {
      return await Notification.requestPermission()
    }
    return 'denied'
  }
})

/**
 * 暴露Node.js版本信息（只读）
 */
contextBridge.exposeInMainWorld('nodeVersions', {
  node: process.versions.node,
  chrome: process.versions.chrome,
  electron: process.versions.electron,
  v8: process.versions.v8
})

/**
 * 环境变量（只读，安全的）
 */
contextBridge.exposeInMainWorld('env', {
  NODE_ENV: process.env.NODE_ENV || 'production',
  platform: process.platform,
  arch: process.arch
})

// ====== 初始化日志 ======

console.log('🚀 Electron预加载脚本已加载')
console.log('📊 环境信息:', {
  platform: process.platform,
  arch: process.arch,
  electron: process.versions.electron,
  node: process.versions.node
})
