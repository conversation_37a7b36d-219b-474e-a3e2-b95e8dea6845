@echo off
chcp 65001 >nul
echo ========================================
echo    启动开发环境 - 教员排班系统
echo ========================================
echo.
echo 正在启动所有服务...
echo.

REM 启动Node.js数据服务
echo [1/3] 启动 Node.js 数据服务 (端口 3003)...
start "Node.js数据服务" cmd /k "node server.cjs"
timeout /t 2 /nobreak >nul

REM 启动OptaPlanner排班服务
echo [2/3] 启动 OptaPlanner 排班服务 (端口 8081)...
start "OptaPlanner排班服务" cmd /k "chcp 65001 >nul && set MAVEN_OPTS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dstdout.encoding=UTF-8 && cd optaplanner-service && mvn quarkus:dev -Dquarkus.log.console.encoding=UTF-8"
timeout /t 5 /nobreak >nul

REM 启动Electron应用
echo [3/3] 启动 Electron 应用...
echo.
echo ========================================
echo 提示：
echo - Node.js服务: http://localhost:3003
echo - OptaPlanner服务: http://localhost:8081
echo - Vite开发服务器: http://localhost:5173
echo.
echo 所有服务将在独立的窗口中运行
echo 关闭窗口即可停止对应服务
echo ========================================
echo.

npm run electron:dev

