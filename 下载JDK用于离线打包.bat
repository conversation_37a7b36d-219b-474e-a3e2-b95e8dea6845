@echo off
chcp 65001 >nul
cls
echo.
echo ================================================================
echo            下载JDK 17 用于离线打包
echo ================================================================
echo.
echo 要创建完全独立、无需网络的应用，需要将JDK打包进EXE
echo.
echo ================================================================
echo.
echo 📋 下载选项:
echo.
echo 方式1: JDK 17（推荐）
echo    - 包含完整Java开发工具包
echo    - 支持OptaPlanner的所有功能（包括动态编译）
echo    - 大小: 约 180-200 MB
echo    - 下载地址: https://adoptium.net/temurin/releases/
echo      选择: JDK 17 - Windows x64 - ZIP 格式
echo.
echo 方式2: JRE 17（体积更小）
echo    - 仅包含Java运行时
echo    - 可能不支持OptaPlanner的某些高级功能
echo    - 大小: 约 80-100 MB
echo    - 下载地址: 同上，选择JRE而不是JDK
echo.
echo ================================================================
echo.
echo 📥 下载步骤:
echo.
echo 1. 访问: https://adoptium.net/temurin/releases/
echo 2. 选择:
echo    - Version: 17 (LTS)
echo    - Operating System: Windows
echo    - Architecture: x64
echo    - Package Type: JDK 或 JRE
echo    - Archive: .zip
echo 3. 点击下载
echo.
echo ================================================================
echo.
pause
echo.
echo 正在打开下载页面...
start https://adoptium.net/temurin/releases/
echo.
echo ================================================================
echo.
echo 📦 下载完成后的操作:
echo.
echo 1. 解压下载的ZIP文件
echo 2. 将解压后的文件夹重命名为: jdk (如果下载的是JDK)
echo                           或: jre (如果下载的是JRE)
echo 3. 移动到项目根目录
echo.
echo 最终目录结构应该是:
echo    E:\Project\examiner-assignment-system\jdk\bin\java.exe
echo 或: E:\Project\examiner-assignment-system\jre\bin\java.exe
echo.
echo ================================================================
echo.
echo ✅ 完成后，运行 "完整打包-管理员权限.bat" 进行打包
echo.
echo ================================================================
echo.

REM 检查是否已存在
if exist "jdk\bin\java.exe" (
    echo.
    echo ⚠️  注意: 已存在 jdk 目录
    echo.
    jdk\bin\java.exe -version
    echo.
    choice /C YN /M "是否替换现有的JDK"
    if errorlevel 2 (
        echo 保留现有JDK
        pause
        exit /b 0
    )
)

if exist "jre\bin\java.exe" (
    echo.
    echo ⚠️  注意: 已存在 jre 目录
    echo.
    jre\bin\java.exe -version
    echo.
    choice /C YN /M "是否替换现有的JRE"
    if errorlevel 2 (
        echo 保留现有JRE
        pause
        exit /b 0
    )
)

echo.
echo 等待手动完成下载和解压...
echo.
pause

REM 验证安装
:verify
cls
echo.
echo ================================================================
echo            验证JDK/JRE安装
echo ================================================================
echo.

if exist "jdk\bin\java.exe" (
    echo ✅ 找到JDK
    echo.
    echo 版本信息:
    jdk\bin\java.exe -version
    echo.
    echo ✅ JDK已正确安装！
    echo.
    echo 现在可以运行 "完整打包-管理员权限.bat" 进行打包
    echo.
) else if exist "jre\bin\java.exe" (
    echo ✅ 找到JRE
    echo.
    echo 版本信息:
    jre\bin\java.exe -version
    echo.
    echo ✅ JRE已正确安装！
    echo.
    echo ⚠️  注意: 使用JRE可能无法支持OptaPlanner的动态编译功能
    echo    如遇问题，建议改用JDK
    echo.
    echo 现在可以运行 "完整打包-管理员权限.bat" 进行打包
    echo.
) else (
    echo ❌ 未找到JDK或JRE
    echo.
    echo 请确保:
    echo    1. 已下载JDK/JRE的ZIP文件
    echo    2. 已解压
    echo    3. 已重命名为 jdk 或 jre
    echo    4. 已移动到项目根目录
    echo.
    echo 当前项目目录: %CD%
    echo.
    choice /C YN /M "是否重新检查"
    if errorlevel 2 (
        echo.
        echo 请完成安装后再运行此脚本
        pause
        exit /b 1
    )
    goto verify
)

echo ================================================================
echo.
pause
