{"name": "examiner-assignment-system", "version": "1.0.0", "description": "智能考官排班系统", "main": "electron/main.js", "homepage": "./", "author": "Your Name", "license": "MIT", "files": ["dist/**/*", "electron/**/*", "package.json"], "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "server": "node server.cjs", "server:dev": "node server.cjs", "electron:dev": "concurrently \"npm run dev\" \"wait-on tcp:5173 && cross-env NODE_ENV=development electron .\"", "electron:build": "npm run build && cross-env NODE_ENV=production electron-builder", "electron:pack": "npm run build && cross-env NODE_ENV=production node build-electron.js", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@rollup/rollup-win32-x64-msvc": "^4.52.0", "@tailwindcss/typography": "^0.5.18", "@types/jszip": "^3.4.0", "autoprefixer": "^10.4.21", "axios": "^1.12.2", "chart.js": "^4.5.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dayjs": "^1.11.0", "echarts": "^5.6.0", "element-plus": "^2.11.2", "express": "^4.18.0", "http-proxy-middleware": "^3.0.5", "jszip": "^3.10.1", "lucide-vue-next": "^0.400.0", "pinia": "^2.1.0", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "vue": "^3.4.0", "vue-echarts": "^6.6.0", "vue-router": "^4.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.5.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^27.0.0", "electron-builder": "^24.6.4", "ts-node": "^10.9.2", "typescript": "^5.2.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0", "wait-on": "^7.2.0"}, "build": {"appId": "com.example.examiner-assignment-system", "productName": "考官排班系统", "directories": {"output": "build-electron-output"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "optaplanner-service/target/quarkus-app", "to": "backend", "filter": ["**/*"]}, {"from": "jdk", "to": "jdk", "filter": ["**/*"]}, {"from": "jre", "to": "jre", "filter": ["**/*"]}, {"from": "public", "to": "public"}, {"from": "dist", "to": "dist", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/icon.png"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/icon.png"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "public/icon.png"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "考官排班系统"}}}