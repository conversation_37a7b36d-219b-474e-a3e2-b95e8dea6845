@echo off
chcp 65001 >nul

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 已获得管理员权限，继续执行...
    goto :main
) else (
    echo.
    echo ================================================================
    echo                需要管理员权限
    echo ================================================================
    echo.
    echo 打包过程需要管理员权限来创建符号链接
    echo 正在请求管理员权限...
    echo.
    
    REM 以管理员权限重新运行
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

:main
cls
echo.
echo ================================================================
echo        考官排班系统 - 完整打包（含离线运行环境）
echo ================================================================
echo.
echo 此脚本将：
echo   1. 清理缓存（解决权限问题）
echo   2. 编译后端服务
echo   3. 构建前端应用
echo   4. 打包完整的独立EXE（包含Java运行时）
echo.
echo ================================================================
echo.
pause

REM 切换到项目目录
cd /d "%~dp0"
echo 当前目录: %CD%
echo.

REM 步骤0: 清理electron-builder缓存（解决符号链接权限问题）
echo ========================================
echo 步骤 0/4: 清理缓存
echo ========================================
echo.
set CACHE_DIR=%LOCALAPPDATA%\electron-builder\Cache\winCodeSign
if exist "%CACHE_DIR%" (
    echo 正在清理缓存目录: %CACHE_DIR%
    rd /s /q "%CACHE_DIR%" 2>nul
    echo ✅ 缓存已清理
) else (
    echo ✅ 缓存目录不存在，跳过
)
echo.

REM 步骤1: 检查并编译后端
echo ========================================
echo 步骤 1/4: 编译Java后端
echo ========================================
echo.

if exist "optaplanner-service\target\quarkus-app\quarkus-run.jar" (
    choice /C YN /M "后端已编译，是否重新编译"
    if errorlevel 2 (
        echo ✅ 跳过后端编译
        goto frontend
    )
)

echo 正在编译后端...
cd optaplanner-service
call mvn clean package -DskipTests -Dquarkus.log.console.encoding=UTF-8
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 后端编译失败！
    cd ..
    pause
    exit /b 1
)
cd ..
echo.
echo ✅ 后端编译成功
echo.

:frontend
REM 步骤2: 构建前端
echo ========================================
echo 步骤 2/4: 构建Vue前端
echo ========================================
echo.

if exist "dist\index.html" (
    choice /C YN /M "前端已构建，是否重新构建"
    if errorlevel 2 (
        echo ✅ 跳过前端构建
        goto check_jre
    )
)

echo 正在构建前端...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 前端构建失败！
    pause
    exit /b 1
)
echo.
echo ✅ 前端构建成功
echo.

:check_jre
REM 步骤3: 检查JRE/JDK（用于离线运行）
echo ========================================
echo 步骤 3/4: 检查Java运行时环境
echo ========================================
echo.

if exist "jdk\bin\java.exe" (
    echo ✅ 找到JDK: jdk\bin\java.exe
    echo    将打包完整JDK，支持离线运行
    set JAVA_FOUND=1
) else if exist "jre\bin\java.exe" (
    echo ✅ 找到JRE: jre\bin\java.exe
    echo    将打包JRE，支持离线运行
    set JAVA_FOUND=1
) else (
    echo.
    echo ⚠️  警告: 未找到JRE/JDK目录！
    echo.
    echo 打包的应用将依赖用户系统的Java环境。
    echo.
    echo 要创建完全独立的应用，请执行以下步骤：
    echo.
    echo 方式1: 使用JDK（推荐，支持OptaPlanner完整功能）
    echo   1. 下载 JDK 17: https://adoptium.net/temurin/releases/
    echo   2. 解压到项目根目录，重命名为 jdk
    echo   3. 确保存在: jdk\bin\java.exe
    echo.
    echo 方式2: 使用JRE（体积更小）
    echo   1. 下载 JRE 17
    echo   2. 解压到项目根目录，重命名为 jre
    echo   3. 确保存在: jre\bin\java.exe
    echo.
    choice /C YN /M "是否继续打包（不包含Java环境）"
    if errorlevel 2 (
        echo.
        echo 已取消打包
        pause
        exit /b 0
    )
    set JAVA_FOUND=0
)
echo.

REM 步骤4: Electron打包
echo ========================================
echo 步骤 4/4: 打包Electron应用
echo ========================================
echo.
echo ⏳ 打包过程需要 5-10 分钟，请耐心等待...
echo.
echo    正在执行的操作：
echo    - 打包应用代码
echo    - 复制后端服务
if %JAVA_FOUND%==1 (
    echo    - 打包Java运行时（JRE/JDK）
)
echo    - 创建安装程序
echo    - 生成便携版
echo.

REM 设置环境变量
set CSC_IDENTITY_AUTO_DISCOVERY=false
set NODE_ENV=production

REM 执行打包
call node_modules\.bin\electron-builder.cmd --config electron-builder.yml --win --x64

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ================================================================
    echo                    ❌ 打包失败
    echo ================================================================
    echo.
    echo 错误代码: %ERRORLEVEL%
    echo.
    echo 常见问题排查:
    echo   1. 网络问题 - 需要下载打包工具
    echo   2. 磁盘空间不足
    echo   3. 权限不足（即使使用管理员权限）
    echo.
    echo 如果仍然失败，请手动清理缓存后重试:
    echo   del /f /s /q "%LOCALAPPDATA%\electron-builder\Cache\*"
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================================
echo                    ✅ 打包成功！
echo ================================================================
echo.

REM 显示输出文件信息
echo 📦 生成的文件:
echo.

if exist "build-electron-output\win-unpacked\考官排班系统.exe" (
    for %%I in ("build-electron-output\win-unpacked\考官排班系统.exe") do (
        set /a SIZE_MB=%%~zI/1048576
    )
    echo    ✅ 便携版EXE
    echo       位置: build-electron-output\win-unpacked\考官排班系统.exe
    echo       大小: 约 !SIZE_MB! MB
    echo       说明: 无需安装，直接运行
    echo.
)

if exist "build-electron-output\考官排班系统-1.0.0-Setup.exe" (
    for %%I in ("build-electron-output\考官排班系统-1.0.0-Setup.exe") do (
        set /a SIZE_MB=%%~zI/1048576
    )
    echo    ✅ 安装程序
    echo       位置: build-electron-output\考官排班系统-1.0.0-Setup.exe
    echo       大小: 约 !SIZE_MB! MB
    echo       说明: 标准安装向导
    echo.
)

echo ================================================================
echo.
echo 📋 离线运行支持:
if %JAVA_FOUND%==1 (
    echo    ✅ 已打包Java运行时，支持完全离线运行
    echo    ✅ 用户无需安装任何依赖
) else (
    echo    ⚠️  未打包Java运行时
    echo    ⚠️  用户需要安装JDK 17或更高版本
)
echo.
echo ================================================================
echo.

REM 询问是否测试运行
choice /C YN /M "是否现在测试运行"
if errorlevel 2 goto end

echo.
echo 正在启动应用（首次启动需要10-20秒初始化）...
start "" "build-electron-output\win-unpacked\考官排班系统.exe"

:end
echo.
echo 完成！按任意键退出...
pause >nul
