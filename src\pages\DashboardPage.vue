<template>
  <div class="dashboard-page">
    <!-- 今日概览 -->
    <div class="today-overview">
      <div class="overview-header">
        <h2 class="overview-title">今日排班概览</h2>
        <div class="date-info">
          <Calendar class="w-5 h-5 text-gray-500" />
          <span class="current-date">{{ currentDate }}</span>
        </div>
      </div>
      
      <div class="overview-stats">
        <div class="stat-item">
          <div class="stat-value text-green-600">{{ todayStats.assignedTeachers }}</div>
          <div class="stat-label">已安排考官</div>
        </div>
        <div class="stat-item">
          <div class="stat-value text-orange-600">{{ todayStats.pendingAssignments }}</div>
          <div class="stat-label">待安排</div>
        </div>
      </div>
    </div>

    <!-- 时间轴视图 -->
    <div class="timeline-section">
      <div class="section-header">
        <h3 class="section-title">今日时间安排</h3>
        <div class="view-controls">
          <button 
            v-for="view in viewOptions" 
            :key="view.value"
            @click="currentView = view.value"
            :class="['view-btn', { active: currentView === view.value }]"
          >
            {{ view.label }}
          </button>
        </div>
      </div>
      
      <div class="timeline-container">
        <div class="time-slots">
          <div 
            v-for="slot in timeSlots" 
            :key="slot.time"
            class="time-slot"
          >
            <div class="time-label">{{ slot.time }}</div>
            <div class="slot-content">
              <div 
                v-for="exam in slot.exams" 
                :key="exam.id"
                class="exam-card"
                :class="exam.status"
              >
                <div class="exam-header">
                  <h4 class="exam-title">{{ exam.subject }}</h4>
                  <span class="exam-room">{{ exam.room }}</span>
                </div>
                <div class="exam-details">
                  <div class="exam-info">
                    <Users class="w-4 h-4" />
                    <span>{{ exam.studentCount }}人</span>
                  </div>
                  <div class="exam-duration">
                    <Clock class="w-4 h-4" />
                    <span>{{ exam.duration }}</span>
                  </div>
                </div>
                <div class="assigned-teachers">
                  <div class="teacher-list">
                    <div 
                      v-for="teacher in exam.teachers" 
                      :key="teacher.id"
                      class="teacher-tag"
                    >
                      {{ teacher.name }}
                    </div>
                  </div>
                  <button 
                    v-if="exam.needsMoreTeachers"
                    class="add-teacher-btn"
                    @click="assignTeacher(exam.id)"
                  >
                    <Plus class="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div v-if="slot.exams.length === 0" class="empty-slot">
                <span class="empty-text">暂无安排</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 考官分布情况 -->
    <TeacherDistribution />

    <!-- 考官状态 -->
    <div class="teachers-status-section">
      <div class="section-header">
        <h3 class="section-title">考官状态</h3>
        <button class="refresh-btn" @click="refreshTeacherStatus">
          <RefreshCw class="w-4 h-4" />
          <span>刷新</span>
        </button>
      </div>
      
      <div class="teachers-grid">
        <div 
          v-for="teacher in teacherStatus" 
          :key="teacher.id"
          class="teacher-card"
          :class="teacher.status"
        >
          <div class="teacher-avatar">
            <User class="w-6 h-6" />
          </div>
          <div class="teacher-info">
            <h4 class="teacher-name">{{ teacher.name }}</h4>
            <p class="teacher-department">{{ teacher.department }}</p>
          </div>
          <div class="teacher-schedule">
            <div class="schedule-count">
              <span class="count">{{ teacher.todaySchedules }}</span>
              <span class="label">今日排班</span>
            </div>
            <div class="status-indicator" :class="teacher.status">
              <div class="status-dot"></div>
              <span class="status-text">{{ getStatusText(teacher.status) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Calendar, Users, Clock, Plus, RefreshCw, User } from 'lucide-vue-next'
import TeacherDistribution from '../components/TeacherDistribution.vue'
import { useResponsive } from '../composables/useResponsive'

interface Exam {
  id: string
  subject: string
  room: string
  studentCount: number
  duration: string
  teachers: { id: string; name: string }[]
  needsMoreTeachers: boolean
  status: 'scheduled' | 'in-progress' | 'completed' | 'pending'
}

interface TimeSlot {
  time: string
  exams: Exam[]
}

interface Teacher {
  id: string
  name: string
  department: string
  todaySchedules: number
  status: 'available' | 'busy' | 'unavailable'
}

// 使用响应式功能
const { isMobile, isTablet, gridCols } = useResponsive()

const currentView = ref('timeline')
const viewOptions = [
  { value: 'timeline', label: '时间轴' },
  { value: 'grid', label: '网格' }
]

const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

const todayStats = ref({
  assignedTeachers: 15,
  pendingAssignments: 2
})

const timeSlots = ref<TimeSlot[]>([
  {
    time: '08:00',
    exams: [
      {
        id: '1',
        subject: '高等数学',
        room: 'A101',
        studentCount: 120,
        duration: '2小时',
        teachers: [{ id: '1', name: '张教授' }, { id: '2', name: '李老师' }],
        needsMoreTeachers: false,
        status: 'scheduled'
      }
    ]
  },
  {
    time: '10:00',
    exams: [
      {
        id: '2',
        subject: '英语四级',
        room: 'B201-B205',
        studentCount: 300,
        duration: '2.5小时',
        teachers: [{ id: '3', name: '王老师' }],
        needsMoreTeachers: true,
        status: 'pending'
      }
    ]
  },
  {
    time: '14:00',
    exams: []
  },
  {
    time: '16:00',
    exams: [
      {
        id: '3',
        subject: '计算机基础',
        room: 'C301',
        studentCount: 80,
        duration: '1.5小时',
        teachers: [{ id: '4', name: '赵老师' }],
        needsMoreTeachers: false,
        status: 'scheduled'
      }
    ]
  }
])

const teacherStatus = ref<Teacher[]>([
  { id: '1', name: '张教授', department: '数学系', todaySchedules: 2, status: 'busy' },
  { id: '2', name: '李老师', department: '数学系', todaySchedules: 1, status: 'available' },
  { id: '3', name: '王老师', department: '外语系', todaySchedules: 3, status: 'busy' },
  { id: '4', name: '赵老师', department: '计算机系', todaySchedules: 1, status: 'available' },
  { id: '5', name: '陈老师', department: '物理系', todaySchedules: 0, status: 'unavailable' },
  { id: '6', name: '刘老师', department: '化学系', todaySchedules: 2, status: 'available' }
])

const getStatusText = (status: string) => {
  switch (status) {
    case 'available': return '空闲'
    case 'busy': return '忙碌'
    case 'unavailable': return '不可用'
    default: return '未知'
  }
}

const assignTeacher = (examId: string) => {
  console.log('为考试分配考官:', examId)
  // 这里可以打开分配考官的对话框
}

const refreshTeacherStatus = () => {
  console.log('刷新考官状态')
  // 重新获取考官状态数据
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.dashboard-page {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  max-width: 100%;
}

.today-overview {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.overview-header {
  @apply flex items-center justify-between mb-4 flex-wrap gap-2;
}

.overview-title {
  @apply text-xl font-semibold text-gray-900;
}

.date-info {
  @apply flex items-center space-x-2 text-gray-600;
}

.current-date {
  @apply font-medium;
}

.overview-stats {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6;
}

.stat-item {
  @apply text-center p-4 rounded-lg bg-gray-50;
}

.stat-value {
  @apply text-2xl md:text-3xl font-bold;
}

.stat-label {
  @apply text-xs md:text-sm text-gray-600 mt-1;
}

.timeline-section {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.section-header {
  @apply flex items-center justify-between mb-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900;
}

.view-controls {
  @apply flex space-x-2;
}

.view-btn {
  @apply px-3 py-1 text-sm rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors;
}

.view-btn.active {
  @apply bg-blue-600 text-white border-blue-600;
}

.timeline-container {
  @apply space-y-4;
}

.time-slot {
  @apply flex;
}

.time-label {
  @apply w-20 text-sm font-medium text-gray-600 pt-2;
}

.slot-content {
  @apply flex-1 ml-4;
}

.exam-card {
  @apply bg-gray-50 rounded-lg p-4 mb-3 border-l-4;
}

.exam-card.scheduled {
  @apply border-l-blue-500 bg-blue-50;
}

.exam-card.pending {
  @apply border-l-orange-500 bg-orange-50;
}

.exam-card.in-progress {
  @apply border-l-green-500 bg-green-50;
}

.exam-card.completed {
  @apply border-l-gray-500 bg-gray-50;
}

.exam-header {
  @apply flex items-center justify-between mb-2;
}

.exam-title {
  @apply font-semibold text-gray-900;
}

.exam-room {
  @apply text-sm text-gray-600 bg-white px-2 py-1 rounded;
}

.exam-details {
  @apply flex items-center space-x-4 mb-3 text-sm text-gray-600;
}

.exam-info, .exam-duration {
  @apply flex items-center space-x-1;
}

.assigned-teachers {
  @apply flex items-center justify-between;
}

.teacher-list {
  @apply flex flex-wrap gap-2;
}

.teacher-tag {
  @apply bg-white px-2 py-1 rounded text-xs font-medium text-gray-700 border;
}

.add-teacher-btn {
  @apply p-1 text-orange-600 hover:bg-orange-100 rounded transition-colors;
}

.empty-slot {
  @apply text-center py-8 text-gray-400;
}

.teachers-status-section {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.refresh-btn {
  @apply flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors;
}

.teachers-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4;
}

.teacher-card {
  @apply bg-gray-50 rounded-lg p-3 md:p-4 flex items-center space-x-2 md:space-x-3 transition-all;
}

.teacher-card.available {
  @apply bg-green-50 border border-green-200;
}

.teacher-card.busy {
  @apply bg-yellow-50 border border-yellow-200;
}

.teacher-card.unavailable {
  @apply bg-red-50 border border-red-200;
}

.teacher-avatar {
  @apply w-10 h-10 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center text-gray-600 flex-shrink-0;
}

.teacher-info {
  @apply flex-1 min-w-0;
}

.teacher-name {
  @apply font-medium text-gray-900 text-sm md:text-base truncate;
}

.teacher-department {
  @apply text-xs md:text-sm text-gray-600 truncate;
}

.teacher-schedule {
  @apply text-right flex-shrink-0;
}

.schedule-count {
  @apply mb-1;
}

.count {
  @apply text-base md:text-lg font-bold text-gray-900;
}

.label {
  @apply text-xs text-gray-600 ml-1;
}

.status-indicator {
  @apply flex items-center space-x-1 justify-end;
}

.status-dot {
  @apply w-2 h-2 rounded-full flex-shrink-0;
}

.status-indicator.available .status-dot {
  @apply bg-green-500;
}

.status-indicator.busy .status-dot {
  @apply bg-yellow-500;
}

.status-indicator.unavailable .status-dot {
  @apply bg-red-500;
}

.status-text {
  @apply text-xs font-medium;
}

/* 响应式优化 - 移动端 */
@media (max-width: 767px) {
  .dashboard-page {
    gap: 1rem;
  }
  
  .today-overview {
    @apply p-4;
  }
  
  .overview-title {
    @apply text-lg;
  }
  
  .date-info {
    @apply text-sm;
  }
  
  .overview-stats {
    @apply gap-3 grid-cols-2;
  }
  
  .stat-item {
    @apply p-3;
  }
  
  .stat-value {
    @apply text-xl;
  }
  
  .timeline-section {
    @apply p-4;
  }
  
  .section-title {
    @apply text-base;
  }
  
  .view-controls {
    @apply flex-wrap;
  }
  
  .view-btn {
    @apply px-2 py-1 text-xs;
  }
  
  .time-slot {
    @apply flex-col items-start;
  }
  
  .time-label {
    @apply w-full mb-2 text-xs;
  }
  
  .slot-content {
    @apply ml-0 w-full;
  }
  
  .exam-card {
    @apply p-3;
  }
  
  .exam-title {
    @apply text-sm;
  }
  
  .exam-room {
    @apply text-xs;
  }
  
  .exam-details {
    @apply text-xs space-x-2;
  }
  
  .teacher-tag {
    @apply text-xs;
  }
  
  .teachers-status-section {
    @apply p-4;
  }
  
  .teachers-grid {
    @apply gap-2 grid-cols-1;
  }
  
  .refresh-btn {
    @apply px-2 py-1 text-xs;
  }
}

/* 响应式优化 - 平板端 */
@media (min-width: 768px) and (max-width: 1023px) {
  .overview-stats {
    @apply grid-cols-3;
  }
  
  .teachers-grid {
    @apply grid-cols-2;
  }
}

/* 响应式优化 - 大屏幕 */
@media (min-width: 1536px) {
  .overview-stats {
    @apply gap-8;
  }
  
  .stat-value {
    @apply text-4xl;
  }
  
  .teachers-grid {
    @apply grid-cols-4;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .view-btn,
  .add-teacher-btn,
  .refresh-btn {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .teacher-card,
  .exam-card {
    transition: none;
  }
}
</style>