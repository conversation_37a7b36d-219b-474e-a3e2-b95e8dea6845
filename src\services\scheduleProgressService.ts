/**
 * 排班进度实时推送服务
 * 通过WebSocket接收后端的分级求解进度
 */

export interface ProgressUpdate {
  currentLevel: number
  levelName: string
  elapsedTime: number
  estimatedRemaining: number
  progressPercentage: number
  currentScore: string
  iterationCount: number
}

export interface IntermediateResult {
  score: string
  assignmentCount: number
  confidence: number
  quality: string
  elapsedTime: number
}

export interface ScoreUpdate {
  oldScore: string
  newScore: string
  improvementAmount: number
  elapsedTime: number
}

export interface LevelUpgrade {
  fromLevel: number
  toLevel: number
  fromLevelName: string
  toLevelName: string
  reason: string
  previousScore: string
}

export interface FinalResult {
  success: boolean
  level: string
  score: string
  quality: string
  totalTime: number
  message: string
}

export interface ProgressMessage {
  type: 'connected' | 'progress' | 'intermediate_result' | 'score_improvement' | 'level_upgrade' | 'final_result' | 'error'
  message: string
  data: ProgressUpdate | IntermediateResult | ScoreUpdate | LevelUpgrade | FinalResult | null
  timestamp: number
}

export type ProgressCallback = (message: ProgressMessage) => void

class ScheduleProgressService {
  private ws: WebSocket | null = null
  private callbacks: ProgressCallback[] = []
  private reconnectAttempts = 0
  private maxReconnectAttempts = 3
  private reconnectDelay = 1000

  connect(sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const isHttps = window.location.protocol === 'https:'
        const protocol = isHttps ? 'wss:' : 'ws:'
        let wsUrl: string

        // Electron 打包/本地运行时，直接连后端 8081
        const isElectron = (window as any).electronAPI && (window as any).electronAPI.isElectron
        if (isElectron) {
          wsUrl = `${protocol}//localhost:8081/ws/schedule-progress/${sessionId}`
        } else {
          // 浏览器开发/生产环境：走同源 + Vite 代理 /ws → 8081
          wsUrl = `${protocol}//${window.location.host}/ws/schedule-progress/${sessionId}`
        }
        
        console.log('🔌 [WebSocket] 正在连接:', wsUrl)
        
        this.ws = new WebSocket(wsUrl)
        
        this.ws.onopen = () => {
          console.log('✅ [WebSocket] 连接成功')
          this.reconnectAttempts = 0
          resolve()
        }
        
        this.ws.onmessage = (event) => {
          try {
            const message: ProgressMessage = JSON.parse(event.data)
            console.log('📨 [WebSocket] 收到消息:', message.type, message)
            
            this.callbacks.forEach(callback => {
              try {
                callback(message)
              } catch (error) {
                console.error('❌ [WebSocket] 回调处理失败:', error)
              }
            })
          } catch (error) {
            console.error('❌ [WebSocket] 消息解析失败:', error)
          }
        }
        
        this.ws.onerror = (error) => {
          console.error('❌ [WebSocket] 连接错误:', error)
          reject(error)
        }
        
        this.ws.onclose = (event) => {
          console.log('🔌 [WebSocket] 连接关闭:', event.code, event.reason)
          
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++
            console.log(`🔄 [WebSocket] 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
            
            setTimeout(() => {
              this.connect(sessionId).catch(console.error)
            }, this.reconnectDelay * this.reconnectAttempts)
          }
        }
        
      } catch (error) {
        console.error('❌ [WebSocket] 创建连接失败:', error)
        reject(error)
      }
    })
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client closed connection')
      this.ws = null
    }
    this.callbacks = []
    this.reconnectAttempts = 0
  }

  onProgress(callback: ProgressCallback): () => void {
    this.callbacks.push(callback)
    
    return () => {
      const index = this.callbacks.indexOf(callback)
      if (index > -1) {
        this.callbacks.splice(index, 1)
      }
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  getReadyState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED
  }
}

export const scheduleProgressService = new ScheduleProgressService()

// Provide a default export for dynamic import users
export default scheduleProgressService
