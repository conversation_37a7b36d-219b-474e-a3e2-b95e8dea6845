const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 3003;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
const mockTeachers = [
  { id: 1, name: '张老师', department: '计算机系', available: true },
  { id: 2, name: '李老师', department: '数学系', available: true },
  { id: 3, name: '王老师', department: '物理系', available: true },
  { id: 4, name: '赵老师', department: '化学系', available: true },
  { id: 5, name: '刘老师', department: '计算机系', available: true }
];

const mockSchedules = [
  { id: 1, date: '2025-09-15', time: '09:00', teacher: '张老师', subject: '数据结构' },
  { id: 2, date: '2025-09-15', time: '14:00', teacher: '李老师', subject: '高等数学' },
  { id: 3, date: '2025-09-16', time: '09:00', teacher: '王老师', subject: '大学物理' }
];

// 模拟学员数据
const mockStudents = [
  { id: 1, name: '张三', studentId: '2021001', department: '计算机系', group: 'A组', examType: 'day1', status: 'assigned' },
  { id: 2, name: '李四', studentId: '2021002', department: '数学系', group: 'B组', examType: 'day2', status: 'assigned' },
  { id: 3, name: '王五', studentId: '2021003', department: '物理系', group: 'A组', examType: 'day1', status: 'assigned' },
  { id: 4, name: '赵六', studentId: '2021004', department: '化学系', group: 'C组', examType: 'day2', status: 'pending' },
  { id: 5, name: '钱七', studentId: '2021005', department: '计算机系', group: 'B组', examType: 'day1', status: 'assigned' }
];

// 模拟排班历史数据
const mockScheduleHistory = [
  {
    id: 1,
    date: '2025-01-15',
    type: 'day1',
    studentsCount: 25,
    teachersCount: 8,
    status: 'completed',
    createdAt: '2025-01-10T09:00:00Z'
  },
  {
    id: 2,
    date: '2025-01-16',
    type: 'day2',
    studentsCount: 30,
    teachersCount: 10,
    status: 'completed',
    createdAt: '2025-01-11T09:00:00Z'
  },
  {
    id: 3,
    date: '2025-01-20',
    type: 'day1',
    studentsCount: 28,
    teachersCount: 9,
    status: 'in_progress',
    createdAt: '2025-01-18T09:00:00Z'
  }
];

// API 路由
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: '后端服务运行正常' });
});

app.get('/api/teachers', (req, res) => {
  res.json(mockTeachers);
});

app.get('/api/schedules', (req, res) => {
  res.json(mockSchedules);
});

app.post('/api/schedules', (req, res) => {
  const newSchedule = {
    id: mockSchedules.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  mockSchedules.push(newSchedule);
  res.json(newSchedule);
});

// OptaPlanner兼容的排班API端点
app.get('/api/schedule/health', (req, res) => {
  res.json({ 
    status: 'UP',
    message: 'Node.js后端服务运行正常（OptaPlanner服务暂不可用）'
  });
});

// 排班生成函数 - 供两个端点复用
function generateScheduleResponse(req, res) {
  const { students, teachers, startDate, endDate, constraints, solverConfig } = req.body;
  
  console.log('📋 收到排班请求:', {
    studentsCount: students?.length || 0,
    teachersCount: teachers?.length || 0,
    startDate,
    endDate,
    mode: solverConfig?.mode || 'unknown',
    endpoint: req.route.path
  });
  
  // 生成有效的工作日期范围
  const generateWorkingDays = (start, end) => {
    const workingDays = [];
    const startDate = new Date(start);
    const endDate = new Date(end);
    
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      // 排除周末 (0=周日, 6=周六)
      if (date.getDay() !== 0 && date.getDay() !== 6) {
        workingDays.push(date.toISOString().split('T')[0]);
      }
    }
    return workingDays;
  };
  
  const workingDays = generateWorkingDays(startDate, endDate);
  console.log(`📅 生成的工作日: ${workingDays.slice(0, 5).join(', ')}... (共${workingDays.length}天)`);
  
  // 模拟排班结果 - 随机分配到不同的工作日
  const mockAssignments = students?.map((student, index) => {
    // 随机选择一个工作日，确保不是周末
    const randomWorkingDay = workingDays[index % workingDays.length] || workingDays[0];
    
    return {
      id: `assignment_${index + 1}`,
      student: {
        id: student.id,
        name: student.name,
        department: student.department,
        group: student.group
      },
      examType: index % 2 === 0 ? 'day1' : 'day2',
      subjects: ['专业课程'],
      examDate: randomWorkingDay,
      examiner1: teachers?.[0] || { id: '1', name: '张老师', department: '计算机系' },
      examiner2: teachers?.[1] || { id: '2', name: '李老师', department: '数学系' },
      backupExaminer: teachers?.[2] || { id: '3', name: '王老师', department: '物理系' },
      location: '考试室A',
      timeSlot: {
        start: '09:00',
        end: '11:00',
        period: 'morning'
      }
    };
  }) || [];
  
  const response = {
    success: true,
    message: '排班计算完成（使用Node.js模拟结果）',
    assignments: mockAssignments,
    statistics: {
      totalStudents: students?.length || 0,
      assignedStudents: students?.length || 0,
      unassignedStudents: 0,
      totalTeachers: teachers?.length || 0,
      activeTeachers: Math.min(3, teachers?.length || 0),
      averageWorkload: 1.5,
      maxWorkload: 2,
      finalScore: {
        hardScore: 0,
        softScore: -10
      },
      completionPercentage: 100,
      solvingTimeMillis: 1500,
      hardConstraintViolations: 0,
      softConstraintViolations: 2
    },
    conflicts: [],
    warnings: ['这是Node.js后端的模拟结果，OptaPlanner服务暂不可用']
  };
  
  console.log('✅ 返回排班结果:', {
    success: response.success,
    assignmentsCount: response.assignments.length,
    hardScore: response.statistics.finalScore.hardScore
  });
  
  res.json(response);
}

// 两个端点都使用相同的处理逻辑
app.post('/api/schedule/generate', generateScheduleResponse);
app.post('/api/schedule/solve', generateScheduleResponse);

app.get('/api/schedule/constraints', (req, res) => {
  res.json({
    noWeekendExam: true,
    noHolidayExam: true,
    twoWorkdaysComplete: true,
    noDayShiftExaminer: true,
    noStudentGroupDayShift: true,
    examiner1SameDept: true,
    examiner2DiffDept: true,
    backupDiffDept: true,
    backupNotSameAsExaminer2: true,
    threeExaminers: true,
    consecutiveExamWeight: { hardScore: 0, softScore: 40 },
    minimizeIntervalWeight: { hardScore: 0, softScore: 30 },
    maxTwoStudentsPerDayWeight: { hardScore: 0, softScore: 20 },
    priorityNightShiftWeight: { hardScore: 0, softScore: 15 },
    priorityRestGroupWeight: { hardScore: 0, softScore: 25 },
    noGroupAsBackupWeight: { hardScore: 0, softScore: 35 },
    balanceWorkloadWeight: { hardScore: 0, softScore: 45 },
    avoidConsecutiveWorkWeight: { hardScore: 0, softScore: 50 },
    considerTotalWorkTimeWeight: { hardScore: 0, softScore: 30 },
    preferRecommendedExaminer2Weight: { hardScore: 0, softScore: 60 },
    preferRecommendedBackupWeight: { hardScore: 0, softScore: 55 },
    fallbackToExaminer1DeptWeight: { hardScore: 0, softScore: 40 },
    mergeDept37: false
  });
});

// 统计页面API端点
// 获取首页统计数据
app.get('/api/overview/stats', (req, res) => {
  const totalStudents = mockStudents.length;
  const assignedStudents = mockStudents.filter(s => s.status === 'assigned').length;
  const totalTeachers = mockTeachers.length;
  const activeTeachers = mockTeachers.filter(t => t.available).length;
  const completedSchedules = mockScheduleHistory.filter(s => s.status === 'completed').length;
  
  res.json({
    totalStudents,
    assignedStudents,
    unassignedStudents: totalStudents - assignedStudents,
    totalTeachers,
    activeTeachers,
    completedSchedules,
    pendingSchedules: mockScheduleHistory.filter(s => s.status === 'in_progress').length,
    averageWorkload: 2.3,
    completionRate: Math.round((assignedStudents / totalStudents) * 100)
  });
});

// 2. 获取学员数据
app.get('/api/students', (req, res) => {
  const { page = 1, pageSize = 10, department, status } = req.query;
  let filteredStudents = [...mockStudents];
  
  if (department && department !== 'all') {
    filteredStudents = filteredStudents.filter(s => s.department === department);
  }
  
  if (status && status !== 'all') {
    filteredStudents = filteredStudents.filter(s => s.status === status);
  }
  
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedStudents = filteredStudents.slice(startIndex, endIndex);
  
  res.json({
    data: paginatedStudents,
    total: filteredStudents.length,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages: Math.ceil(filteredStudents.length / pageSize)
  });
});

// 数据管理API - 获取所有学员数据（用于排班页面）
app.get('/api/data/students', (req, res) => {
  // 转换为前端期望的格式
  const studentsWithDepartments = mockStudents.map(student => ({
    id: student.id,
    studentId: student.studentId,
    name: student.name,
    department: {
      id: 1,
      code: student.department.slice(0, 1),
      name: student.department
    },
    group: {
      id: 1,
      code: student.group.slice(0, 1),
      name: student.group
    },
    recommendedExaminer1Dept: {
      id: 1,
      code: student.department.slice(0, 1),
      name: student.department
    },
    recommendedExaminer2Dept: {
      id: 2,
      code: '其他',
      name: '其他科室'
    },
    isActive: student.status === 'assigned'
  }));
  
  console.log('📋 返回学员数据:', studentsWithDepartments.length, '名学员');
  res.json(studentsWithDepartments);
});

// 数据管理API - 获取所有教师数据（用于排班页面）
app.get('/api/data/teachers', (req, res) => {
  // 转换为前端期望的格式
  const teachersWithDepartments = mockTeachers.map(teacher => ({
    id: teacher.id,
    teacherId: teacher.id.toString(),
    name: teacher.name,
    department: {
      id: 1,
      code: teacher.department.slice(0, 1),
      name: teacher.department
    },
    group: {
      id: 1,
      code: '一组',
      name: '一组'
    },
    workload: Math.floor(Math.random() * 5) + 1,
    consecutiveDays: Math.floor(Math.random() * 3),
    isActive: teacher.available
  }));
  
  console.log('👥 返回教师数据:', teachersWithDepartments.length, '名教师');
  res.json(teachersWithDepartments);
});

// 3. 获取排班历史数据
app.get('/api/schedules/history', (req, res) => {
  const { page = 1, pageSize = 10, status, startDate, endDate } = req.query;
  let filteredHistory = [...mockScheduleHistory];
  
  if (status && status !== 'all') {
    filteredHistory = filteredHistory.filter(h => h.status === status);
  }
  
  if (startDate) {
    filteredHistory = filteredHistory.filter(h => new Date(h.date) >= new Date(startDate));
  }
  
  if (endDate) {
    filteredHistory = filteredHistory.filter(h => new Date(h.date) <= new Date(endDate));
  }
  
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedHistory = filteredHistory.slice(startIndex, endIndex);
  
  res.json({
    data: paginatedHistory,
    total: filteredHistory.length,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages: Math.ceil(filteredHistory.length / pageSize)
  });
});

// 4. 获取图表数据
app.get('/api/overview/charts/:type', (req, res) => {
  const { type } = req.params;
  
  switch (type) {
    case 'workload':
      // 工作量分布图数据
      res.json({
        labels: mockTeachers.slice(0, 10).map(t => t.name),
        values: mockTeachers.slice(0, 10).map(() => Math.floor(Math.random() * 50) + 10)
      });
      break;
      
    case 'heatmap':
      // 排班热力图数据
      const heatmapData = [];
      const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      const timeSlots = ['8:00', '10:00', '14:00', '16:00', '19:00'];
      
      days.forEach((day, dayIndex) => {
        timeSlots.forEach((time, timeIndex) => {
          heatmapData.push([
            dayIndex,
            timeIndex,
            Math.floor(Math.random() * 100)
          ]);
        });
      });
      
      res.json({
        xAxis: days,
        yAxis: timeSlots,
        data: heatmapData,
        min: 0,
        max: 100
      });
      break;
      
    case 'trend':
      // 工作量趋势图数据
      const dates = [];
      const workloadValues = [];
      
      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
        workloadValues.push(Math.floor(Math.random() * 80) + 20);
      }
      
      res.json({
        dates: dates,
        values: workloadValues
      });
      break;
      
    case 'distribution':
      // 班级分布图数据
      const departments = ['计算机系', '数学系', '物理系', '化学系', '英语系'];
      const distributionData = departments.map(dept => ({
        name: dept,
        value: mockStudents.filter(s => s.department === dept).length || Math.floor(Math.random() * 30) + 5
      }));
      
      res.json({
        data: distributionData
      });
      break;
      
    default:
      res.status(404).json({ error: '未知的图表类型' });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 后端服务器已启动，运行在端口 ${PORT}`);
  console.log(`📋 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`👥 考官列表: http://localhost:${PORT}/api/teachers`);
  console.log(`📅 排班列表: http://localhost:${PORT}/api/schedules`);
  console.log(`🔧 OptaPlanner健康检查: http://localhost:${PORT}/api/schedule/health`);
});