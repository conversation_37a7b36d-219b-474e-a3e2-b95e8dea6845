const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron')
const path = require('path')
const fs = require('fs')
const { spawn } = require('child_process')
const os = require('os')

// 开发环境检查
const isDev = process.env.NODE_ENV === 'development'

let mainWindow
let javaProcess = null

// 🔧 智能检测系统环境并应用合适的GPU配置
function configureGPUSettings() {
  const platform = process.platform
  const osRelease = os.release()
  
  // 解析Windows版本号
  // Windows 7: 6.1.x
  // Windows 10: 10.0.x (build < 22000)
  // Windows 11: 10.0.x (build >= 22000)
  const isWin7 = platform === 'win32' && osRelease.startsWith('6.1')
  const isWin10OrLower = platform === 'win32' && (
    osRelease.startsWith('6.') || 
    (osRelease.startsWith('10.0') && parseInt(osRelease.split('.')[2] || '0') < 22000)
  )
  const isWin11 = platform === 'win32' && osRelease.startsWith('10.0') && 
                  parseInt(osRelease.split('.')[2] || '0') >= 22000
  
  console.log('========================================')
  console.log('系统环境检测:')
  console.log('平台:', platform)
  console.log('系统版本:', osRelease)
  console.log('是否Win7:', isWin7)
  console.log('是否Win10或更低:', isWin10OrLower)
  console.log('是否Win11:', isWin11)
  console.log('========================================')
  
  // 根据环境变量强制使用高性能模式（用于RTX 4090等高端显卡）
  const forceHighPerformanceMode = process.env.GPU_MODE === 'high-performance'
  
  if (forceHighPerformanceMode) {
    console.log('🚀 强制启用高性能GPU模式（适用于RTX 4090等）')
    // 高性能显卡优化：禁用可能导致闪烁的GPU功能
    app.commandLine.appendSwitch('disable-gpu-vsync')
    app.commandLine.appendSwitch('disable-gpu-compositing')
    app.commandLine.appendSwitch('disable-software-rasterizer')
    app.commandLine.appendSwitch('disable-frame-rate-limit')
  } else if (isWin7 || isWin10OrLower) {
    console.log('💻 使用标准GPU模式（Win7/Win10兼容）')
    // Win7/Win10：保持基本的硬件加速，只禁用可能有问题的功能
    app.commandLine.appendSwitch('disable-gpu-sandbox')
    app.commandLine.appendSwitch('disable-software-rasterizer')
    // 启用硬件加速以获得更好的性能
    app.disableHardwareAcceleration = false
  } else {
    console.log('🎯 使用平衡GPU模式（通用配置）')
    // 其他系统：平衡配置
    app.commandLine.appendSwitch('disable-gpu-sandbox')
  }
  
  return {
    isWin7,
    isWin10OrLower,
    isWin11,
    highPerformanceMode: forceHighPerformanceMode
  }
}

// 执行GPU配置并保存结果
const systemConfig = configureGPUSettings()

/**
 * 获取Java后端路径
 */
function getBackendPath() {
  if (isDev) {
    return path.join(__dirname, '../optaplanner-service/target/quarkus-app')
  } else {
    return path.join(process.resourcesPath, 'backend')
  }
}

/**
 * 获取Java可执行文件路径
 * 优先使用打包的JDK，找不到才尝试JRE或系统Java
 */
function getJavaExecutable() {
  // 开发环境使用系统Java
  if (isDev) {
    return process.platform === 'win32' ? 'java.exe' : 'java'
  }
  
  // 生产环境优先使用打包的JDK
  const bundledJdkPath = path.join(process.resourcesPath, 'jdk', 'bin', 'java.exe')
  if (fs.existsSync(bundledJdkPath)) {
    console.log('✅ 使用打包的JDK:', bundledJdkPath)
    writeLog('✅ 使用打包的JDK: ' + bundledJdkPath)
    return bundledJdkPath
  }
  
  // 备选：使用打包的JRE
  const bundledJrePath = path.join(process.resourcesPath, 'jre', 'bin', 'java.exe')
  if (fs.existsSync(bundledJrePath)) {
    console.log('⚠️ 使用打包的JRE:', bundledJrePath)
    writeLog('⚠️ 使用打包的JRE (可能缺少编译器): ' + bundledJrePath)
    return bundledJrePath
  }
  
  // 如果都找不到，尝试使用系统Java
  console.log('未找到打包的Java环境，尝试使用系统Java')
  writeLog('未找到打包的Java环境，尝试使用系统Java')
  return process.platform === 'win32' ? 'java.exe' : 'java'
}

/**
 * 获取日志文件路径
 */
function getLogFilePath() {
  if (isDev) {
    return path.join(__dirname, '../log/backend.log')
  } else {
    const userDataPath = app.getPath('userData')
    const logDir = path.join(userDataPath, 'logs')
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true })
    }
    return path.join(logDir, 'backend.log')
  }
}

/**
 * 写入日志
 */
function writeLog(message) {
  const logFile = getLogFilePath()
  const timestamp = new Date().toISOString()
  const logMessage = `[${timestamp}] ${message}\n`
  
  try {
    fs.appendFileSync(logFile, logMessage)
  } catch (error) {
    console.error('写入日志失败:', error)
  }
}

/**
 * 启动Java后端服务
 */
function startBackendService() {
  const backendPath = getBackendPath()
  const javaCmd = getJavaExecutable()
  
  console.log('========================================')
  console.log('启动后端服务...')
  console.log('Java命令:', javaCmd)
  console.log('后端路径:', backendPath)
  console.log('当前环境:', isDev ? '开发环境' : '生产环境')
  console.log('========================================')
  
  writeLog('========================================')
  writeLog('启动后端服务...')
  writeLog(`Java命令: ${javaCmd}`)
  writeLog(`后端路径: ${backendPath}`)
  writeLog(`当前环境: ${isDev ? '开发环境' : '生产环境'}`)
  writeLog('========================================')
  
  // 检查Java命令是否存在
  if (!isDev && !fs.existsSync(javaCmd)) {
    const errorMsg = `❌ Java可执行文件不存在: ${javaCmd}`
    console.error(errorMsg)
    writeLog(errorMsg)
    dialog.showErrorBox(
      '启动失败',
      `找不到Java环境！\n\n路径：${javaCmd}\n\n请重新安装应用程序。`
    )
    return false
  }
  
  // 检查后端目录是否存在
  if (!fs.existsSync(backendPath)) {
    const errorMsg = `❌ 后端目录不存在: ${backendPath}`
    console.error(errorMsg)
    writeLog(errorMsg)
    dialog.showErrorBox(
      '启动失败',
      `找不到后端服务文件。\n\n路径：${backendPath}\n\n请确保应用程序完整安装。`
    )
    return false
  }
  
  // 检查quarkus-run.jar是否存在
  const jarPath = path.join(backendPath, 'quarkus-run.jar')
  if (!fs.existsSync(jarPath)) {
    const errorMsg = `❌ 后端JAR文件不存在: ${jarPath}`
    console.error(errorMsg)
    writeLog(errorMsg)
    dialog.showErrorBox(
      '启动失败',
      `找不到后端服务JAR文件。\n\n路径：${jarPath}\n\n请重新安装应用程序。`
    )
    return false
  }
  
  console.log('✅ 所有文件检查通过，开始启动Java进程...')
  writeLog('✅ 所有文件检查通过，开始启动Java进程...')
  
  try {
    // 使用相对路径启动JAR（相对于工作目录）
    const javaArgs = [
      '-Xms512m',
      '-Xmx2g',
      '-Dquarkus.http.port=8081',
      '-Dquarkus.http.host=127.0.0.1',
      // 强制Drools使用ECJ编译器（JRE环境） - 完整配置
      '-Ddrools.dialect.java.compiler=ECLIPSE',
      '-Ddrools.dialect.java.compiler.lnglevel=17',
      '-Ddrools.dialect.java.strict=false',
      '-Ddrools.compiler.lnglevel=1.8',
      '-Dkie.maven.settings.custom=/dev/null',
      //添加ECJ到类路径
      '-Dorg.drools.compiler.kie.builder.impl.KieBuilderImpl.ignoreKieModuleErrors=true',
      '-jar',
      'quarkus-run.jar'  // 使用相对路径
    ]
    
    writeLog(`Java命令完整路径: ${javaCmd}`)
    writeLog(`工作目录: ${backendPath}`)
    writeLog(`启动参数: ${javaArgs.join(' ')}`)
    
    // 启动Java进程
    javaProcess = spawn(javaCmd, javaArgs, {
      cwd: backendPath,
      stdio: ['ignore', 'pipe', 'pipe'],
      windowsHide: false  // 改为false，方便调试
    })
    
    console.log('✅ Java进程已启动，PID:', javaProcess.pid)
    writeLog(`✅ Java进程已启动，PID: ${javaProcess.pid}`)
    
    // 监听标准输出
    javaProcess.stdout.on('data', (data) => {
      const output = data.toString().trim()
      console.log('[Backend]', output)
      writeLog(`[Backend] ${output}`)
    })
    
    // 监听错误输出
    javaProcess.stderr.on('data', (data) => {
      const output = data.toString().trim()
      console.error('[Backend Error]', output)
      writeLog(`[Backend Error] ${output}`)
    })
    
    // 监听进程退出
    javaProcess.on('exit', (code, signal) => {
      const msg = `Java进程退出: code=${code}, signal=${signal}`
      console.log(msg)
      writeLog(msg)
      javaProcess = null
      
      if (code !== 0 && code !== null) {
        dialog.showErrorBox(
          '后端服务异常',
          `后端服务意外退出，退出代码: ${code}。应用程序可能无法正常工作。`
        )
      }
    })
    
    // 监听进程错误
    javaProcess.on('error', (err) => {
      const errorInfo = `❌ 启动Java进程失败: ${err.message}, 代码: ${err.code || '未知'}, Java命令: ${javaCmd}`
      console.error(errorInfo)
      writeLog(errorInfo)
      dialog.showErrorBox(
        '启动失败',
        `启动后端服务失败，请检查Java环境是否正确安装（需要Java 17或更高版本）。\n\n` +
        `错误详情：${err.message}\n\n` +
        `Java路径：${javaCmd}\n\n` +
        `错误代码：${err.code || '未知'}`
      )
    })
    
    return true
  } catch (error) {
    const errorMsg = `启动后端服务异常: ${error.message}`
    console.error(errorMsg)
    writeLog(errorMsg)
    dialog.showErrorBox(
      '启动失败',
      `启动后端服务时发生错误: ${error.message}`
    )
    return false
  }
}

/**
 * 停止Java后端服务
 */
function stopBackendService() {
  if (javaProcess) {
    console.log('正在停止后端服务...')
    try {
      javaProcess.kill('SIGTERM')
      // 给予进程5秒时间优雅退出
      setTimeout(() => {
        if (javaProcess && !javaProcess.killed) {
          console.log('强制终止后端服务...')
          javaProcess.kill('SIGKILL')
        }
      }, 5000)
    } catch (error) {
      console.error('停止后端服务失败:', error)
    }
    javaProcess = null
  }
}

/**
 * 等待后端服务启动
 */
async function waitForBackend(maxAttempts = 60) {  // 增加到60次（60秒）
  const http = require('http')
  
  console.log(`开始健康检查，最多尝试 ${maxAttempts} 次...`)
  writeLog(`开始健康检查，最多尝试 ${maxAttempts} 次...`)
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      await new Promise((resolve, reject) => {
        const req = http.get('http://127.0.0.1:8081/q/health', (res) => {
          let data = ''
          res.on('data', chunk => data += chunk)
          res.on('end', () => {
            if (res.statusCode === 200) {
              console.log(`✅ 后端健康检查成功: ${data}`)
              resolve()
            } else {
              reject(new Error(`Health check failed: ${res.statusCode}`))
            }
          })
        })
        
        req.on('error', reject)
        req.setTimeout(3000, () => {  // 增加超时到3秒
          req.abort()
          reject(new Error('Timeout'))
        })
      })
      
      console.log(`✅ 后端服务已就绪（尝试 ${i + 1}/${maxAttempts} 次）`)
      writeLog(`✅ 后端服务已就绪（尝试 ${i + 1} 次）`)
      return true
    } catch (error) {
      const progress = Math.floor((i + 1) / maxAttempts * 100)
      console.log(`⏳ 等待后端启动... ${progress}% (${i + 1}/${maxAttempts}) - ${error.message}`)
      writeLog(`健康检查失败（尝试 ${i + 1}/${maxAttempts}）: ${error.message}`)
      if (i < maxAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }
  
  console.error('❌ 后端服务启动超时')
  writeLog('❌ 后端服务启动超时')
  return false
}

/**
 * 获取适合当前系统的背景色
 */
function getBackgroundColor() {
  if (systemConfig.highPerformanceMode) {
    // 高性能模式使用深色背景，减少闪烁
    return '#1a1a1a'
  } else {
    // 标准模式使用浅色背景
    return '#f5f5f5'
  }
}

/**
 * 创建主窗口
 */
async function createMainWindow() {
  const backgroundColor = getBackgroundColor()
  
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: false, // 生产环境下允许加载本地文件
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      preload: path.join(__dirname, 'preload.js'),
      offscreen: false,
      backgroundThrottling: false
    },
    icon: path.join(__dirname, '../public/icon.png'),
    titleBarStyle: 'default',
    show: false, // 先隐藏，等准备好再显示
    backgroundColor: backgroundColor, // 根据系统配置选择背景色
    frame: true,
    autoHideMenuBar: true,
    enableLargerThanScreen: false,
    useContentSize: false,
    center: true,
    transparent: false,
    hasShadow: true,
    opacity: 1.0
  })

  // 如果不是开发环境，启动后端服务
  if (!isDev) {
    console.log('生产环境：启动后端服务...')
    const started = startBackendService()
    
    if (!started) {
      mainWindow.close()
      return
    }
    
    console.log('⏳ 等待后端启动，窗口保持隐藏...')
    const backendReady = await waitForBackend()
    
    if (!backendReady) {
      const logPath = getLogFilePath()
      writeLog('❌ 应用启动失败：后端服务启动超时')
      dialog.showErrorBox(
        '启动超时',
        `后端服务启动超时。\n\n` +
        `请检查日志文件以获取详细信息：\n${logPath}\n\n` +
        `可能的原因：\n` +
        `1. Java环境异常\n` +
        `2. 端口8081被占用\n` +
        `3. 数据库初始化失败`
      )
      mainWindow.close()
      return
    }
    
    console.log('✅ 后端启动完成，准备加载应用界面...')
  }

  // 加载应用
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173')
    // 开发环境打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    // 生产环境：从 resources/dist 加载（extraResources）
    const distPath = path.join(process.resourcesPath, 'dist', 'index.html')
    console.log('📂 加载前端文件:', distPath)
    console.log('📂 文件是否存在:', fs.existsSync(distPath))
    
    // 检查文件是否存在
    if (!fs.existsSync(distPath)) {
      console.error('❌ 找不到前端文件:', distPath)
      writeLog(`❌ 找不到前端文件: ${distPath}`)
      dialog.showErrorBox(
        '启动失败',
        `找不到前端文件。\n\n路径：${distPath}\n\n请重新安装应用程序。`
      )
      mainWindow.close()
      return
    }
    
    // 使用file://协议加载本地文件
    mainWindow.loadFile(distPath).catch(err => {
      console.error('❌ 加载前端文件失败:', err)
      writeLog(`❌ 加载前端文件失败: ${err.message}`)
    })
  }

  // 监听页面加载失败
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ 页面加载失败:', errorCode, errorDescription)
    writeLog(`❌ 页面加载失败: ${errorCode} - ${errorDescription}`)
  })

  // 监听控制台消息（调试用）
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`[Renderer ${level}]`, message)
  })

  // 窗口准备好后显示（避免白屏和闪烁）
  mainWindow.once('ready-to-show', () => {
    if (!isDev && systemConfig.highPerformanceMode) {
      // 高性能模式：延迟显示，确保GPU渲染完成
      setTimeout(() => {
        mainWindow.show()
        setTimeout(() => {
          mainWindow.focus()
        }, 16)
      }, 50)
    } else {
      // 标准模式：直接显示
      mainWindow.show()
      mainWindow.focus()
    }
  })

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // 防止新窗口打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

/**
 * 应用准备完成
 */
app.whenReady().then(() => {
  createMainWindow()

  // macOS应用激活时重新创建窗口
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow()
    }
  })
})

/**
 * 所有窗口关闭时退出应用（macOS除外）
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    stopBackendService()
    app.quit()
  }
})

/**
 * 安全策略：阻止新窗口创建
 */
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    shell.openExternal(navigationUrl)
  })
})

// ====== IPC 通信处理 ======

/**
 * 保存备份文件
 */
ipcMain.handle('save-backup', async (event, filename, data) => {
  try {
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
      title: '保存备份文件',
      defaultPath: filename,
      filters: [
        { name: 'JSON文件', extensions: ['json'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    })

    if (!canceled && filePath) {
      await fs.promises.writeFile(filePath, data, 'utf8')
      return { success: true, path: filePath }
    }
    
    return { success: false, message: '用户取消保存' }
  } catch (error) {
    console.error('保存备份失败:', error)
    return { success: false, message: error.message }
  }
})

/**
 * 加载备份文件
 */
ipcMain.handle('load-backup', async () => {
  try {
    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
      title: '选择备份文件',
      filters: [
        { name: 'JSON文件', extensions: ['json'] },
        { name: '所有文件', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (!canceled && filePaths.length > 0) {
      const data = await fs.promises.readFile(filePaths[0], 'utf8')
      return { success: true, data, path: filePaths[0] }
    }
    
    return { success: false, message: '用户取消选择' }
  } catch (error) {
    console.error('加载备份失败:', error)
    return { success: false, message: error.message }
  }
})

/**
 * 导出Excel文件
 */
ipcMain.handle('export-excel', async (event, filename, data) => {
  try {
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
      title: '导出Excel文件',
      defaultPath: filename,
      filters: [
        { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    })

    if (!canceled && filePath) {
      await fs.promises.writeFile(filePath, data, 'utf8')
      return { success: true, path: filePath }
    }
    
    return { success: false, message: '用户取消导出' }
  } catch (error) {
    console.error('导出Excel失败:', error)
    return { success: false, message: error.message }
  }
})

/**
 * 获取应用信息
 */
ipcMain.handle('get-app-info', () => {
  return {
    name: app.getName(),
    version: app.getVersion(),
    platform: process.platform,
    arch: process.arch,
    electronVersion: process.versions.electron,
    nodeVersion: process.versions.node,
    systemConfig: systemConfig // 返回系统配置信息
  }
})

/**
 * 显示消息框
 */
ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options)
  return result
})

/**
 * 重启应用
 */
ipcMain.handle('restart-app', () => {
  stopBackendService()
  app.relaunch()
  app.exit(0)
})

/**
 * 最小化窗口
 */
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize()
  }
})

/**
 * 最大化/还原窗口
 */
ipcMain.handle('toggle-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize()
    } else {
      mainWindow.maximize()
    }
  }
})

/**
 * 关闭窗口
 */
ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close()
  }
})

// ====== 应用生命周期 ======

/**
 * 应用退出前的清理工作
 */
app.on('before-quit', (event) => {
  console.log('应用即将退出，执行清理工作...')
  stopBackendService()
})

/**
 * 处理未捕获的异常
 */
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
})

