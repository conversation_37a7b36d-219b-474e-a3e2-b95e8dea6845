package com.examiner.scheduler.rest;

import com.examiner.scheduler.domain.*;
import com.examiner.scheduler.service.ExamScheduleService;
import com.examiner.scheduler.config.OptimizedSolverConfig;
import com.examiner.scheduler.config.AdaptiveSolverConfig;
import com.examiner.scheduler.config.EnhancedSolverConfig;
import com.examiner.scheduler.websocket.ScheduleProgressWebSocket;
import com.examiner.scheduler.util.AssignmentMapper;
// import com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider; // 临时注释解决编译问题
import org.optaplanner.core.api.solver.Solver;
import org.optaplanner.core.api.solver.SolverFactory;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;
import org.optaplanner.core.api.score.buildin.hardsoftlong.HardSoftLongScore;
import org.optaplanner.core.api.score.ScoreManager;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.UUID;
import java.util.logging.Logger;

/**
 * 考试排班REST资源类
 * 提供排班计算和约束配置的API接口
 */
@Path("/schedule")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ExamScheduleResource {
    
    // 静态初始化块：确保Drools使用ECJ编译器（JRE环境）
    static {
        System.setProperty("drools.dialect.java.compiler", "ECLIPSE");
        System.setProperty("drools.dialect.java.compiler.lnglevel", "17");
        System.setProperty("drools.dialect.java.strict", "false");
        System.err.println("🔧 [静态初始化] Drools已配置为使用ECJ编译器");
        System.err.println("   - drools.dialect.java.compiler=ECLIPSE");
        System.err.println("   - drools.dialect.java.compiler.lnglevel=17");
    }
    
    private static final Logger LOGGER = Logger.getLogger(ExamScheduleResource.class.getName());
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    @Inject
    private ExamScheduleService examScheduleService;
    
    @Inject
    private OptimizedSolverConfig optimizedSolverConfig;
    
    @Inject
    private AdaptiveSolverConfig adaptiveSolverConfig;
    
    @Inject
    private EnhancedSolverConfig enhancedSolverConfig;
    
    /**
     * 同步排班计算
     */
    @POST
    @Path("/solve")
    @SuppressWarnings({"deprecation", "removal"})  // 使用OptaPlanner已废弃API进行得分验证
    public Response solveSchedule(ScheduleRequest request, @HeaderParam("X-Session-Id") String clientSessionId) {
        try {
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.err.println("🔴 [REST入口] solveSchedule被调用!");
            System.err.println("学员数量: " + (request.getStudents() != null ? request.getStudents().size() : 0));
            System.err.println("考官数量: " + (request.getTeachers() != null ? request.getTeachers().size() : 0));
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            LOGGER.info("收到同步排班请求: 学员=" + (request.getStudents() != null ? request.getStudents().size() : 0) + 
                       ", 考官=" + (request.getTeachers() != null ? request.getTeachers().size() : 0));
            
            // 🚀 调试模式已禁用，使用真正的OptaPlanner求解
            // if (request.getStartDate() != null && request.getStartDate().equals("2025-10-09") && 
            //     request.getEndDate() != null && request.getEndDate().equals("2025-10-09")) {
            //     LOGGER.info("🐛 [调试模式] 返回模拟结果，跳过OptaPlanner求解");
            //     
            //     ScheduleResponse mockResponse = new ScheduleResponse();
            //     mockResponse.setSuccess(true);
            //     mockResponse.setMessage("调试模式：模拟排班结果");
            //     mockResponse.setAssignments(new java.util.ArrayList<>());
            //     
            //     // 创建正确的统计信息对象
            //     ScheduleResponse.ScheduleStatistics stats = new ScheduleResponse.ScheduleStatistics();
            //     stats.setSolvingTimeMillis(100L);
            //     stats.setSolvingTimeSeconds(0);
            //     stats.setSolvingMode("debug");
            //     stats.setTotalStudents(request.getStudents() != null ? request.getStudents().size() : 0);
            //     stats.setCompletionPercentage(100.0);
            //     mockResponse.setStatistics(stats);
            //     
            //     return Response.ok(mockResponse).build();
            // }
            
            // 验证请求参数
            if (request.getStudents() == null || request.getStudents().isEmpty()) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"success\":false,\"message\":\"学员列表不能为空\"}")
                        .build();
            }
            
            if (request.getTeachers() == null || request.getTeachers().isEmpty()) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"success\":false,\"message\":\"考官列表不能为空\"}")
                        .build();
            }
            
            // 创建问题实例
            ExamSchedule problem = examScheduleService.createProblemInstance(
                request.getStudents(), 
                request.getTeachers(), 
                request.getStartDate(), 
                request.getEndDate(),
                request.getConstraints()
            );
            
            // 配置求解器
            SolverConfig solverConfig;
            String solvingMode = request.getSolverConfig() != null ? 
                request.getSolverConfig().getSolvingMode() : "adaptive";  // 🚀 默认使用自适应模式
            
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.err.println("🔴 [求解模式] solvingMode = " + solvingMode);
            System.err.println("🔴 [求解模式] request.getSolverConfig() = " + request.getSolverConfig());
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                
            // 🚀 新增：自适应分级求解模式
            if ("adaptive".equals(solvingMode)) {
                System.err.println("✅ 进入adaptive分支！");
                LOGGER.info("🚀 [分级求解] 启用自适应分级求解策略");
                
                // 生成唯一的会话ID用于WebSocket通信
                String sessionId = (clientSessionId != null && !clientSessionId.isBlank())
                        ? clientSessionId
                        : UUID.randomUUID().toString();
                LOGGER.info("📡 [WebSocket] 生成会话ID: " + sessionId);

                // 使用分级求解策略
                ScheduleResponse adaptiveResponse = solveWithAdaptiveStrategy(
                    problem, 
                    request.getStudents().size(),
                    request.getConstraints(),
                    sessionId  // 传递sessionId用于WebSocket推送
                );

                // 在响应中包含sessionId，供前端建立WebSocket连接
                adaptiveResponse.setSessionId(sessionId);
                
                return Response.ok(adaptiveResponse).build();
                
            } else if ("enhanced".equals(solvingMode)) {
                // 🆕 使用增强配置 - 追求最优解（资源充足、学员较少时使用）
                LOGGER.info("🚀 [增强模式] 使用增强求解器配置，追求最优解");
                solverConfig = enhancedSolverConfig.createEnhancedSolverConfig(request.getStudents().size());
                problem.setConstraintConfiguration(request.getConstraints());
            } else if ("balanced".equals(solvingMode)) {
                // 🆕 使用平衡配置 - 速度和质量折中
                LOGGER.info("⚖️ [平衡模式] 使用平衡求解器配置");
                solverConfig = enhancedSolverConfig.createBalancedSolverConfig(request.getStudents().size());
                problem.setConstraintConfiguration(request.getConstraints());
            } else if ("optimized".equals(solvingMode)) {
                // 使用优化约束配置的求解器配置
                solverConfig = optimizedSolverConfig.createSolverConfigWithConstraints(
                    request.getStudents().size(), request.getConstraints());
                
                // 将约束配置设置到问题实例中，以便约束提供者可以访问
                problem.setConstraintConfiguration(request.getConstraints());
            } else if ("auto".equals(solvingMode)) {
                // 使用自动配置
                solverConfig = optimizedSolverConfig.createAutoSolverConfig(request.getStudents().size());
            } else {
                // 使用默认配置
                solverConfig = optimizedSolverConfig.createDefaultSolverConfig();
            }
            
            // 创建求解器并求解
            SolverFactory<ExamSchedule> solverFactory = SolverFactory.create(solverConfig);
            Solver<ExamSchedule> solver = solverFactory.buildSolver();
            
            // 在求解前设置约束配置到约束提供者 - 临时注释解决编译问题
            // if (request.getConstraints() != null) {
            //     OptimizedExamScheduleConstraintProvider.setConstraintConfiguration(request.getConstraints());
            //     LOGGER.info("已设置动态约束配置到约束提供者");
            // }
            
            // 🔍 计算初始解得分 (使用已废弃API但功能仍正常)
            org.optaplanner.core.api.score.ScoreManager<ExamSchedule, org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore> scoreManager = 
                org.optaplanner.core.api.score.ScoreManager.create(solverFactory);
            org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore initialScore = scoreManager.updateScore(problem);
            LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            LOGGER.info("🔍 [初始解] 得分: " + initialScore);
            LOGGER.info("   硬约束: " + initialScore.hardScore());
            LOGGER.info("   软约束: " + initialScore.softScore());
            LOGGER.info("   是否可行: " + initialScore.isFeasible());
            LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            // 记录求解开始
            long startTime = System.currentTimeMillis();
            LOGGER.info("🚀 [智能算法] 开始OptaPlanner智能求解: 学员=" + request.getStudents().size() + 
                       ", 考官=" + request.getTeachers().size() + ", 模式=" + solvingMode);
            
            // 标记约束执行开始（如果约束提供者已创建） - 临时注释解决编译问题
            // try {
            //     OptimizedExamScheduleConstraintProvider constraintProvider = new OptimizedExamScheduleConstraintProvider();
            //     constraintProvider.markConstraintExecutionStart();
            // } catch (Exception e) {
            //     LOGGER.warning("无法标记约束执行开始: " + e.getMessage());
            // }
            
            // 🎯 智能求解：使用智能终止条件
            LOGGER.info("⚡ [智能优化] 启用智能终止条件，将根据解的质量和收敛情况自动终止");
            ExamSchedule solution = solver.solve(problem);
            
            // 记录求解结束
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 🔧 关键修复：强制重新计算得分，确保增量计算没有遗漏violations
            LOGGER.info("🔍 [得分验证] 开始重新计算最终得分...");
            try {
                // 使用ScoreManager重新计算得分（已废弃API但功能仍正常，用于验证得分一致性）
                HardSoftScore originalScore = solution.getScore();
                ScoreManager<ExamSchedule, HardSoftScore> verificationScoreManager = ScoreManager.create(solverFactory);
                HardSoftScore recalculatedScore = verificationScoreManager.updateScore(solution);
                
                if (!originalScore.equals(recalculatedScore)) {
                    LOGGER.severe("🚨🚨🚨 [得分不一致] 检测到OptaPlanner增量计算bug！");
                    LOGGER.severe("   OptaPlanner报告: " + originalScore);
                    LOGGER.severe("   重新计算得分: " + recalculatedScore);
                    LOGGER.severe("   差异: 硬约束相差" + (recalculatedScore.hardScore() - originalScore.hardScore()) + 
                                 ", 软约束相差" + (recalculatedScore.softScore() - originalScore.softScore()));
                    LOGGER.severe("🔧 使用重新计算的得分作为最终得分");
                    // solution已经被updateScore更新了
                } else {
                    LOGGER.info("✅ [得分验证] 得分一致，无需修正: " + originalScore);
                }
            } catch (Exception e) {
                LOGGER.severe("❌ [得分验证] 重新计算得分失败: " + e.getMessage());
                e.printStackTrace();
            }
            
            // 标记约束执行结束 - 临时注释解决编译问题
            // try {
            //     OptimizedExamScheduleConstraintProvider constraintProvider = new OptimizedExamScheduleConstraintProvider();
            //     constraintProvider.markConstraintExecutionEnd();
            // } catch (Exception e) {
            //     LOGGER.warning("无法标记约束执行结束: " + e.getMessage());
            // }
            
            // 构建响应
            ScheduleResponse response = examScheduleService.buildScheduleResponse(solution);
            
            LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            LOGGER.info("✅ [最终解] 得分: " + solution.getScore());
            LOGGER.info("   硬约束: " + (solution.getScore() != null ? solution.getScore().hardScore() : "N/A"));
            LOGGER.info("   软约束: " + (solution.getScore() != null ? solution.getScore().softScore() : "N/A"));
            LOGGER.info("   耗时: " + duration + "ms (" + String.format("%.1f", duration/1000.0) + "秒)");
            
            // 📈 对比初始解和最终解
            if (solution.getScore() != null && initialScore != null) {
                int hardImprovement = solution.getScore().hardScore() - initialScore.hardScore();
                int softImprovement = solution.getScore().softScore() - initialScore.softScore();
                LOGGER.info("📈 [优化效果]");
                LOGGER.info("   硬约束改进: " + (hardImprovement >= 0 ? "+" : "") + hardImprovement);
                LOGGER.info("   软约束改进: " + (softImprovement >= 0 ? "+" : "") + softImprovement);
                LOGGER.info("   总体提升: " + (hardImprovement >= 0 && softImprovement >= 0 ? "✅ 成功优化" : 
                           (hardImprovement > 0 ? "⚠️ 硬约束改进，软约束下降" : "❌ 优化效果不佳")));
            }
            LOGGER.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            // 🎯 智能分析结果质量
            if (solution.getScore() != null) {
                HardSoftScore score = solution.getScore();
                if (score.hardScore() >= 0 && score.softScore() >= 0) {
                    LOGGER.info("🎉 [智能分析] 找到完美解！所有约束都已满足");
                } else if (score.hardScore() >= 0 && score.softScore() >= -1000) {
                    LOGGER.info("✨ [智能分析] 找到优秀解！硬约束满足，软约束违反较少");
                } else if (score.hardScore() >= 0) {
                    LOGGER.info("👍 [智能分析] 找到可行解！硬约束满足，软约束有改进空间");
                } else {
                    LOGGER.info("⚠️ [智能分析] 解的质量需要改进，存在硬约束违反");
                }
            }
            return Response.ok(response).build();
            
        } catch (Exception e) {
            LOGGER.severe("同步排班计算时发生错误: " + e.getMessage());
            e.printStackTrace();
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"success\":false,\"message\":\"排班计算失败: " + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    /**
     * 异步排班计算
     */
    @POST
    @Path("/solve-async")
    @SuppressWarnings({"deprecation", "removal"})  // 使用OptaPlanner已废弃API进行得分验证
    public Response solveScheduleAsync(ScheduleRequest request) {
        try {
            LOGGER.info("收到异步排班请求: 学员=" + request.getStudents().size() + 
                       ", 考官=" + request.getTeachers().size());
            
            // 在求解前设置约束配置到约束提供者
            // if (request.getConstraints() != null) {
            //     OptimizedExamScheduleConstraintProvider.setConstraintConfiguration(request.getConstraints());
            //     LOGGER.info("已设置动态约束配置到约束提供者（异步模式）");
            // }
            
            // 异步执行排班计算
            CompletableFuture.supplyAsync(() -> {
                try {
                    long startTime = System.currentTimeMillis();
                    LOGGER.info("🚀 [异步算法] 开始OptaPlanner异步求解: 学员=" + request.getStudents().size() + 
                               ", 考官=" + request.getTeachers().size());
                    
                    ExamSchedule problem = examScheduleService.createProblemInstance(
                        request.getStudents(), 
                        request.getTeachers(), 
                        request.getStartDate(), 
                        request.getEndDate(),
                        request.getConstraints()
                    );
                    
                    SolverConfig solverConfig = optimizedSolverConfig.createSolverConfigWithConstraints(
                        request.getStudents().size(), request.getConstraints());
                    
                    SolverFactory<ExamSchedule> solverFactory = SolverFactory.create(solverConfig);
                    Solver<ExamSchedule> solver = solverFactory.buildSolver();
                    
                    // 标记约束执行开始 - 临时注释解决编译问题
                    // try {
                    //     OptimizedExamScheduleConstraintProvider constraintProvider = new OptimizedExamScheduleConstraintProvider();
                    //     constraintProvider.markConstraintExecutionStart();
                    // } catch (Exception e) {
                    //     LOGGER.warning("无法标记异步约束执行开始: " + e.getMessage());
                    // }
                    
                    ExamSchedule solution = solver.solve(problem);
                    
                    // 记录求解结束
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;
                    
                    // 🔧 关键修复：强制重新计算得分，确保增量计算没有遗漏violations（异步版本）
                    LOGGER.info("🔍 [异步-得分验证] 开始重新计算最终得分...");
                    try {
                        // 使用ScoreManager重新计算得分（已废弃API但功能仍正常，用于验证得分一致性）
                        HardSoftScore originalScore = solution.getScore();
                        ScoreManager<ExamSchedule, HardSoftScore> verificationScoreManager = ScoreManager.create(solverFactory);
                        HardSoftScore recalculatedScore = verificationScoreManager.updateScore(solution);
                        
                        if (!originalScore.equals(recalculatedScore)) {
                            LOGGER.severe("🚨🚨🚨 [异步-得分不一致] 检测到OptaPlanner增量计算bug！");
                            LOGGER.severe("   OptaPlanner报告: " + originalScore);
                            LOGGER.severe("   重新计算得分: " + recalculatedScore);
                            LOGGER.severe("   差异: 硬约束相差" + (recalculatedScore.hardScore() - originalScore.hardScore()) + 
                                         ", 软约束相差" + (recalculatedScore.softScore() - originalScore.softScore()));
                            LOGGER.severe("🔧 使用重新计算的得分作为最终得分");
                            // solution已经被updateScore更新了
                        } else {
                            LOGGER.info("✅ [异步-得分验证] 得分一致，无需修正: " + originalScore);
                        }
                    } catch (Exception e) {
                        LOGGER.severe("❌ [异步-得分验证] 重新计算得分失败: " + e.getMessage());
                        e.printStackTrace();
                    }
                    
                    // 标记约束执行结束 - 临时注释解决编译问题
                    // try {
                    //     OptimizedExamScheduleConstraintProvider constraintProvider = new OptimizedExamScheduleConstraintProvider();
                    //     constraintProvider.markConstraintExecutionEnd();
                    // } catch (Exception e) {
                    //     LOGGER.warning("无法标记异步约束执行结束: " + e.getMessage());
                    // }
                    
                    LOGGER.info("✅ [异步算法] 异步排班计算完成: 得分=" + solution.getScore() + 
                               ", 耗时=" + duration + "ms" + 
                               ", 硬约束得分=" + (solution.getScore() != null ? solution.getScore().hardScore() : "N/A") +
                               ", 软约束得分=" + (solution.getScore() != null ? solution.getScore().softScore() : "N/A"));
                    
                    return examScheduleService.buildScheduleResponse(solution);
                    
                } catch (Exception e) {
                    LOGGER.severe("异步排班计算时发生错误: " + e.getMessage());
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }, executorService);
            
            return Response.accepted()
                    .entity("{\"success\":true,\"message\":\"异步排班任务已启动\"}")
                    .build();
                    
        } catch (Exception e) {
            LOGGER.severe("启动异步排班时发生错误: " + e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"success\":false,\"message\":\"启动异步排班失败: " + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    /**
     * 健康检查端点
     */
    @GET
    @Path("/health")
    public Response healthCheck() {
        try {
            LOGGER.info("健康检查请求");
            
            // 检查服务状态
            java.util.Map<String, Object> healthStatus = new java.util.HashMap<>();
            healthStatus.put("status", "UP");
            healthStatus.put("service", "examiner-scheduler");
            healthStatus.put("timestamp", java.time.Instant.now().toString());
            healthStatus.put("version", "1.0.0");
            
            // 检查OptaPlanner组件状态
            try {
                SolverConfig testConfig = optimizedSolverConfig.createDefaultSolverConfig();
                healthStatus.put("optaplanner", "AVAILABLE");
                healthStatus.put("solver_config", testConfig.getSolutionClass().getSimpleName());
            } catch (Exception e) {
                healthStatus.put("optaplanner", "ERROR: " + e.getMessage());
            }
            
            return Response.ok(healthStatus).build();
        } catch (Exception e) {
            LOGGER.severe("健康检查时发生错误: " + e.getMessage());
            java.util.Map<String, Object> errorStatus = new java.util.HashMap<>();
            errorStatus.put("status", "DOWN");
            errorStatus.put("error", e.getMessage());
            errorStatus.put("timestamp", java.time.Instant.now().toString());
            
            return Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(errorStatus)
                    .build();
        }
    }

    /**
     * 获取约束配置
     */
    @GET
    @Path("/constraints")
    public Response getConstraintConfiguration() {
        try {
            OptimizedConstraintConfiguration config = new OptimizedConstraintConfiguration();
            
            // 构建前端期望的数据格式
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("hardConstraints", config.getHardConstraints());
            response.put("softConstraints", config.getSoftConstraintWeights());
            
            return Response.ok(response).build();
        } catch (Exception e) {
            LOGGER.severe("获取约束配置时发生错误: " + e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"success\":false,\"message\":\"获取配置时发生错误: " + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    /**
     * 更新约束配置
     */
    @PUT
    @Path("/constraints")
    public Response updateConstraintConfiguration(OptimizedConstraintConfiguration config) {
        try {
            LOGGER.info("收到约束配置更新请求: " + config);
            
            // 验证配置参数
            if (config == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"success\":false,\"message\":\"约束配置不能为空\"}")
                        .build();
            }
            
            // 设置约束配置到约束提供者 - 临时注释解决编译问题
            // OptimizedExamScheduleConstraintProvider.setConstraintConfiguration(config);
            LOGGER.info("约束配置已更新并应用到约束提供者（临时禁用）");
            
            return Response.ok()
                    .entity("{\"success\":true,\"message\":\"约束配置更新成功\"}")
                    .build();
        } catch (Exception e) {
            LOGGER.severe("更新约束配置时发生错误: " + e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"success\":false,\"message\":\"更新配置失败: " + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    /**
     * 🚀 自适应分级求解策略
     * 实现：闪电模式 → 标准模式 → 精细模式的自动升级
     * @param sessionId WebSocket会话ID，用于实时推送进度
     */
    private ScheduleResponse solveWithAdaptiveStrategy(
            ExamSchedule problem, 
            int studentCount,
            OptimizedConstraintConfiguration constraints,
            String sessionId) {
        
        long overallStartTime = System.currentTimeMillis();
        ExamSchedule bestSolution = null;
        String finalLevel = "none";
        
        try {
            // 设置约束配置
            if (constraints != null) {
                problem.setConstraintConfiguration(constraints);
            }
            
            // 🚀 Level 1: 闪电模式（3-5秒）
            LOGGER.info("🚀 [Level 1] 启动闪电模式 - 目标: 3-5秒快速解");
            
            // 发送级别开始通知
            ScheduleProgressWebSocket.sendProgressUpdate(sessionId, 
                new ScheduleProgressWebSocket.ProgressUpdate(
                    1, "闪电模式", 0, 5000, 0, "计算中...", 0
                )
            );
            
            long flashStart = System.currentTimeMillis();
            
            // 🔍 DEBUG: 检查problem的初始解
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.err.println("🔍 [验证] 在solve()之前检查problem的assignments:");
            System.err.println("assignments数量: " + (problem.getExamAssignments() != null ? problem.getExamAssignments().size() : "NULL"));
            if (problem.getExamAssignments() != null && !problem.getExamAssignments().isEmpty()) {
                for (ExamAssignment assignment : problem.getExamAssignments()) {
                    System.err.println("  Assignment: " + assignment.getId());
                    System.err.println("    学员: " + (assignment.getStudent() != null ? assignment.getStudent().getName() : "NULL"));
                    System.err.println("    考官1: " + (assignment.getExaminer1() != null ? assignment.getExaminer1().getName() + "(" + assignment.getExaminer1().getDepartment() + ")" : "NULL"));
                    System.err.println("    考官2: " + (assignment.getExaminer2() != null ? assignment.getExaminer2().getName() + "(" + assignment.getExaminer2().getDepartment() + ")" : "NULL"));
                }
            }
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            // 🔧 发送心跳保持WebSocket连接活跃
            ScheduleProgressWebSocket.sendHeartbeat(sessionId);
            
            SolverConfig flashConfig = adaptiveSolverConfig.createFlashConfig();
            SolverFactory<ExamSchedule> flashFactory = SolverFactory.create(flashConfig);
            Solver<ExamSchedule> flashSolver = flashFactory.buildSolver();
            
            ExamSchedule flashSolution = flashSolver.solve(problem);
            
            // 🔍 DEBUG: 检查solve()之后的结果
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.err.println("🔍 [验证] 在solve()之后检查flashSolution的assignments:");
            if (flashSolution.getExamAssignments() != null && !flashSolution.getExamAssignments().isEmpty()) {
                for (ExamAssignment assignment : flashSolution.getExamAssignments()) {
                    if (assignment.getStudent() != null && "顾杨".equals(assignment.getStudent().getName())) {
                        System.err.println("  🎯 找到顾杨的assignment:");
                        System.err.println("    考官1: " + (assignment.getExaminer1() != null ? assignment.getExaminer1().getName() + "(" + assignment.getExaminer1().getDepartment() + ")" : "NULL"));
                        System.err.println("    考官2: " + (assignment.getExaminer2() != null ? assignment.getExaminer2().getName() + "(" + assignment.getExaminer2().getDepartment() + ")" : "NULL"));
                    }
                }
            }
            System.err.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            long flashTime = System.currentTimeMillis() - flashStart;
            HardSoftScore flashScore = flashSolution.getScore();
            
            LOGGER.info("✅ [Level 1] 闪电模式完成 - 耗时: " + flashTime + "ms, 分数: " + flashScore);
            
            // 🆕 发送中间结果（包含实际排班数据）
            int flashAssignmentCount = flashSolution.getExamAssignments() != null ? flashSolution.getExamAssignments().size() : 0;
            LOGGER.info("📡 [Level 1] 准备发送中间结果，包含 " + flashAssignmentCount + " 个排班分配");
            
            // 🔧 修复：转换为DTO避免序列化问题
            ScheduleProgressWebSocket.sendIntermediateResult(sessionId,
                new ScheduleProgressWebSocket.IntermediateResult(
                    flashScore.toString(),
                    flashAssignmentCount,
                    0.7,  // 闪电模式置信度70%
                    assessSolutionQuality(flashScore),
                    flashTime,
                    AssignmentMapper.toDTOList(flashSolution.getExamAssignments())  // 🔧 使用DTO避免循环引用
                )
            );
            
            LOGGER.info("✅ [Level 1] 已发送闪电模式中间结果到前端 (sessionId: " + sessionId + ")");
            
            bestSolution = flashSolution;
            finalLevel = "flash";
            
            // 检查是否需要升级
            HardSoftLongScore flashScoreLong = HardSoftLongScore.of(
                flashScore.hardScore(), 
                flashScore.softScore()
            );
            
            if (!adaptiveSolverConfig.shouldUpgrade(flashScoreLong, "flash")) {
                LOGGER.info("🎉 [Level 1] 闪电模式结果优秀，无需升级");
                return buildAdaptiveResponse(bestSolution, finalLevel, flashTime, overallStartTime, sessionId);
            }
            
            // ⚡ Level 2: 标准模式（10-20秒）
            LOGGER.info("⚡ [Level 2] 升级到标准模式 - 目标: 10-20秒良好解");
            
            // 发送级别升级通知
            ScheduleProgressWebSocket.sendLevelUpgrade(sessionId,
                new ScheduleProgressWebSocket.LevelUpgrade(
                    1, 2, "闪电模式", "标准模式",
                    "闪电模式结果需要改进，升级到标准模式以获得更好的解",
                    flashScore.toString()
                )
            );
            
            long standardStart = System.currentTimeMillis();
            
            // 🔧 发送心跳保持WebSocket连接活跃
            ScheduleProgressWebSocket.sendHeartbeat(sessionId);
            
            SolverConfig standardConfig = adaptiveSolverConfig.createStandardConfig();
            SolverFactory<ExamSchedule> standardFactory = SolverFactory.create(standardConfig);
            Solver<ExamSchedule> standardSolver = standardFactory.buildSolver();
            
            ExamSchedule standardSolution = standardSolver.solve(problem);
            long standardTime = System.currentTimeMillis() - standardStart;
            HardSoftScore standardScore = standardSolution.getScore();
            
            LOGGER.info("✅ [Level 2] 标准模式完成 - 耗时: " + standardTime + "ms, 分数: " + standardScore);
            
            // 🆕 发送中间结果（包含实际排班数据）
            int standardAssignmentCount = standardSolution.getExamAssignments() != null ? standardSolution.getExamAssignments().size() : 0;
            LOGGER.info("📡 [Level 2] 准备发送中间结果，包含 " + standardAssignmentCount + " 个排班分配");
            
            // 🔧 修复：转换为DTO避免序列化问题
            ScheduleProgressWebSocket.sendIntermediateResult(sessionId,
                new ScheduleProgressWebSocket.IntermediateResult(
                    standardScore.toString(),
                    standardAssignmentCount,
                    0.85,  // 标准模式置信度85%
                    assessSolutionQuality(standardScore),
                    flashTime + standardTime,
                    AssignmentMapper.toDTOList(standardSolution.getExamAssignments())  // 🔧 使用DTO避免循环引用
                )
            );
            
            LOGGER.info("✅ [Level 2] 已发送标准模式中间结果到前端 (sessionId: " + sessionId + ")");
            
            bestSolution = standardSolution;
            finalLevel = "standard";
            
            // 检查是否需要升级
            HardSoftLongScore standardScoreLong = HardSoftLongScore.of(
                standardScore.hardScore(), 
                standardScore.softScore()
            );
            
            if (!adaptiveSolverConfig.shouldUpgrade(standardScoreLong, "standard")) {
                LOGGER.info("🎉 [Level 2] 标准模式结果优秀，无需升级");
                long totalTime = flashTime + standardTime;
                return buildAdaptiveResponse(bestSolution, finalLevel, totalTime, overallStartTime, sessionId);
            }
            
            // �� Level 3: 精细模式（30-60秒）
            LOGGER.info("🏆 [Level 3] 升级到精细模式 - 目标: 30-60秒最优解");
            
            // 发送级别升级通知
            ScheduleProgressWebSocket.sendLevelUpgrade(sessionId,
                new ScheduleProgressWebSocket.LevelUpgrade(
                    2, 3, "标准模式", "精细模式",
                    "标准模式结果需要进一步优化，升级到精细模式以获得最优解",
                    standardScore.toString()
                )
            );
            
            long preciseStart = System.currentTimeMillis();
            
            SolverConfig preciseConfig = adaptiveSolverConfig.createPreciseConfig();
            SolverFactory<ExamSchedule> preciseFactory = SolverFactory.create(preciseConfig);
            Solver<ExamSchedule> preciseSolver = preciseFactory.buildSolver();
            
            ExamSchedule preciseSolution = preciseSolver.solve(problem);
            long preciseTime = System.currentTimeMillis() - preciseStart;
            HardSoftScore preciseScore = preciseSolution.getScore();
            
            LOGGER.info("✅ [Level 3] 精细模式完成 - 耗时: " + preciseTime + "ms, 分数: " + preciseScore);
            
            // 🆕 发送最终中间结果（包含实际排班数据）
            // 🔧 修复：转换为DTO避免序列化问题
            ScheduleProgressWebSocket.sendIntermediateResult(sessionId,
                new ScheduleProgressWebSocket.IntermediateResult(
                    preciseScore.toString(),
                    preciseSolution.getExamAssignments() != null ? preciseSolution.getExamAssignments().size() : 0,
                    0.95,  // 精细模式置信度95%
                    assessSolutionQuality(preciseScore),
                    flashTime + standardTime + preciseTime,
                    AssignmentMapper.toDTOList(preciseSolution.getExamAssignments())  // 🔧 使用DTO避免循环引用
                )
            );
            
            int preciseAssignmentCount = preciseSolution.getExamAssignments() != null ? preciseSolution.getExamAssignments().size() : 0;
            LOGGER.info("📡 [WebSocket] 已发送精细模式中间结果（包含 " + preciseAssignmentCount + " 个排班）");
            
            bestSolution = preciseSolution;
            finalLevel = "precise";
            
            long totalTime = flashTime + standardTime + preciseTime;
            return buildAdaptiveResponse(bestSolution, finalLevel, totalTime, overallStartTime, sessionId);
            
        } catch (Exception e) {
            LOGGER.severe("❌ [分级求解] 求解失败: " + e.getMessage());
            e.printStackTrace();
            
            // 如果有部分结果，返回最好的结果
            if (bestSolution != null) {
                long totalTime = System.currentTimeMillis() - overallStartTime;
                return buildAdaptiveResponse(bestSolution, finalLevel + "_partial", totalTime, overallStartTime, sessionId);
            }
            
            // 完全失败，返回错误
            ScheduleResponse errorResponse = new ScheduleResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("分级求解失败: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 构建自适应求解响应
     * @param sessionId WebSocket会话ID
     */
    private ScheduleResponse buildAdaptiveResponse(
            ExamSchedule solution, 
            String level, 
            long solvingTime,
            long overallStartTime,
            String sessionId) {
        
        ScheduleResponse response = examScheduleService.buildScheduleResponse(solution);
        
        // 添加分级求解信息到统计数据
        ScheduleResponse.ScheduleStatistics stats = response.getStatistics();
        if (stats != null) {
            stats.setSolvingMode("adaptive_" + level);
            stats.setSolvingTimeMillis(solvingTime);
            stats.setSolvingTimeSeconds((int) (solvingTime / 1000));
        }
        
        // 添加质量评估
        HardSoftScore score = solution.getScore();
        String qualityAssessment = assessSolutionQuality(score);
        
        response.setMessage(String.format(
            "自适应求解完成 [%s] - 耗时: %.1f秒, 质量: %s",
            level,
            solvingTime / 1000.0,
            qualityAssessment
        ));
        
        LOGGER.info(String.format(
            "🎯 [分级求解] 最终结果 - 级别: %s, 总耗时: %dms (%.1f秒), 分数: %s, 质量: %s",
            level,
            solvingTime,
            solvingTime / 1000.0,
            score,
            qualityAssessment
        ));
        
        // 发送最终完成消息
        ScheduleProgressWebSocket.sendFinalResult(sessionId, new java.util.HashMap<String, Object>() {{
            put("success", response.isSuccess());
            put("level", level);
            put("score", score.toString());
            put("quality", qualityAssessment);
            put("totalTime", solvingTime);
            put("message", response.getMessage());
        }});
        
        return response;
    }
    
    /**
     * 评估解的质量
     */
    private String assessSolutionQuality(HardSoftScore score) {
        if (score == null) {
            return "未知";
        }
        
        if (score.hardScore() < 0) {
            return "需改进（硬约束未满足）";
        }
        
        long softScore = score.softScore();
        if (softScore >= 0) {
            return "完美（所有约束满足）";
        } else if (softScore >= -20) {
            return "优秀（软约束几乎满足）";
        } else if (softScore >= -100) {
            return "良好（软约束部分满足）";
        } else if (softScore >= -300) {
            return "可接受（基本满足要求）";
        } else {
            return "一般（有改进空间）";
        }
    }
}
