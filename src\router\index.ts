import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'


const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../pages/HomePage2.vue')
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../pages/DashboardPage.vue')
  },
  {
    path: '/teachers',
    name: 'Teachers',
    component: () => import('../pages/TeachersPage.vue')
  },
  {
    path: '/schedules',
    name: 'Schedules',
    component: () => import('../pages/SchedulesPage.vue')
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('../pages/ReportsPage.vue')
  },
  {
    path: '/data-management',
    name: 'DataManagement',
    component: () => import('../pages/DataManagementPage.vue')
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('../pages/StatisticsPage.vue')
  },
  {
    path: '/learning-stats',
    name: 'LearningStats',
    component: () => import('../pages/LearningStatsPage.vue')
  }
]

// 创建路由实例
// 使用Hash模式以支持file://协议（Electron打包后）
const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

export default router
