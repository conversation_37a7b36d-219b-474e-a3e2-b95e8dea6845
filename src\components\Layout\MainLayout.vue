﻿<template>
  <div class="main-layout">
    <!-- 移动端遮罩层 -->
    <div 
      v-if="isMobile && mobileMenuOpen" 
      class="mobile-overlay fixed inset-0 bg-black bg-opacity-50 z-40"
      @click="closeMobileMenu"
    ></div>
    
    <!-- 侧边栏 -->
    <aside 
      class="sidebar" 
      :class="{ 
        'sidebar-collapsed': sidebarCollapsed && !isMobile,
        'mobile-open': isMobile && mobileMenuOpen,
        'mobile-closed': isMobile && !mobileMenuOpen
      }"
    >
      <div class="sidebar-header">
        <div class="logo">
          <img src="/logo.svg" alt="排班系统" class="logo-icon" />
          <span 
            class="logo-text" 
            v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)"
          >
            排班系统
          </span>
        </div>
        <!-- 移动端关闭按钮 -->
        <button 
          v-if="isMobile" 
          class="mobile-close-btn"
          @click="closeMobileMenu"
        >
          <X class="w-5 h-5" />
        </button>
      </div>
      
      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link 
              to="/" 
              class="nav-link" 
              :class="{ active: $route.path === '/' }"
              @click="handleNavClick"
            >
              <Home class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">首页概览</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              to="/dashboard" 
              class="nav-link" 
              :class="{ active: $route.path === '/dashboard' }"
              @click="handleNavClick"
            >
              <Calendar class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">今日排班</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              to="/teachers" 
              class="nav-link" 
              :class="{ active: $route.path.startsWith('/teachers') }"
              @click="handleNavClick"
            >
              <Users class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">考官管理</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              to="/schedules" 
              class="nav-link" 
              :class="{ active: $route.path.startsWith('/schedules') }"
              @click="handleNavClick"
            >
              <Calendar class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">排班管理</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              to="/reports" 
              class="nav-link" 
              :class="{ active: $route.path.startsWith('/reports') }"
              @click="handleNavClick"
            >
              <BarChart3 class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">报表统计</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              to="/statistics" 
              class="nav-link" 
              :class="{ active: $route.path === '/statistics' }"
              @click="handleNavClick"
            >
              <BarChart3 class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">数据统计</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              to="/learning-stats" 
              class="nav-link" 
              :class="{ active: $route.path.startsWith('/learning-stats') }"
              @click="handleNavClick"
            >
              <Brain class="nav-icon" />
              <span v-show="!sidebarCollapsed || (isMobile && mobileMenuOpen)">学习统计</span>
            </router-link>
          </li>
        </ul>
      </nav>
      
      <!-- 侧边栏收缩按钮 (仅桌面端显示) -->
      <div 
        v-if="!isMobile" 
        class="sidebar-toggle" 
        @click="toggleSidebar"
      >
        <ChevronLeft class="toggle-icon" :class="{ 'rotate-180': sidebarCollapsed }" />
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main 
      class="main-content"
      :class="{
        'sidebar-collapsed': sidebarCollapsed && !isMobile,
        'mobile-layout': isMobile
      }"
    >
      <header class="main-header">
        <div class="header-left">
          <!-- 移动端菜单按钮 -->
          <button 
            v-if="isMobile" 
            class="mobile-menu-btn"
            @click="openMobileMenu"
          >
            <Menu class="w-6 h-6" />
          </button>
          <h1 class="page-title">{{ pageTitle }}</h1>
        </div>
        <div class="header-right">
          <div class="user-info">
            <User class="user-icon" />
            <span class="user-name" v-show="!isMobile || isTablet">管理员</span>
          </div>
        </div>
      </header>
      
      <div class="content-wrapper">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Home, Calendar, Users, BarChart3, User, Menu, X, ChevronLeft, Brain } from 'lucide-vue-next'
import { useResponsive } from '../../composables/useResponsive'

const route = useRoute()

// 使用响应式组合式函数
const { 
  isMobile, 
  isTablet, 
  isDesktop,
  windowWidth,
  sidebarWidth,
  contentPadding,
  navigationConfig 
} = useResponsive()

// 响应式状态
const sidebarCollapsed = ref(false)
const mobileMenuOpen = ref(false)

// 页面标题
const pageTitle = computed(() => {
  switch (route.path) {
    case '/':
      return '系统概览'
    case '/dashboard':
      return '今日排班'
    case '/teachers':
      return '考官管理'
    case '/schedules':
      return '排班管理'
    case '/reports':
      return '报表统计'
    case '/statistics':
      return '数据统计'
    default:
      if (route.path.startsWith('/teachers')) return '考官管理'
      if (route.path.startsWith('/schedules')) return '排班管理'
      if (route.path.startsWith('/reports')) return '报表统计'
      if (route.path.startsWith('/statistics')) return '数据统计'
      return '考官自动排班系统'
  }
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const openMobileMenu = () => {
  mobileMenuOpen.value = true
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const handleNavClick = () => {
  if (isMobile.value) {
    closeMobileMenu()
  }
}

// 监听窗口宽度变化，自动管理移动菜单状态
watch(windowWidth, (newWidth) => {
  // 当从移动端切换到桌面端时，关闭移动菜单
  if (newWidth >= 768) {
    mobileMenuOpen.value = false
  }
  // 自动调整侧边栏收缩状态
  if (newWidth >= 1280 && !isMobile.value) {
    sidebarCollapsed.value = false
  }
})

onMounted(() => {
  // 初始化侧边栏状态
  if (windowWidth.value >= 1280) {
    sidebarCollapsed.value = false
  } else if (windowWidth.value < 1024 && windowWidth.value >= 768) {
    sidebarCollapsed.value = true
  }
})
</script>

<style scoped>
.main-layout {
  @apply flex h-screen bg-gray-50 relative;
  overflow: hidden;
}

.mobile-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  animation: fadeIn 0.2s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar {
  @apply bg-white shadow-lg flex flex-col transition-all duration-300 ease-in-out z-50;
  width: var(--sidebar-width);
  max-width: var(--sidebar-width);
  flex-shrink: 0;
}

.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
  max-width: var(--sidebar-collapsed-width);
}

.mobile-closed {
  @apply fixed left-0 top-0 h-full;
  transform: translateX(-100%);
  width: 280px;
  max-width: 85vw;
}

.mobile-open {
  @apply fixed left-0 top-0 h-full;
  transform: translateX(0);
  width: 280px;
  max-width: 85vw;
}

.sidebar-header {
  @apply pt-4 px-6 pb-3 border-b border-gray-200 flex items-center justify-between;
}

.logo {
  @apply flex items-center min-w-0;
}

.logo-icon {
  @apply w-8 h-8 flex-shrink-0;
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-text {
  @apply text-xl font-bold text-gray-800 ml-3 truncate;
}

.mobile-close-btn {
  @apply p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors;
}

.sidebar-nav {
  @apply flex-1 pt-2 pb-4 overflow-y-auto;
}

.nav-list {
  @apply space-y-1;
}

.nav-item {
  @apply px-4;
}

.nav-link {
  @apply flex items-center px-4 py-3 text-gray-600 rounded-lg transition-colors duration-200 hover:bg-blue-50 hover:text-blue-600 min-h-[44px];
}

.nav-link.active {
  @apply bg-blue-100 text-blue-700 font-medium;
}

.nav-icon {
  @apply w-5 h-5 mr-3 flex-shrink-0;
}

.sidebar-toggle {
  @apply p-4 border-t border-gray-200 flex justify-center;
}

.toggle-icon {
  @apply w-5 h-5 text-gray-500 hover:text-gray-700 cursor-pointer transition-transform duration-200;
}

.main-content {
  @apply flex-1 flex flex-col overflow-hidden transition-all duration-300;
  margin-left: 0;
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

.main-content.mobile-layout {
  @apply w-full;
}

.main-header {
  @apply bg-white shadow-sm px-4 md:px-6 py-4 flex items-center justify-between border-b border-gray-200;
  height: var(--header-height);
}

.header-left {
  @apply flex items-center min-w-0 flex-1;
}

.mobile-menu-btn {
  @apply p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors mr-3 flex-shrink-0;
}

.page-title {
  @apply text-xl md:text-2xl font-semibold text-gray-800 truncate;
}

.header-right {
  @apply flex items-center flex-shrink-0;
}

.user-info {
  @apply flex items-center px-3 md:px-4 py-2 bg-gray-100 rounded-lg;
}

.user-icon {
  @apply w-5 h-5 text-gray-600 flex-shrink-0;
}

.user-name {
  @apply text-sm font-medium text-gray-700 ml-2;
}

.content-wrapper {
  @apply flex-1 overflow-auto;
  padding: var(--content-padding);
  /* 防止底部过多空白 */
  display: flex;
  flex-direction: column;
}

.content-wrapper > * {
  flex-shrink: 0;
}

/* 超小屏幕优化 (<640px) */
@media (max-width: 639px) {
  .sidebar {
    width: 100vw;
    max-width: 85vw;
  }
  
  .content-wrapper {
    @apply p-3;
  }
  
  .main-header {
    @apply px-3 py-2;
    height: var(--mobile-header-height);
  }
  
  .page-title {
    @apply text-base;
  }
  
  .user-info {
    @apply px-2 py-1;
  }
  
  .user-name {
    @apply text-xs;
  }
  
  .sidebar-header {
    @apply pt-3 px-4 pb-2;
  }
  
  .logo-text {
    @apply text-lg;
  }
  
  .nav-link {
    @apply px-3 py-2 text-sm min-h-[40px];
  }
}

/* 移动端优化 (640px - 767px) */
@media (min-width: 640px) and (max-width: 767px) {
  .content-wrapper {
    @apply p-4;
  }
  
  .main-header {
    @apply px-4 py-3;
    height: var(--mobile-header-height);
  }
  
  .page-title {
    @apply text-lg;
  }
}

/* 平板端优化 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar {
    width: 240px;
  }
  
  .sidebar-collapsed {
    width: 70px;
  }
  
  .content-wrapper {
    @apply p-5;
  }
  
  .main-header {
    @apply px-5;
  }
  
  .page-title {
    @apply text-xl;
  }
}

/* 桌面端优化 (1024px - 1279px) */
@media (min-width: 1024px) {
  .sidebar {
    @apply relative;
    position: relative !important;
    transform: none !important;
    width: 280px;
  }
  
  .sidebar-collapsed {
    width: 80px;
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .content-wrapper {
    @apply p-6;
  }
}

/* 大桌面优化 (1280px - 1535px) */
@media (min-width: 1280px) and (max-width: 1535px) {
  .content-wrapper {
    @apply p-8;
  }
}

/* 宽屏优化 (1536px - 1919px) */
@media (min-width: 1536px) and (max-width: 1919px) {
  .sidebar {
    width: 320px;
  }
  
  .content-wrapper {
    padding: 40px;
  }
}

/* 超宽屏优化 (≥1920px) */
@media (min-width: 1920px) {
  .sidebar {
    width: 360px;
  }
  
  .sidebar-collapsed {
    width: 90px;
  }
  
  .content-wrapper {
    padding: 48px;
  }
  
  .logo-text {
    @apply text-2xl;
  }
  
  .nav-link {
    @apply text-base;
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .main-content,
  .nav-link,
  .toggle-icon,
  .mobile-overlay {
    @apply transition-none;
    animation: none;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .nav-link {
    min-height: 48px;
  }
  
  .mobile-menu-btn,
  .mobile-close-btn {
    min-width: 48px;
    min-height: 48px;
  }
  
  .sidebar-toggle {
    width: 32px;
    height: 32px;
  }
}

/* 横屏移动设备 */
@media (max-width: 920px) and (orientation: landscape) {
  .sidebar {
    width: 240px;
  }
  
  .mobile-open {
    width: 240px;
    max-width: 40vw;
  }
  
  .content-wrapper {
    @apply p-4;
  }
}</style>