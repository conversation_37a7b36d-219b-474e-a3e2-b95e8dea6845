# OptaPlanner 考试排班微服务

这是一个基于OptaPlanner和Quarkus的考试排班微服务，用于替换原有的JavaScript排班算法。

## 功能特性

- 🚀 **高性能**: 使用OptaPlanner AI约束求解器，提供更优的排班结果
- 📋 **完整约束**: 实现9个硬约束和12个软约束
- 🔄 **实时求解**: 支持同步和异步排班计算
- 📊 **详细统计**: 提供完整的排班统计和约束违反信息
- 🌐 **REST API**: 标准的RESTful接口，易于集成

## 技术栈

- **OptaPlanner 9.44.0**: AI约束求解引擎
- **Quarkus 3.6.0**: 云原生Java框架
- **Java 17**: 运行环境
- **Maven**: 构建工具

## 快速开始

### 前置条件

- Java 17 或更高版本
- Maven 3.6 或更高版本

### 启动服务

```bash
# 开发模式启动
mvn clean compile quarkus:dev

# 生产模式构建
mvn clean package
java -jar target/quarkus-app/quarkus-run.jar
```

服务将在 `http://localhost:8081` 启动。

## API 接口

### 健康检查

```http
GET /api/schedule/health
```

### 生成排班计划

```http
POST /api/schedule/generate
Content-Type: application/json

{
  "students": [
    {
      "id": "1",
      "name": "张三",
      "department": "一",
      "group": "一组"
    }
  ],
  "teachers": [
    {
      "id": "1",
      "name": "李老师",
      "department": "一",
      "group": "二组"
    }
  ],
  "startDate": "2025-01-15",
  "endDate": "2025-01-30",
  "constraints": {
    "noWeekendExam": true,
    "consecutiveExamWeight": 30,
    "balanceWorkloadWeight": 30
  }
}
```

### 异步生成排班

```http
POST /api/schedule/generate-async
```

### 查询求解状态

```http
GET /api/schedule/status/{problemId}
```

### 验证排班结果

```http
POST /api/schedule/validate
```

## 约束说明

### 硬约束 (必须满足)

1. **HC1**: 周六、周日不安排考试
2. **HC2**: 法定节假日不安排考试
3. **HC3**: 每个学员必须在2个工作日完成全部考试
4. **HC4**: 任何考官不能是其考试当天执勤白班的考官
5. **HC5**: 学员进行现场考试时，不能安排在学员本班组执勤白班的时间
6. **HC6**: 考官1必须与学员同科室
7. **HC7**: 考官2必须与学员不同科室
8. **HC8**: 备份考官必须与学员不同科室

### 软约束 (尽量满足)

1. **SC2**: 考官2安排优先级 - 优先安排推荐部门的考官担任考官2角色
2. **SC3**: 备份考官安排优先级 - 优先安排不同部门的考官作为备份考官
3. **SC4**: 区域协作鼓励 - 允许区域3和区域7的考官相互替换
4. **SC5**: 考官工作量均衡 - 平衡各考官的工作量分配
5. **SC6**: 考试日期分配均衡 - 平衡不同日期的考试安排
6. **SC10**: 晚班考官优先级（第一优先级） - 优先安排执勤晚班的考官
7. **SC11**: 第一休息日考官优先级（第二优先级） - 优先安排第一休息日的考官
8. **SC12**: 第二休息日考官优先级（第三优先级） - 次优安排第二休息日的考官
9. **SC1_ADMIN**: 行政班考官优先级（第四优先级） - 合理安排行政班考官
10. **SC13**: 晚班考官推荐科室加分 - 为晚班考官在推荐科室提供额外加分

## 配置说明

### 求解器配置

- **终止时间**: 默认30秒
- **算法**: 禁忌搜索 (Tabu Search)
- **构造启发式**: First Fit Decreasing

### 约束权重

可以通过请求参数调整软约束的权重，权重越高优先级越高。

## 性能优化

- 使用Quarkus原生编译可显著提升启动速度
- 支持多线程并行求解
- 内置缓存机制减少重复计算
- 智能约束验证提高求解效率

## 监控和日志

- 健康检查: `/health`
- 指标监控: `/metrics` (Prometheus格式)
- 日志级别可通过配置文件调整

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查Java版本是否为17+
   - 确认端口8081未被占用

2. **排班结果不理想**
   - 调整约束权重
   - 增加求解时间
   - 检查输入数据质量

3. **性能问题**
   - 启用原生编译
   - 调整JVM参数
   - 优化约束配置

## 开发说明

### 项目结构

```
src/main/java/com/examiner/scheduler/
├── domain/          # 领域模型
├── solver/          # 约束提供者
├── rest/           # REST接口
└── service/        # 业务服务
```

### 扩展开发

1. 添加新约束: 在 `OptimizedExamScheduleConstraintProvider` 中定义
2. 修改领域模型: 更新 `domain` 包中的类
3. 调整求解配置: 修改 `solverConfig.xml`

## 许可证

MIT License