<?xml version="1.0" encoding="UTF-8"?>
<solver>

    <!-- 解决方案类 -->
    <solutionClass>com.examiner.scheduler.domain.ExamSchedule</solutionClass>
    
    <!-- 规划实体类 -->
    <entityClass>com.examiner.scheduler.domain.ExamAssignment</entityClass>
    
    <!-- 约束提供者 -->
    <scoreDirectorFactory>
        <constraintProviderClass>com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider</constraintProviderClass>
    </scoreDirectorFactory>
    
    <!-- 🔧 优化求解配置 - 延长到90秒以改善软约束 -->
    <termination>
        <or>
            <!-- 条件1：最大求解时间90秒 -->
            <spentLimit>PT90S</spentLimit>
            <!-- 条件2：15秒内没有改进就停止 -->
            <unimprovedSpentLimit>PT20S</unimprovedSpentLimit>
            <!-- 🔧 关键修复：移除bestScoreLimit，避免过早终止 -->
            <!-- 让求解器充分验证所有硬约束，避免返回"伪完美"解 -->
        </or>
    </termination>
    
    <!-- 🔧 改进的构造启发式算法 -->
    <constructionHeuristic>
        <!-- 使用FIRST_FIT_DECREASING，但允许更多探索 -->
        <constructionHeuristicType>FIRST_FIT_DECREASING</constructionHeuristicType>
        <forager>
            <!-- 🔧 提高到80，允许更充分的初始方案探索 -->
            <acceptedCountLimit>80</acceptedCountLimit>
        </forager>
    </constructionHeuristic>
    
    <!-- 🔧 改进的局部搜索算法 -->
    <localSearch>
        <!-- 使用完整的移动选择器以探索更多可能 -->
        <unionMoveSelector>
            <changeMoveSelector/>
            <!-- 🔧 添加交换选择器以探索更好的考官组合 -->
            <swapMoveSelector/>
        </unionMoveSelector>
        <acceptor>
            <!-- 🔧 增大禁忌表以避免早期收敛和循环 -->
            <entityTabuSize>9</entityTabuSize>
        </acceptor>
        <forager>
            <!-- 🔧 提高到1500，允许更充分的局部搜索（配合90秒求解时间） -->
            <acceptedCountLimit>1500</acceptedCountLimit>
        </forager>
    </localSearch>
    
</solver>