# Requirements Document

## Introduction

本需求文档旨在检查和完善排班表实时显示计算结果的功能。根据现有文档（`REALTIME_SCHEDULE_UPDATE.md`），该功能应该允许用户在排班计算过程中实时看到后端OptaPlanner的中间计算结果，而不是等待30秒-3分钟后才看到最终结果。

该功能通过WebSocket实现前后端实时通信，在三个优化阶段（闪电模式70%、标准模式85%、精细模式95%）分别推送中间结果到前端，并在表格中实时显示，提供视觉反馈（蓝色横幅、边框闪烁动画等）。

## Requirements

### Requirement 1: 后端WebSocket中间结果推送

**User Story:** 作为后端开发者，我希望在每个优化阶段完成时通过WebSocket推送包含完整排班数据的中间结果，以便前端能够实时显示排班表格。

#### Acceptance Criteria

1. WHEN 闪电模式（Level 1）求解完成 THEN 系统 SHALL 通过WebSocket发送包含assignments数组的IntermediateResult消息
2. WHEN 标准模式（Level 2）求解完成 THEN 系统 SHALL 通过WebSocket发送包含assignments数组的IntermediateResult消息
3. WHEN 精细模式（Level 3）求解完成 THEN 系统 SHALL 通过WebSocket发送包含assignments数组的IntermediateResult消息
4. IF IntermediateResult被发送 THEN 消息 SHALL 包含以下字段：score、assignmentCount、confidence、quality、elapsedTime、assignments
5. WHEN assignments数据被发送 THEN 每个assignment对象 SHALL 包含完整的学员信息（student对象）和考官信息（examiner1、examiner2、backupExaminer）
6. WHEN 中间结果发送成功 THEN 后端日志 SHALL 记录"📡 [WebSocket] 已发送XX模式中间结果（包含 N 个排班）"

### Requirement 2: 前端WebSocket连接和监听

**User Story:** 作为前端开发者，我希望在用户点击"开始排班"时自动连接WebSocket并监听中间结果消息，以便能够接收并处理后端推送的实时数据。

#### Acceptance Criteria

1. WHEN 用户点击"开始排班"按钮 THEN 系统 SHALL 获取sessionId并立即建立WebSocket连接
2. WHEN WebSocket连接建立成功 THEN 系统 SHALL 注册onProgress回调函数监听消息
3. WHEN 收到type为"intermediate_result"的消息 THEN 系统 SHALL 检查message.data.assignments是否存在
4. IF message.data.assignments存在 THEN 系统 SHALL 将assignments数据转换为表格格式
5. WHEN 数据转换完成 THEN 系统 SHALL 更新scheduleResults响应式变量触发表格重新渲染
6. WHEN WebSocket连接失败 THEN 系统 SHALL 记录错误日志并尝试重连（最多3次）
7. WHEN 排班完成或用户离开页面 THEN 系统 SHALL 正确关闭WebSocket连接避免内存泄漏

### Requirement 3: 实时更新视觉反馈

**User Story:** 作为用户，我希望在排班计算过程中看到明显的视觉反馈，以便知道系统正在工作并且表格正在更新。

#### Acceptance Criteria

1. WHEN 表格开始更新 THEN 系统 SHALL 在表格上方显示蓝色横幅提示"正在实时更新排班结果..."
2. WHEN 表格正在更新 THEN 表格容器 SHALL 添加蓝色边框并显示脉冲动画效果
3. WHEN 表格更新完成 THEN 蓝色边框动画 SHALL 在300ms后消失
4. WHEN 收到中间结果 THEN 页面右上角 SHALL 显示"最后更新: HH:MM:SS"时间戳
5. WHEN 实时进度窗口打开 THEN 系统 SHALL 在日志中记录"🔄 实时更新: 收到XX%置信度的中间结果"
6. IF 用户点击"开始排班" THEN 实时进度窗口 SHALL 自动打开且不最小化

### Requirement 4: 数据格式转换和兼容性

**User Story:** 作为系统，我需要正确处理后端发送的多种可能的数据格式，以便在不同情况下都能正确显示排班结果。

#### Acceptance Criteria

1. WHEN 转换assignments数据 THEN 系统 SHALL 支持多种数据结构（assignment.student.id 或 assignment.studentId）
2. WHEN 转换assignments数据 THEN 系统 SHALL 正确提取学员信息（id、name、department）
3. WHEN 转换assignments数据 THEN 系统 SHALL 正确提取考官信息（examiner1、examiner2、backupExaminer的id和name）
4. WHEN 转换assignments数据 THEN 系统 SHALL 使用现有的convertOptaPlannerResultToTableFormat函数确保格式一致
5. IF 数据字段缺失 THEN 系统 SHALL 使用空字符串或默认值避免显示错误
6. WHEN 数据转换过程中出现错误 THEN 系统 SHALL 记录详细错误日志并继续处理其他数据

### Requirement 5: 性能和用户体验优化

**User Story:** 作为用户，我希望实时更新功能不会影响系统性能，并且更新过程流畅自然。

#### Acceptance Criteria

1. WHEN 表格数据更新 THEN 更新动画时长 SHALL 不超过300ms
2. WHEN 收到多个快速连续的中间结果 THEN 系统 SHALL 使用节流机制避免过度渲染
3. WHEN 学员数量超过50人 THEN 系统 SHALL 考虑使用虚拟滚动或分页避免性能问题
4. WHEN 表格更新 THEN 用户当前的滚动位置 SHALL 保持不变
5. WHEN 最终结果到达 THEN 系统 SHALL 清除"更新中"状态并停止所有动画
6. IF WebSocket消息处理失败 THEN 系统 SHALL 不影响用户继续使用其他功能

### Requirement 6: 调试和可维护性

**User Story:** 作为开发者，我希望有完善的日志记录和调试信息，以便快速定位和解决问题。

#### Acceptance Criteria

1. WHEN WebSocket连接建立 THEN 控制台 SHALL 输出"📡 [实时更新] 连接WebSocket: sessionId"
2. WHEN 收到中间结果 THEN 控制台 SHALL 输出"📊 [实时更新] 收到中间结果，排班数量: N"
3. WHEN 表格更新完成 THEN 控制台 SHALL 输出"✅ [实时更新] 表格已更新，显示 N 行数据"
4. WHEN 数据转换过程 THEN 控制台 SHALL 输出前3个assignment的详细映射信息用于调试
5. IF 发生错误 THEN 控制台 SHALL 输出"❌ [实时更新] XX失败: 错误详情"
6. WHEN 后端发送中间结果 THEN 后端日志 SHALL 包含发送的排班数量和置信度信息

### Requirement 7: 功能完整性验证

**User Story:** 作为测试人员，我需要能够验证实时更新功能的各个环节是否正常工作。

#### Acceptance Criteria

1. WHEN 点击"开始排班" THEN 实时进度窗口 SHALL 自动打开
2. WHEN 5-10秒后 THEN 表格 SHALL 显示初步排班结果（闪电模式）
3. WHEN 15-20秒后 THEN 表格中的考官名字 SHALL 发生变化（标准模式）
4. WHEN 30-45秒后 THEN 表格 SHALL 显示最终优化结果（精细模式）
5. WHEN 每次更新 THEN 蓝色横幅和边框动画 SHALL 短暂出现
6. WHEN 排班完成 THEN 所有视觉反馈 SHALL 消失，表格显示最终结果
7. IF 功能正常 THEN 控制台日志 SHALL 包含完整的连接、接收、更新流程记录
