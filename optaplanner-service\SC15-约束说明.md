# SC15约束：鼓励同一学员两天考试使用不同考官1

## 📋 约束概述

**约束ID**: SC15  
**约束类型**: 软约束（Soft Constraint）  
**权重**: 60（中等优先级）  
**实施日期**: 2025-10-08

## 🎯 业务目标

鼓励系统在资源充足的情况下，为同一学员的两天考试分配不同的考官1，以实现：

1. **减少考官工作压力** - 避免同一考官连续两天监考同一学员
2. **提升评审质量** - 让学员体验不同考官的评审风格和标准
3. **优化资源分配** - 更均衡地分配考官工作量

## 🔧 技术实现

### 约束逻辑

```java
/**
 * SC15: 鼓励同一学员两天考试使用不同考官1
 * 
 * 实现方式：
 * - 使用join将同一学员的Day1和Day2 assignment配对
 * - 检查两个考官1是否为同一人
 * - 如果是同一人，给予惩罚（鼓励使用不同考官）
 * 
 * 权重：60（中等优先级）
 */
```

### 约束触发条件

当满足以下条件时，约束会给予 **-60分** 的惩罚：

1. 同一学员的Day1和Day2考试
2. 两天的考官1都已分配
3. 两天的考官1为同一人

### 约束不触发条件

以下情况约束不会触发（不惩罚）：

1. 考官1未完全分配
2. 两天使用了不同的考官1 ✅

## 📊 约束优先级

在所有软约束中的位置：

| 优先级 | 约束ID | 约束名称 | 权重 |
|--------|--------|----------|------|
| 1 | SC10 | 工作量均衡+连续工作惩罚 | 400 |
| 2 | SC11 | 日期分配均衡 | 300 |
| 3 | SC1 | 晚班考官优先 | 150 |
| 4 | SC3 | 休息第一天考官优先 | 120 |
| 5 | SC14 | Day1/Day2考官二科室互斥 | 110 |
| 6 | SC2 | 考官2专业匹配 | 100 |
| 7 | SC4 | 备份考官专业匹配 | 80 |
| **8** | **SC15** | **鼓励考官1多样性** | **60** ⬅️ 新增 |
| 9 | SC7 | 行政班备份考官优先 | 60 |
| 10 | SC5 | 休息第二天考官优先 | 40 |
| ... | ... | ... | ... |

## ⚖️ 与硬约束的关系

### SC15不会违反任何硬约束

- ✅ **HC2（考官1与学员同科室）** - SC15不改变考官1的科室要求，只是鼓励使用不同的人
- ✅ **HC3（考官执勤白班限制）** - 两个不同的考官1都必须满足白班限制
- ✅ **HC4（每名考官每天只能监考一名考生）** - 使用不同考官1反而有助于满足此约束
- ✅ **HC7（考官1和考官2不同科室）** - 不影响考官2的选择

### 资源不足时的行为

如果满足以下情况，OptaPlanner会**忽略SC15约束**，继续使用同一个考官1：

1. 只有一个符合HC2要求的考官1可用
2. 使用不同考官1会导致HC3违反（白班执勤冲突）
3. 使用不同考官1会导致HC4违反（考官时间冲突）
4. 更换考官1会导致整体分数下降（其他高权重约束受影响）

## 📈 预期效果

### 正面影响

1. **减少连续工作** - 考官不需要连续两天监考同一学员
2. **工作量更均衡** - 更多考官参与排班，工作量分散
3. **评审多样性** - 学员接触不同考官的评审风格

### 可能的权衡

1. **资源消耗增加** - 需要2个考官1而不是1个（如果资源充足）
2. **优化时间略增** - OptaPlanner需要考虑更多的考官组合

## 🔍 日志示例

### 约束触发（惩罚）

```
⚠️ [SC15检测] 学员 张三 两天考试使用同一考官1: 李老师 (科室:一)
```

### 约束满足（无惩罚）

```
✅ [SC15满足] 学员 张三 两天考试使用不同考官1: Day1=李老师 vs Day2=王老师
```

## 🧪 测试建议

### 测试场景1：资源充足

**输入**:
- 学员：张三（科室：一）
- 可用考官1：李老师、王老师、赵老师（都是科室一）
- 两天都没有白班执勤冲突

**预期结果**:
- Day1考官1：李老师
- Day2考官1：王老师（不同于Day1）
- SC15约束满足，无惩罚

### 测试场景2：资源紧张

**输入**:
- 学员：张三（科室：一）
- 可用考官1：只有李老师（科室一）
- 其他科室一的考官都有白班执勤冲突

**预期结果**:
- Day1考官1：李老师
- Day2考官1：李老师（同一人）
- SC15约束触发，-60分惩罚
- 但由于没有其他选择，系统接受此惩罚

### 测试场景3：权衡其他约束

**输入**:
- 学员：张三（科室：一）
- 可用考官1：李老师（晚班）、王老师（白班）
- SC1权重150 > SC15权重60

**预期结果**:
- Day1考官1：李老师（晚班，SC1奖励+150）
- Day2考官1：李老师（晚班，SC1奖励+150）
- SC15约束触发，-60分惩罚
- 总分：+150+150-60 = +240（仍然是正收益）

## 📝 配置说明

### 启用/禁用约束

在统一约束配置中：

```json
{
  "softConstraints": {
    "SC15": {
      "id": "SC15",
      "name": "鼓励考官1多样性",
      "description": "鼓励同一学员两天考试使用不同考官1",
      "weight": 60,
      "status": "ENABLED"
    }
  }
}
```

### 调整权重

如果需要调整SC15的优先级：

- **提高权重（如80）** - 更强烈地鼓励使用不同考官1
- **降低权重（如40）** - 减弱此约束的影响，允许更多情况下使用同一考官1
- **设为0** - 实际上禁用此约束

## 🔄 与现有系统的兼容性

### 初始解生成

- ✅ **不影响** - 初始解仍然为两天分配同一个考官1
- ✅ **OptaPlanner优化** - 在优化阶段，OptaPlanner会尝试更换考官1以满足SC15

### 现有约束

- ✅ **完全兼容** - 不与任何现有硬约束或软约束冲突
- ✅ **协同工作** - 与SC10（工作量均衡）协同，共同优化考官分配

### 数据结构

- ✅ **无需修改** - 不需要修改Student、Teacher或ExamAssignment数据结构
- ✅ **向后兼容** - 旧的排班数据仍然有效

## 📞 支持信息

**实施版本**: 1.0.0  
**实施日期**: 2025-10-08  
**负责人**: 系统开发团队  
**文档版本**: 1.0

---

© 2025 考官排班系统 - SC15约束说明文档

