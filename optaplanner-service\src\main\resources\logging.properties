# =======================================================================
# OptaPlanner 考试排班系统 - 日志配置
# 为约束条件执行和算法监控提供清晰明了的日志输出
# =======================================================================

# 全局日志级别
.level = INFO

# 根日志处理器
handlers = java.util.logging.ConsoleHandler, java.util.logging.FileHandler

# 控制台处理器配置
java.util.logging.ConsoleHandler.level = INFO
java.util.logging.ConsoleHandler.formatter = java.util.logging.SimpleFormatter

# 文件处理器配置
java.util.logging.FileHandler.level = ALL
java.util.logging.FileHandler.pattern = logs/optaplanner-constraints-%u.log
java.util.logging.FileHandler.formatter = java.util.logging.SimpleFormatter
java.util.logging.FileHandler.append = true
java.util.logging.FileHandler.limit = 10485760

# 格式化器配置 - 增加时间戳和线程信息
java.util.logging.SimpleFormatter.format = %1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS.%1$tL [%4$s] %3$s: %5$s%6$s%n

# =======================================================================
# 约束系统专项日志配置
# =======================================================================

# 约束提供者 - 显示约束执行的详细过程
com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider.level = INFO

# SC5 和 SC6 约束详细调试 (可以设置为DEBUG查看更多细节)
# com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider.level = DEBUG

# 排班服务 - 显示算法执行过程
com.examiner.scheduler.rest.ExamScheduleResource.level = INFO

# 约束验证服务 - 显示约束违反检测
com.examiner.service.UnifiedConstraintValidationService.level = INFO
com.examiner.service.ConstraintViolationReportService.level = INFO

# 性能监控服务
com.examiner.service.ConstraintPerformanceMonitoringService.level = INFO

# =======================================================================
# OptaPlanner 框架日志配置
# =======================================================================

# OptaPlanner 核心日志
org.optaplanner.level = INFO

# 求解器执行日志
org.optaplanner.core.impl.solver.DefaultSolver.level = INFO

# 约束匹配日志
org.optaplanner.core.impl.score.stream.level = INFO

# 构造启发式日志
org.optaplanner.core.impl.constructionheuristic.level = INFO

# 局部搜索日志
org.optaplanner.core.impl.localsearch.level = INFO

# =======================================================================
# 业务领域日志配置
# =======================================================================

# 考试分配领域
com.examiner.scheduler.domain.level = INFO

# 约束配置
com.examiner.scheduler.config.level = INFO

# 数据服务
com.examiner.scheduler.service.level = INFO

# =======================================================================
# 第三方框架日志配置
# =======================================================================

# Hibernate (如果使用)
org.hibernate.level = WARN

# Spring (如果使用)
org.springframework.level = WARN

# JAX-RS
javax.ws.rs.level = INFO

# =======================================================================
# 特殊调试配置
# =======================================================================

# 如需查看约束计算的详细过程，取消注释以下行：
# com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider.level = DEBUG

# 如需查看权重计算过程，取消注释以下行：
# com.examiner.scheduler.solver.level = DEBUG

# 如需查看统计信息的详细过程，取消注释以下行：
# com.examiner.service.level = DEBUG
