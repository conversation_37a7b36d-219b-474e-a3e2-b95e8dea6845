﻿<template>
  <div class="data-management-panel">
    <!-- 数据管理导航 -->
    <div class="management-header">
      <h2>📊 数据管理中心</h2>
      <p>管理考官、学员、值班等基础数据，支持批量导入和实时同步</p>
      
      <div class="tab-navigation">
        <button 
          v-for="tab in tabs" 
          :key="tab.key"
          class="tab-button"
          :class="{ 'active': currentTab === tab.key }"
          @click="currentTab = tab.key"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-label">{{ tab.label }}</span>
          <span v-if="tab.count !== undefined" class="tab-count">{{ tab.count }}</span>
        </button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <div class="stat-card">
        <div class="stat-icon">👨‍🏫</div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.totalTeachers }}</div>
          <div class="stat-label">活跃考官</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">👨‍🎓</div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.totalStudents }}</div>
          <div class="stat-label">活跃学员</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.totalAssignments }}</div>
          <div class="stat-label">考试分配</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.completionRate.toFixed(1) }}%</div>
          <div class="stat-label">完成率</div>
        </div>
      </div>
    </div>

    <!-- 考官管理 -->
    <div v-if="currentTab === 'teachers'" class="tab-content">
      <div class="content-header">
        <h3>👨‍🏫 考官管理</h3>
        <div class="action-buttons">
          <button class="btn btn-primary" @click="showAddTeacherModal = true">
            <span class="btn-icon">➕</span>
            添加考官
          </button>
          <button class="btn btn-secondary" @click="showImportTeachersModal = true">
            <span class="btn-icon">📤</span>
            批量导入
          </button>
          <button class="btn btn-outline" @click="refreshTeachers">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
        </div>
      </div>

      <div class="data-table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>考官ID</th>
              <th>姓名</th>
              <th>科室</th>
              <th>班组</th>
              <th>工作负荷</th>
              <th>连续天数</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="teacher in teachers" :key="teacher.id">
              <td>{{ teacher.teacherId }}</td>
              <td>{{ teacher.name }}</td>
              <td>{{ teacher.department?.name }}</td>
              <td>{{ teacher.group?.name || '无' }}</td>
              <td>{{ teacher.workload }}</td>
              <td>{{ teacher.consecutiveDays }}</td>
              <td>
                <span class="status-badge" :class="{ 'active': teacher.isActive, 'inactive': !teacher.isActive }">
                  {{ teacher.isActive ? '活跃' : '停用' }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button class="btn-icon-small" @click="editTeacher(teacher)" title="编辑">
                    ✏️
                  </button>
                  <button class="btn-icon-small danger" @click="deleteTeacher(teacher.id!)" title="删除">
                    🗑️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 学员管理 -->
    <div v-if="currentTab === 'students'" class="tab-content">
      <div class="content-header">
        <h3>👨‍🎓 学员管理</h3>
        <div class="action-buttons">
          <button class="btn btn-primary" @click="showAddStudentModal = true">
            <span class="btn-icon">➕</span>
            添加学员
          </button>
          <button class="btn btn-secondary" @click="showImportStudentsModal = true">
            <span class="btn-icon">📤</span>
            批量导入
          </button>
          <button class="btn btn-outline" @click="refreshStudents">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
        </div>
      </div>

      <div class="data-table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>学员ID</th>
              <th>姓名</th>
              <th>科室</th>
              <th>班组</th>
              <th>推荐考官1科室</th>
              <th>推荐考官2科室</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="student in students" :key="student.id">
              <td>{{ student.studentId }}</td>
              <td>{{ student.name }}</td>
              <td>{{ student.department?.name }}</td>
              <td>{{ student.group?.name }}</td>
              <td>{{ student.recommendedExaminer1Dept?.name || '未设置' }}</td>
              <td>{{ student.recommendedExaminer2Dept?.name || '未设置' }}</td>
              <td>
                <span class="status-badge" :class="{ 'active': student.isActive, 'inactive': !student.isActive }">
                  {{ student.isActive ? '活跃' : '停用' }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button class="btn-icon-small" @click="editStudent(student)" title="编辑">
                    ✏️
                  </button>
                  <button class="btn-icon-small danger" @click="deleteStudent(student.id!)" title="删除">
                    🗑️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 值班管理 -->
    <div v-if="currentTab === 'duties'" class="tab-content">
      <div class="content-header">
        <h3>📅 值班管理</h3>
        <div class="action-buttons">
          <button class="btn btn-secondary" @click="showImportDutiesModal = true">
            <span class="btn-icon">📤</span>
            导入值班数据
          </button>
          <button class="btn btn-outline" @click="refreshDuties">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
        </div>
      </div>

      <div class="date-range-selector">
        <label>查询日期范围：</label>
        <input v-model="dutyStartDate" type="date" class="date-input">
        <span>至</span>
        <input v-model="dutyEndDate" type="date" class="date-input">
        <button class="btn btn-primary" @click="loadDutySchedules">查询</button>
      </div>

      <div class="data-table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>考官</th>
              <th>科室</th>
              <th>值班日期</th>
              <th>班次</th>
              <th>更新时间</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="duty in duties" :key="duty.id">
              <td>{{ duty.teacher?.name }}</td>
              <td>{{ duty.teacher?.department?.name }}</td>
              <td>{{ duty.dutyDate }}</td>
              <td>
                <span class="shift-badge" :class="duty.shiftType.toLowerCase()">
                  {{ getShiftTypeName(duty.shiftType) }}
                </span>
              </td>
              <td>{{ formatDateTime(duty.updatedAt) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 考试分配 -->
    <div v-if="currentTab === 'assignments'" class="tab-content">
      <div class="content-header">
        <h3>📋 考试分配</h3>
        <div class="action-buttons">
          <button class="btn btn-outline" @click="refreshAssignments">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
        </div>
      </div>

      <div class="data-table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>学员</th>
              <th>科室</th>
              <th>考试时间</th>
              <th>考官1</th>
              <th>考官2</th>
              <th>备份考官</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="assignment in assignments" :key="assignment.id">
              <td>{{ assignment.student?.name }}</td>
              <td>{{ assignment.student?.department?.name }}</td>
              <td>{{ formatTimeSlot(assignment.timeSlot) }}</td>
              <td>{{ assignment.examiner1?.name || '未分配' }}</td>
              <td>{{ assignment.examiner2?.name || '未分配' }}</td>
              <td>{{ assignment.backupExaminer?.name || '未分配' }}</td>
              <td>
                <span class="status-badge" :class="assignment.status.toLowerCase()">
                  {{ getAssignmentStatusName(assignment.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>{{ loadingMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { dataManagementApi, type Teacher, type Student, type DutySchedule, type ExamAssignment, type Statistics } from '../services/dataManagementApi'

// 响应式数据
const currentTab = ref('teachers')
const loading = ref(false)
const loadingMessage = ref('加载中...')

// 数据状态
const teachers = ref<Teacher[]>([])
const students = ref<Student[]>([])
const duties = ref<DutySchedule[]>([])
const assignments = ref<ExamAssignment[]>([])
const statistics = ref<Statistics>({
  totalTeachers: 0,
  totalStudents: 0,
  totalAssignments: 0,
  completedAssignments: 0,
  completionRate: 0
})

// 模态框状态
const showAddTeacherModal = ref(false)
const showAddStudentModal = ref(false)
const showImportTeachersModal = ref(false)
const showImportStudentsModal = ref(false)
const showImportDutiesModal = ref(false)

// 值班查询日期范围
const dutyStartDate = ref('')
const dutyEndDate = ref('')

// 标签页配置
const tabs = computed(() => [
  { key: 'teachers', label: '考官管理', icon: '👨‍🏫', count: teachers.value.length },
  { key: 'students', label: '学员管理', icon: '👨‍🎓', count: students.value.length },
  { key: 'duties', label: '值班管理', icon: '📅', count: duties.value.length },
  { key: 'assignments', label: '考试分配', icon: '📋', count: assignments.value.length }
])

// 生命周期
onMounted(async () => {
  await loadInitialData()
})

// 方法
const loadInitialData = async () => {
  loading.value = true
  loadingMessage.value = '加载基础数据...'
  
  try {
    await Promise.all([
      loadStatistics(),
      refreshTeachers(),
      refreshStudents()
    ])
  } catch (error) {
    console.error('加载初始数据失败:', error)
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    statistics.value = await dataManagementApi.getStatistics()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const refreshTeachers = async () => {
  try {
    teachers.value = await dataManagementApi.getAllTeachers()
  } catch (error) {
    console.error('加载考官数据失败:', error)
  }
}

const refreshStudents = async () => {
  try {
    students.value = await dataManagementApi.getAllStudents()
  } catch (error) {
    console.error('加载学员数据失败:', error)
  }
}

const refreshAssignments = async () => {
  try {
    assignments.value = await dataManagementApi.getAllAssignments()
  } catch (error) {
    console.error('加载考试分配失败:', error)
  }
}

const refreshDuties = async () => {
  if (dutyStartDate.value && dutyEndDate.value) {
    await loadDutySchedules()
  }
}

const loadDutySchedules = async () => {
  if (!dutyStartDate.value || !dutyEndDate.value) {
    alert('请选择查询日期范围')
    return
  }
  
  try {
    duties.value = await dataManagementApi.getDutySchedules(dutyStartDate.value, dutyEndDate.value)
  } catch (error) {
    console.error('加载值班数据失败:', error)
  }
}

const editTeacher = (teacher: Teacher) => {
  // TODO: 实现考官编辑功能
  console.log('编辑考官:', teacher)
}

const deleteTeacher = async (teacherId: number) => {
  if (confirm('确定要删除这个考官吗？')) {
    try {
      await dataManagementApi.deleteTeacher(teacherId)
      await refreshTeachers()
      await loadStatistics()
    } catch (error) {
      console.error('删除考官失败:', error)
      alert('删除考官失败')
    }
  }
}

const editStudent = (student: Student) => {
  // TODO: 实现学员编辑功能
  console.log('编辑学员:', student)
}

const deleteStudent = async (studentId: number) => {
  if (confirm('确定要删除这个学员吗？')) {
    try {
      await dataManagementApi.deleteStudent(studentId)
      await refreshStudents()
      await loadStatistics()
    } catch (error) {
      console.error('删除学员失败:', error)
      alert('删除学员失败')
    }
  }
}

// 辅助方法
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '未知'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatTimeSlot = (timeSlot?: any) => {
  if (!timeSlot) return '未分配'
  return `${timeSlot.date} ${timeSlot.startTime}-${timeSlot.endTime}`
}

const getShiftTypeName = (shiftType: string) => {
  const names: Record<string, string> = {
    'DAY': '白班',
    'NIGHT': '夜班',
    'OFF': '休息'
  }
  return names[shiftType] || shiftType
}

const getAssignmentStatusName = (status: string) => {
  const names: Record<string, string> = {
    'PENDING': '待分配',
    'ASSIGNED': '已分配',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return names[status] || status
}

// 设置默认日期范围（最近7天）
const today = new Date()
const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
dutyStartDate.value = lastWeek.toISOString().split('T')[0]
dutyEndDate.value = today.toISOString().split('T')[0]
</script>

<style scoped>
.data-management-panel {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.management-header {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.management-header h2 {
  margin: 0 0 8px 0;
  color: #1976d2;
  font-size: 24px;
}

.management-header p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 14px;
}

.tab-navigation {
  display: flex;
  gap: 8px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.tab-button:hover {
  border-color: #1976d2;
  background: #f3f8ff;
}

.tab-button.active {
  border-color: #1976d2;
  background: #1976d2;
  color: white;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.tab-button.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

.statistics-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f8ff;
  border-radius: 50%;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.tab-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.content-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover {
  background: #1565c0;
}

.btn-secondary {
  background: #ff9800;
  color: white;
}

.btn-secondary:hover {
  background: #f57c00;
}

.btn-outline {
  background: white;
  color: #666;
  border: 1px solid #e0e0e0;
}

.btn-outline:hover {
  background: #f5f5f5;
}

.data-table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.data-table td {
  font-size: 14px;
  color: #666;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.inactive {
  background: #ffebee;
  color: #c62828;
}

.status-badge.pending {
  background: #fff3e0;
  color: #ef6c00;
}

.status-badge.assigned {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.cancelled {
  background: #ffebee;
  color: #c62828;
}

.shift-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.shift-badge.day {
  background: #fff3e0;
  color: #ef6c00;
}

.shift-badge.night {
  background: #e8eaf6;
  color: #3f51b5;
}

.shift-badge.off {
  background: #f3e5f5;
  color: #7b1fa2;
}

.btn-icon-small {
  padding: 4px 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s;
}

.btn-icon-small:hover {
  background: #f5f5f5;
}

.btn-icon-small.danger:hover {
  background: #ffebee;
}

.date-range-selector {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-input {
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background: white;
  padding: 32px;
  border-radius: 12px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>