/**
 * 人工修改约束检查服务
 * 提供与后端约束系统一致的前端验证逻辑
 */

import type { Teacher, ScheduleRecord } from '../types'

export interface ConstraintViolation {
  constraintId: string
  type: 'hard' | 'soft'
  severity: 'high' | 'medium' | 'low'
  title: string
  description: string
  suggestions: string[]
}

/**
 * 标准化科室名称（与后端一致）
 */
function normalizeDepartment(dept: string | undefined): string {
  if (!dept) return ''
  
  const mapping: Record<string, string> = {
    '区域一室': '一',
    '区域二室': '二',
    '区域三室': '三',
    '区域四室': '四',
    '区域五室': '五',
    '区域六室': '六',
    '区域七室': '七',
    '一室': '一',
    '二室': '二',
    '三室': '三',
    '四室': '四',
    '五室': '五',
    '六室': '六',
    '七室': '七'
  }
  
  return mapping[dept] || dept
}

/**
 * HC2: 检查考官1科室是否与学员匹配
 */
function isValidExaminer1Department(studentDept: string, examiner1Dept: string): boolean {
  // 同科室匹配
  if (studentDept === examiner1Dept) {
    return true
  }
  
  // 三室七室互通规则
  if ((studentDept === '三' && examiner1Dept === '七') || 
      (studentDept === '七' && examiner1Dept === '三')) {
    return true
  }
  
  return false
}

/**
 * 完整的约束检查函数
 */
export function checkManualEditConstraints(
  teacher: Teacher,
  editingField: string,
  editingRecord: ScheduleRecord,
  allScheduleRecords: ScheduleRecord[]
): ConstraintViolation[] {
  const violations: ConstraintViolation[] = []
  
  const studentDept = normalizeDepartment(editingRecord.department)
  const teacherDept = normalizeDepartment(teacher.department)
  const examDate = editingRecord.examDate
  
  // 🔧 修复：解析字段类型，支持完整字段名
  const isExaminer1 = editingField === 'examiner1_1' || editingField === 'examiner2_1' || editingField === 'examiner1'
  const isExaminer2 = editingField === 'examiner1_2' || editingField === 'examiner2_2' || editingField === 'examiner2'
  const isBackup = editingField === 'backup1' || editingField === 'backup2' || editingField === 'backup'
  
  console.log('🔍 [约束检查] 开始检查:', {
    teacher: teacher.name,
    teacherDept,
    editingField,
    isExaminer1,
    isExaminer2,
    isBackup,
    student: editingRecord.student,
    studentDept,
    examDate
  })
  
  // ============================================
  // HC2: 科室匹配规则
  // ============================================
  if (isExaminer1) {
    // 考官1必须与学员同科室（或三七互通）
    if (!isValidExaminer1Department(studentDept, teacherDept)) {
      violations.push({
        constraintId: 'HC2',
        type: 'hard',
        severity: 'high',
        title: 'HC2硬约束违反：考官1科室不匹配',
        description: `考官1必须与学员同科室。学员${editingRecord.student}来自${editingRecord.department}，${teacher.name}来自${teacher.department}`,
        suggestions: [
          `选择${editingRecord.department}的考官`,
          studentDept === '三' ? '或选择区域七室的考官（三七互通）' : '',
          studentDept === '七' ? '或选择区域三室的考官（三七互通）' : ''
        ].filter(s => s)
      })
    }
  } else if (isExaminer2) {
    // 考官2必须与学员不同科室（HC7）
    if (studentDept === teacherDept) {
      violations.push({
        constraintId: 'HC7',
        type: 'hard',
        severity: 'high',
        title: 'HC7硬约束违反：考官2不能与学员同科室',
        description: `考官2必须来自不同科室。学员${editingRecord.student}来自${editingRecord.department}，不能选择同科室的${teacher.name}`,
        suggestions: [
          '选择其他科室的考官'
        ]
      })
    }
  }
  
  // ============================================
  // HC7: 考官1和考官2必须来自不同科室（额外检查）
  // ============================================
  if (isExaminer1) {
    // 如果当前记录已有考官2，检查是否同科室
    const currentExaminer2 = editingRecord.examiner2
    if (currentExaminer2) {
      // 从所有考官中找到考官2的科室信息
      // 这里需要从props或全局状态中获取
      // 简化处理：从editingRecord中获取
      const examiner2Dept = normalizeDepartment((editingRecord as any).examiner2_department)
      if (examiner2Dept && teacherDept === examiner2Dept) {
        violations.push({
          constraintId: 'HC7',
          type: 'hard',
          severity: 'high',
          title: 'HC7硬约束违反：考官1和考官2不能同科室',
          description: `考官1 ${teacher.name}(${teacher.department}) 与考官2 ${currentExaminer2}(${examiner2Dept}) 来自同一科室`,
          suggestions: [
            '选择其他科室的考官作为考官1',
            '或更换考官2'
          ]
        })
      }
    }
  } else if (isExaminer2) {
    // 如果当前记录已有考官1，检查是否同科室
    const currentExaminer1 = editingRecord.examiner1
    if (currentExaminer1) {
      const examiner1Dept = normalizeDepartment((editingRecord as any).examiner1_department)
      if (examiner1Dept && teacherDept === examiner1Dept) {
        violations.push({
          constraintId: 'HC7',
          type: 'hard',
          severity: 'high',
          title: 'HC7硬约束违反：考官1和考官2不能同科室',
          description: `考官2 ${teacher.name}(${teacher.department}) 与考官1 ${currentExaminer1}(${examiner1Dept}) 来自同一科室`,
          suggestions: [
            '选择其他科室的考官作为考官2',
            '或更换考官1'
          ]
        })
      }
    }
  }
  
  // ============================================
  // HC8: 同一学员的三个考官不能重复
  // ============================================
  const currentExaminers = [
    editingRecord.examiner1,
    editingRecord.examiner2,
    editingRecord.backup
  ].filter(name => name && name !== editingRecord[editingField as keyof ScheduleRecord])
  
  if (currentExaminers.includes(teacher.name)) {
    violations.push({
      constraintId: 'HC8',
      type: 'hard',
      severity: 'high',
      title: 'HC8硬约束违反：考官重复',
      description: `${teacher.name}已担任该学员的其他考官角色`,
      suggestions: [
        '选择其他考官',
        '或调整现有角色分配'
      ]
    })
  }
  
  // ============================================
  // HC5: 同一考官一天只能参与一次考试
  // ============================================
  const sameTeacherSameDay = allScheduleRecords.filter(record => 
    record.examDate === examDate && 
    record.id !== editingRecord.id &&
    (record.examiner1 === teacher.name || 
     record.examiner2 === teacher.name || 
     record.backup === teacher.name)
  )
  
  if (sameTeacherSameDay.length > 0) {
    violations.push({
      constraintId: 'HC5',
      type: 'hard',
      severity: 'high',
      title: 'HC5硬约束违反：考官当天已有考试',
      description: `${teacher.name}在${examDate}已担任${sameTeacherSameDay.length}场考试的考官`,
      suggestions: [
        '选择当天没有考试安排的考官',
        `冲突的学员：${sameTeacherSameDay.map(r => r.student).join('、')}`
      ]
    })
  }
  
  // ============================================
  // HC3: 白班执勤不能担任考官（除非行政班）
  // ============================================
  // 注意：这需要班组轮换数据，前端可能无法完整验证
  // 目前依赖后端传递的conflictInfo
  if ((teacher as any).conflictInfo?.includes('白班执勤')) {
    violations.push({
      constraintId: 'HC3',
      type: 'hard',
      severity: 'high',
      title: 'HC3硬约束违反：白班执勤冲突',
      description: `${teacher.name}在${examDate}执勤白班，不能担任考官`,
      suggestions: [
        '选择非白班执勤的考官',
        '或调整考试日期'
      ]
    })
  }
  
  // ============================================
  // 软约束检查（提示但不阻止）
  // ============================================
  
  // SC10: 考官工作量
  if (teacher.currentWorkload && teacher.currentWorkload > 5) {
    violations.push({
      constraintId: 'SC10',
      type: 'soft',
      severity: 'medium',
      title: '软约束提示：工作量较高',
      description: `${teacher.name}当前工作量为${teacher.currentWorkload}，超过建议上限(5)`,
      suggestions: [
        '考虑选择工作量较轻的考官',
        '或接受此分配并继续'
      ]
    })
  }
  
  console.log('✅ [约束检查] 完成，发现违反:', violations.length)
  violations.forEach(v => {
    console.log(`  ${v.type === 'hard' ? '🚨' : '⚠️'} [${v.constraintId}] ${v.title}`)
  })
  
  return violations
}

/**
 * 检查是否有硬约束违反（阻止保存）
 */
export function hasHardConstraintViolations(violations: ConstraintViolation[]): boolean {
  return violations.some(v => v.type === 'hard')
}

/**
 * 格式化约束违反信息为显示文本
 */
export function formatViolationsForDisplay(violations: ConstraintViolation[]): string {
  const hardViolations = violations.filter(v => v.type === 'hard')
  const softViolations = violations.filter(v => v.type === 'soft')
  
  let text = ''
  
  if (hardViolations.length > 0) {
    text += `🚨 硬约束违反 (${hardViolations.length}个):\n\n`
    hardViolations.forEach((v, i) => {
      text += `${i + 1}. ${v.title}\n${v.description}\n\n`
    })
  }
  
  if (softViolations.length > 0) {
    text += `⚠️ 软约束提示 (${softViolations.length}个):\n\n`
    softViolations.forEach((v, i) => {
      text += `${i + 1}. ${v.title}\n${v.description}\n\n`
    })
  }
  
  return text
}


