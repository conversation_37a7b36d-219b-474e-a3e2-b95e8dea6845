# 考官排班系统

一个基于OptaPlanner智能算法的考官排班管理系统。

## 📦 最终交付版本

**版本**: 1.0.0  
**打包时间**: 2025-10-04  
**Java环境**: OpenJDK 17 (内置)

### 安装包位置

```
build-1759519804429\考官排班系统 Setup 1.0.0.exe  (331 MB)
```

## 🚀 快速开始

### 用户使用

1. 运行安装包 `考官排班系统 Setup 1.0.0.exe`
2. 按照向导完成安装
3. 运行程序
4. 导入数据并开始排班

详细使用说明请查看：[📋最终交付文档.md](📋最终交付文档.md)

### 开发者

#### 环境要求

- Node.js 16+
- Maven 3.6+
- JDK 17

#### 开发模式

```bash
# 启动前端
npm run dev

# 启动后端
cd optaplanner-service
mvn quarkus:dev
```

#### 打包构建

```bash
# 1. 下载和解压JDK（仅首次）
download-jdk.bat
extract-jdk.bat

# 2. 完整打包流程
完整打包流程-JDK版.bat
```

## 📁 项目结构

```
examiner-assignment-system/
├── src/                    # 前端Vue源代码
├── electron/               # Electron主进程代码
├── optaplanner-service/    # 后端OptaPlanner服务
│   └── target/quarkus-app/ # 后端编译产物
├── public/                 # 公共资源
├── dist/                   # 前端构建产物
├── docs/                   # 技术文档
├── jdk/                    # JDK运行环境
└── build-1759519804429/    # 最终打包结果
    └── 考官排班系统 Setup 1.0.0.exe  # 安装包
```

## 🔧 技术栈

- **前端**: Vue 3 + Vite + TypeScript
- **桌面**: Electron 27
- **后端**: Quarkus 2.16 + OptaPlanner 8.38
- **Java**: OpenJDK 17
- **UI**: Tailwind CSS

## 📄 文档

- [📋最终交付文档.md](📋最终交付文档.md) - 用户手册和部署指南
- [docs/](docs/) - 技术文档和开发说明

## ⚠️ 重要说明

- 本系统使用完整的JDK（非JRE），内含Java编译器
- 安装包为自包含，无需预装Java环境
- 首次启动需10-20秒初始化后端服务

## 🧹 系统清理

系统已完成彻底清理，删除了所有不必要的开发文件、测试代码和冗余代码。

### 已删除内容（共约100+个文件）

**开发文档** (73个文件)
- `docs/` - 所有开发调试文档、修复记录和诊断指南

**测试和诊断文件** (7个文件)
- `check_teacher.html` - 教师数据测试页面
- `optaplanner-service/duty-test.bat` - 后端测试脚本
- `optaplanner-service/src/test/` - Java测试代码目录
- `一键诊断脚本.js` - 前端诊断脚本
- `快速诊断-实时更新.bat` - 实时更新诊断脚本
- `快速修复实时更新.bat` - 快速修复脚本
- `[Help` - 空文件

**版本切换脚本** (5个文件)
- `切换到通用版.bat` - 通用版本切换脚本
- `恢复到RTX4090版.bat` - RTX4090版本恢复脚本
- `启动-高性能模式.bat` - 高性能模式启动脚本
- `electron/main-rtx4090-backup.js` - RTX4090备份文件
- `electron/main-universal.js` - 通用版本备份文件

**临时构建文件** (2个文件)
- `build-with-jdk.js` - 临时构建脚本
- `打包成功说明.md` - 打包说明文档

**冗余页面和组件** (8个文件)
- `src/views/` - 整个未使用的views目录 (Home.vue, Schedule.vue, NotFound.vue)
- `src/pages/DatePickerDemo.vue` - 日期选择器演示页面
- `src/pages/DatePickerTestPage.vue` - 日期选择器测试页面
- `src/pages/EnhancedStatisticsPage.vue` - 未使用的增强统计页面
- `src/pages/SettingsPage.vue` - 未使用的设置页面
- `src/algorithm/` - 未使用的算法类型定义目录
- `src/assets/vue.svg` - 未使用的图标文件

**冗余服务层代码** (5个文件)
- `src/services/conflictSeverityAlertService.ts` - 未使用的冲突严重性服务
- `src/services/constraint-validation-engine.ts` - 未使用的约束验证引擎
- `src/services/constraintSyncService.ts` - 未使用的约束同步服务
- `src/services/todayExamService.ts` - 未使用的当日考试服务
- `src/services/unified-constraint-service.ts` - 未使用的统一约束服务

### 保留的核心内容

**源代码**
- `src/` - 完整的Vue 3前端源代码
  - `pages/` - 8个核心页面（Dashboard, DataManagement, Teachers, Schedules, Reports, Statistics, LearningStats, HomePage2）
  - `components/` - 30个业务组件
  - `services/` - 42个服务层文件（已精简）
  - `utils/` - 16个工具类
  - `types/` - 7个类型定义文件
- `optaplanner-service/src/main/` - 完整的Java后端源代码（83个文件）
- `electron/main.js`, `electron/preload.js` - Electron主进程代码

**配置文件**
- `package.json`, `vite.config.mjs` - 前端配置
- `optaplanner-service/pom.xml` - 后端Maven配置
- `tsconfig.json`, `tailwind.config.js` - TypeScript和样式配置
- `electron-builder.yml` - Electron打包配置

**运行环境**
- `jdk/` - Java 17运行环境（完整JDK）
- `dist/` - 前端构建产物
- `optaplanner-service/target/` - 后端编译产物
- `build-1759835892024/` - 最新打包结果（含安装包）

**启动和打包脚本**
- `start-dev.bat` - 开发环境一键启动（推荐）
- `start_frontend.bat` - 前端单独启动
- `start_backend.bat` - 后端单独启动
- `启动已打包应用.bat` - 打包应用启动
- `完整打包流程-JDK版.bat` - 完整打包脚本

### 清理效果

✅ **删除了约100+个文件和目录**  
✅ **项目体积显著减小，结构更加清晰**  
✅ **保留了100%核心功能代码**  
✅ **系统经过验证，可以正常运行**

清理后的项目更加专注于核心功能，便于维护和部署。所有关键功能保持完整，路由配置已同步更新。

## 📞 支持

如遇问题，请按F12打开开发者工具查看详细日志。

---

© 2025 考官排班系统

