# 考官排班系统 - 完整打包为独立EXE

## 🎯 目标

创建一个完全独立的Windows EXE应用程序，可以在**没有网络、没有Java环境**的电脑上运行。

## ⚠️ 当前遇到的问题及解决方案

### 问题1: 符号链接权限错误

**错误信息**:
```
ERROR: Cannot create symbolic link : 客户端没有所需的特权
```

**原因**: Windows创建符号链接需要管理员权限

**解决方案**（3选1）:

#### 方案A: 以管理员权限运行（最简单）✅

1. 右键点击 `完整打包-管理员权限.bat`
2. 选择"以管理员身份运行"
3. 在UAC提示时点击"是"

#### 方案B: 启用开发者模式（Windows 10/11）

1. 打开 设置 → 更新和安全 → 开发者选项
2. 启用"开发人员模式"
3. 重启电脑
4. 之后无需管理员权限即可打包

#### 方案C: 手动清理+普通权限运行

1. 删除缓存目录: `C:\Users\<USER>\AppData\Local\electron-builder\Cache\winCodeSign\`
2. 手动下载工具: 参见 `手动下载依赖工具.txt`
3. 运行打包脚本

---

## 📋 完整打包步骤（包含离线运行环境）

### 第1步: 准备Java运行时（用于离线运行）

如果您希望打包的EXE可以在**没有Java**的电脑上运行：

```bash
# 运行此脚本下载JDK
下载JDK用于离线打包.bat
```

或手动下载：
1. 访问 https://adoptium.net/temurin/releases/
2. 下载 JDK 17 (Windows x64 ZIP)
3. 解压后重命名为 `jdk`
4. 放到项目根目录

**目录结构**:
```
E:\Project\examiner-assignment-system\
├── jdk\              ← 放这里
│   ├── bin\
│   │   └── java.exe  ← 确保这个文件存在
│   └── lib\
├── optaplanner-service\
├── src\
└── ...
```

### 第2步: 执行完整打包

**以管理员权限运行**:
```bash
# 右键 → 以管理员身份运行
完整打包-管理员权限.bat
```

**脚本会自动**:
1. ✅ 清理缓存（解决权限问题）
2. ✅ 编译Java后端
3. ✅ 构建Vue前端
4. ✅ 打包Java运行时（如果存在jdk目录）
5. ✅ 创建独立EXE
6. ✅ 生成安装程序

**预计时间**: 10-15分钟（首次更长）

### 第3步: 获取打包结果

打包成功后，在 `build-electron-output` 目录会生成：

```
build-electron-output\
├── win-unpacked\
│   └── 考官排班系统.exe          ← 便携版（推荐测试）
└── 考官排班系统-1.0.0-Setup.exe  ← 安装程序（推荐分发）
```

---

## 🔍 离线运行验证

### 如何验证是否可以离线运行？

1. **断开网络连接**
2. **在另一台没有安装Java的电脑上**运行EXE
3. 应该能够正常启动和使用

### 检查是否打包了Java运行时

```bash
# 查看resources目录
dir build-electron-output\win-unpacked\resources\jdk
```

如果存在`jdk`或`jre`目录，说明已打包Java运行时 ✅

---

## 📦 打包后的应用特点

### 完整打包（包含JDK）

- ✅ 完全独立，无需任何依赖
- ✅ 支持离线运行
- ✅ 支持OptaPlanner完整功能
- ✅ 用户无需安装Java
- ⚠️ 文件较大（约400-500MB）

### 不包含JDK

- ⚠️ 需要用户安装JDK 17+
- ⚠️ 需要网络下载依赖
- ✅ 文件较小（约200-250MB）

---

## 🐛 故障排除

### Q1: 打包失败 - 网络超时

**现象**: 
```
Get "https://github.com/...": connection timeout
```

**解决**:
1. 检查网络连接
2. 使用VPN或代理
3. 手动下载依赖工具（参见 `手动下载依赖工具.txt`）

### Q2: 打包失败 - 符号链接权限

**现象**:
```
ERROR: Cannot create symbolic link
```

**解决**:
1. 以管理员权限运行
2. 或启用开发者模式
3. 或手动清理缓存后重试

### Q3: EXE运行但后端启动失败

**现象**: 应用打开但提示后端连接失败

**原因**: 
- 端口8081被占用
- Java运行时未正确打包
- 后端编译有问题

**解决**:
1. 检查端口: `netstat -ano | findstr 8081`
2. 查看日志: 按F12打开开发者工具
3. 重新编译后端: `cd optaplanner-service && mvn clean package -DskipTests`

### Q4: 打包的EXE在其他电脑无法运行

**检查项**:
1. ✅ 是否包含JDK? 查看 `resources\jdk` 目录
2. ✅ 是否是64位Windows? 应用仅支持x64
3. ✅ Windows版本? 至少需要Windows 7 SP1+
4. ✅ 防火墙是否拦截? 添加白名单

---

## 📊 打包配置详解

### electron-builder.yml 关键配置

```yaml
extraResources:
  # 后端服务（必需）
  - from: optaplanner-service/target/quarkus-app
    to: backend
  
  # Java运行时（可选，用于离线运行）
  - from: jdk      # 或 jre
    to: jdk        # 或 jre
  
  # 前端资源
  - from: dist
    to: dist
```

### package.json 打包命令

```json
{
  "scripts": {
    "electron:build": "npm run build && cross-env NODE_ENV=production electron-builder"
  }
}
```

---

## 🚀 优化建议

### 减少文件大小

1. **使用JRE而不是JDK**
   - 减少约100MB
   - 但可能影响OptaPlanner功能

2. **压缩后分发**
   - 使用7-Zip极限压缩
   - 可减少40-50%

3. **只打包必要的语言包**
   ```yaml
   win:
     electronLanguages:
       - zh-CN
       - en-US
   ```

### 加快启动速度

1. 优化JVM参数（在electron/main.js中）:
   ```javascript
   const javaArgs = [
     '-Xms256m',     // 减少初始内存
     '-Xmx1g',       // 减少最大内存
     '-XX:+UseG1GC', // 使用G1GC
   ]
   ```

2. 使用AOT编译（高级）

---

## 📝 分发检查清单

打包完成后，分发前请检查：

- [ ] 在干净的虚拟机中测试（无Java、无网络）
- [ ] 测试安装程序安装/卸载流程
- [ ] 测试便携版在不同目录运行
- [ ] 检查首次启动时间（应在30秒内）
- [ ] 测试基本功能（排班、数据导入导出等）
- [ ] 检查EXE文件大小是否合理
- [ ] 准备用户手册和安装说明

---

## 🎓 技术架构说明

### 应用组成

```
考官排班系统.exe
├── Electron框架
│   ├── Chromium内核（前端渲染）
│   ├── Node.js运行时
│   └── 原生模块
├── Vue前端应用（打包在app.asar）
├── Java后端服务（resources\backend）
└── Java运行时（resources\jdk）← 可选
```

### 启动流程

1. 用户双击 `考官排班系统.exe`
2. Electron启动，加载main.js
3. main.js启动Java后端（使用打包的JDK）
4. 等待后端健康检查通过（约10-20秒）
5. 显示Vue前端界面
6. 用户开始使用

---

## 💡 常见问题

### 为什么打包这么慢？

首次打包需要：
- 下载electron-builder工具
- 下载winCodeSign工具
- 编译后端（Maven下载依赖）
- 构建前端
- 复制所有文件
- 生成安装程序

后续打包会快很多（3-5分钟）

### 为什么文件这么大？

Electron应用包含：
- Chromium浏览器（约120MB）
- Node.js运行时（约40MB）
- Java后端+依赖（约80MB）
- JDK（约180MB，可选）
- 前端资源（约20MB）

总计：约400-450MB（含JDK）

### 可以打包成更小的文件吗？

可以，但会牺牲功能：
1. 不打包JDK → 减少180MB
2. 使用Tauri代替Electron → 减少100MB+（需要重构）
3. 使用原生方案 → 最小，但开发成本高

---

## 📞 获取帮助

如果遇到问题：

1. **查看日志**
   - 打包日志：控制台输出
   - 运行日志：按F12打开开发者工具
   - 后端日志：`%APPDATA%\考官排班系统\logs\backend.log`

2. **检查配置**
   - electron-builder.yml
   - package.json
   - electron/main.js

3. **重置环境**
   ```bash
   # 清理所有缓存
   rd /s /q node_modules
   rd /s /q build-electron-output
   rd /s /q dist
   rd /s /q %LOCALAPPDATA%\electron-builder\Cache
   
   # 重新安装
   npm install
   ```

---

## ✅ 快速开始（TL;DR）

```bash
# 1. 下载JDK（可选，用于离线运行）
下载JDK用于离线打包.bat

# 2. 完整打包（右键 → 以管理员身份运行）
完整打包-管理员权限.bat

# 3. 等待10-15分钟

# 4. 测试运行
build-electron-output\win-unpacked\考官排班系统.exe
```

**就这么简单！** 🎉

---

**最后更新**: 2025-10-08  
**适用版本**: 1.0.0
