﻿/**
 * OptaPlanner数据转换器
 * 将前端数据格式转换为OptaPlanner服务所需的格式
 */

import type { OptaPlannerStudent, OptaPlannerTeacher } from './optaplanner-service'

export class OptaPlannerDataConverter {
  /**
   * 转换学员数据为OptaPlanner格式
   */
  static convertStudents(students: any[]): OptaPlannerStudent[] {
    return students.map(student => ({
      id: student.id || `student_${student.name || 'unknown'}`,
      name: student.name || '未知学员',
      department: student.department || '未知科室',
      group: student.group || '无',
      recommendedExaminer1Dept: student.recommendedExaminer1Dept,
      recommendedExaminer2Dept: student.recommendedExaminer2Dept,
      recommendedBackupDept: student.recommendedBackupDept,
      // ✨ 方案A：传递前端智能日期选择的推荐
      recommendedExamDate1: student.recommendedExamDate1,
      recommendedExamDate2: student.recommendedExamDate2
    }))
  }

  /**
   * 转换考官数据为OptaPlanner格式
   */
  static convertTeachers(teachers: any[]): OptaPlannerTeacher[] {
    return teachers.map(teacher => ({
      id: teacher.id || `teacher_${teacher.name || 'unknown'}`,
      name: teacher.name || '未知考官',
      department: teacher.department || '未知科室',
      group: teacher.group || '无',
      skills: teacher.skills || [],
      workload: teacher.workload || 0,
      consecutiveDays: teacher.consecutiveDays || 0
    }))
  }

  /**
   * 转换排班结果为前端格式
   */
  static convertScheduleResult(optaPlannerResult: any): any {
    if (!optaPlannerResult || !optaPlannerResult.assignments) {
      return {
        success: false,
        message: '无效的排班结果',
        assignments: [],
        statistics: {
          totalStudents: 0,
          assignedStudents: 0,
          unassignedStudents: 0,
          completionPercentage: 0
        }
      }
    }

    return {
      success: optaPlannerResult.success,
      message: optaPlannerResult.message,
      score: optaPlannerResult.score,  // ✨ 关键修复：传递score字段
      assignments: optaPlannerResult.assignments.map((assignment: any) => ({
        id: assignment.id,
        studentId: assignment.student?.id,
        studentName: assignment.student?.name,
        studentDepartment: assignment.student?.department, // ✨ 新增：直接携带学员科室
        examType: assignment.examType,
        examDate: assignment.examDate,
        examiner1: {
          id: assignment.examiner1?.id,
          name: assignment.examiner1?.name,
          department: assignment.examiner1?.department
        },
        examiner2: {
          id: assignment.examiner2?.id,
          name: assignment.examiner2?.name,
          department: assignment.examiner2?.department
        },
        backupExaminer: {
          id: assignment.backupExaminer?.id,
          name: assignment.backupExaminer?.name,
          department: assignment.backupExaminer?.department
        },
        timeSlot: assignment.timeSlot,
        location: assignment.location
      })),
      statistics: optaPlannerResult.statistics,
      conflicts: optaPlannerResult.conflicts || [],
      warnings: optaPlannerResult.warnings || []
    }
  }
}

// 导出默认实例
export const optaPlannerDataConverter = new OptaPlannerDataConverter()