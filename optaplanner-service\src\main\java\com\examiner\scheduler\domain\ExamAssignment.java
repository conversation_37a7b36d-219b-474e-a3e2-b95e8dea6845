package com.examiner.scheduler.domain;

import org.optaplanner.core.api.domain.entity.PlanningEntity;
import org.optaplanner.core.api.domain.entity.PlanningPin;
import org.optaplanner.core.api.domain.variable.PlanningVariable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Objects;

/**
 * 考试分配实体类 - OptaPlanner规划实体
 * 每个实例代表一个学员的一次考试安排
 */
@PlanningEntity
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExamAssignment {
    
    private String id;
    private Student student;        // 学员（固定）
    private String examType;        // 考试类型：day1 或 day2（固定）
    private List<String> subjects;  // 考试科目（固定）
    
    // ⚠️ 固定标志：当为true时，OptaPlanner不会修改此assignment的规划变量
    @PlanningPin
    private boolean pinned = false;
    
    // OptaPlanner规划变量
    // 🔒 日期不作为规划变量（在初始解中已确定连续性）
    private String examDate;        // 考试日期（初始解固定，不再是@PlanningVariable）
    
    // 🔧 关键修复：将examiner1也设为规划变量，让OptaPlanner可以调整以满足HC2约束  
    // 🚨 HC2修复：依赖极高权重的HC2硬约束（权重：**********）来强制执行科室规则
    @PlanningVariable(valueRangeProviderRefs = "teacherRange", nullable = false)
    private Teacher examiner1;      // 考官1 - 同科室（规划变量，必须满足HC2）
    
    @PlanningVariable(valueRangeProviderRefs = "teacherRange", nullable = false)
    private Teacher examiner2;      // 考官2 - 不同科室（规划变量，不允许为null）
    
    @PlanningVariable(valueRangeProviderRefs = "teacherRange", nullable = true)
    private Teacher backupExaminer; // 备份考官 - 不同科室（规划变量，备份可选）
    
    // 其他属性
    private String location;
    private TimeSlot timeSlot;
    
    // 构造函数
    public ExamAssignment() {
        // 确保无参构造函数也初始化必要的字段
        this.location = "考试室";
        this.timeSlot = new TimeSlot("08:00", "12:00", "morning");
    }
    
    public ExamAssignment(String id, Student student, String examType, List<String> subjects) {
        this(); // 调用无参构造函数确保基本字段初始化
        this.id = id;
        this.student = student;
        this.examType = examType;
        this.subjects = subjects;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public Student getStudent() {
        return student;
    }
    
    public void setStudent(Student student) {
        this.student = student;
    }
    
    public String getExamType() {
        return examType;
    }
    
    public void setExamType(String examType) {
        this.examType = examType;
    }
    
    public List<String> getSubjects() {
        return subjects;
    }
    
    public void setSubjects(List<String> subjects) {
        this.subjects = subjects;
    }
    
    public String getExamDate() {
        return examDate;
    }
    
    public void setExamDate(String examDate) {
        this.examDate = examDate;
    }
    
    public Teacher getExaminer1() {
        return examiner1;
    }
    
    public void setExaminer1(Teacher examiner1) {
        this.examiner1 = examiner1;
    }
    
    public Teacher getExaminer2() {
        return examiner2;
    }
    
    public void setExaminer2(Teacher examiner2) {
        this.examiner2 = examiner2;
    }
    
    public Teacher getBackupExaminer() {
        return backupExaminer;
    }
    
    public void setBackupExaminer(Teacher backupExaminer) {
        this.backupExaminer = backupExaminer;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public TimeSlot getTimeSlot() {
        return timeSlot;
    }
    
    public void setTimeSlot(TimeSlot timeSlot) {
        this.timeSlot = timeSlot;
    }
    
    public boolean isPinned() {
        return pinned;
    }
    
    public void setPinned(boolean pinned) {
        this.pinned = pinned;
    }
    
    /**
     * 检查分配是否完整（所有规划变量都已分配）
     */
    public boolean isComplete() {
        return examDate != null && 
               examiner1 != null && 
               examiner2 != null && 
               backupExaminer != null;
    }
    
    /**
     * 获取学员ID（便于访问）
     */
    public String getStudentId() {
        return student != null ? student.getId() : null;
    }
    
    /**
     * 获取学员姓名（便于访问）
     */
    public String getStudentName() {
        return student != null ? student.getName() : "未知学员";
    }
    
    /**
     * 获取学员科室（便于访问）
     */
    public String getStudentDepartment() {
        return student != null ? student.getDepartment() : null;
    }
    
    /**
     * 检查是否为第一天考试
     */
    public boolean isDay1Exam() {
        return "day1".equals(examType);
    }
    
    /**
     * 检查是否为第二天考试
     */
    public boolean isDay2Exam() {
        return "day2".equals(examType);
    }
    
    /**
     * 检查是否包含现场考试
     */
    public boolean hasFieldExam() {
        return subjects != null && subjects.contains("现场");
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExamAssignment that = (ExamAssignment) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "ExamAssignment{" +
                "id='" + id + '\'' +
                ", student=" + (student != null ? student.getName() : "未知学员") +
                ", examType='" + examType + '\'' +
                ", examDate='" + examDate + '\'' +
                ", examiner1=" + (examiner1 != null ? examiner1.getName() : "未知考官") +
                ", examiner2=" + (examiner2 != null ? examiner2.getName() : "未知考官") +
                ", backupExaminer=" + (backupExaminer != null ? backupExaminer.getName() : "未知考官") +
                '}';
    }
}