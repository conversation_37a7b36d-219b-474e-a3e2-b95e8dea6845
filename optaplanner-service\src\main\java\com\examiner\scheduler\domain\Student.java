package com.examiner.scheduler.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Objects;

/**
 * 学员实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Student {
    
    private String id;
    private String name;
    private String department; // 一、二、三、四、五、六、七
    private String group;      // 一组、二组、三组、四组
    private String recommendedExaminer1Dept; // 推荐考官1科室
    private String recommendedExaminer2Dept; // 推荐考官2科室
    private String recommendedBackupDept;    // 推荐备份考官科室
    
    // ✨ 新增：前端智能日期选择推荐的考试日期
    private String recommendedExamDate1;     // 推荐考试日期1（第一天）
    private String recommendedExamDate2;     // 推荐考试日期2（第二天）
    
    // 构造函数
    public Student() {}
    
    public Student(String id, String name, String department, String group) {
        this.id = id;
        this.name = name;
        this.department = department;
        this.group = group;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getGroup() {
        return group;
    }
    
    public void setGroup(String group) {
        this.group = group;
    }
    
    public String getRecommendedExaminer1Dept() {
        return recommendedExaminer1Dept;
    }
    
    public void setRecommendedExaminer1Dept(String recommendedExaminer1Dept) {
        this.recommendedExaminer1Dept = recommendedExaminer1Dept;
    }
    
    public String getRecommendedExaminer2Dept() {
        return recommendedExaminer2Dept;
    }
    
    public void setRecommendedExaminer2Dept(String recommendedExaminer2Dept) {
        this.recommendedExaminer2Dept = recommendedExaminer2Dept;
    }
    
    public String getRecommendedBackupDept() {
        return recommendedBackupDept;
    }
    
    public void setRecommendedBackupDept(String recommendedBackupDept) {
        this.recommendedBackupDept = recommendedBackupDept;
    }
    
    // ✨ 智能日期推荐的Getter/Setter
    public String getRecommendedExamDate1() {
        return recommendedExamDate1;
    }
    
    public void setRecommendedExamDate1(String recommendedExamDate1) {
        this.recommendedExamDate1 = recommendedExamDate1;
    }
    
    public String getRecommendedExamDate2() {
        return recommendedExamDate2;
    }
    
    public void setRecommendedExamDate2(String recommendedExamDate2) {
        this.recommendedExamDate2 = recommendedExamDate2;
    }
    
    /**
     * 检查是否属于优先科室（三室、七室）
     */
    public boolean isPriorityDepartment() {
        return "三".equals(this.department) || "七".equals(this.department);
    }
    
    /**
     * 获取学员优先级分数
     * 三室、七室学员优先级更高
     */
    public int getPriorityScore() {
        if (isPriorityDepartment()) {
            return 100; // 高优先级
        }
        return 50; // 普通优先级
    }
    
    /**
     * 检查学员是否可以在指定日期进行考试
     * 主要检查HC5约束：学员进行现场考试时，不能安排在学员本班组执勤白班的时间
     */
    public boolean canExamOnDate(String date, DutySchedule dutySchedule, String examType) {
        // HC5约束：现场考试时不能安排在学员本班组执勤白班的时间
        if ("现场".equals(examType) || "day1".equals(examType)) {
            return !this.group.equals(dutySchedule.getDayShift());
        }
        
        // 其他类型考试没有此限制
        return true;
    }
    
    /**
     * 获取推荐科室池（考官1推荐科室 + 考官2推荐科室）
     * 用于SC2、SC4、SC6、SC8约束
     * 🔧 说明：考官2和备份考官都使用这同一个推荐科室池
     * Excel中只有两列：考官一推荐科室、考官二推荐科室
     */
    public java.util.List<String> getExaminer2RecommendedDepartments() {
        java.util.List<String> departments = new java.util.ArrayList<>();
        if (recommendedExaminer1Dept != null && !recommendedExaminer1Dept.trim().isEmpty()) {
            departments.add(recommendedExaminer1Dept);
        }
        if (recommendedExaminer2Dept != null && !recommendedExaminer2Dept.trim().isEmpty()) {
            departments.add(recommendedExaminer2Dept);
        }
        return departments;
    }
    
    /**
     * 🆕 根据考试类型获取考官2的推荐科室
     * 新规则：
     * - 第一天（day1）：考官2应该来自考官1推荐科室
     * - 第二天（day2）：考官2应该来自考官2推荐科室
     * 
     * @param examType 考试类型："day1" 或 "day2"
     * @return 对应的推荐科室（单个科室，不是列表）
     */
    public String getExaminer2RecommendedDepartmentByExamType(String examType) {
        if ("day1".equals(examType)) {
            // 第一天：使用考官1推荐科室
            return recommendedExaminer1Dept;
        } else if ("day2".equals(examType)) {
            // 第二天：使用考官2推荐科室
            return recommendedExaminer2Dept;
        }
        // 默认返回null（不应该发生）
        return null;
    }
    
    /**
     * @deprecated 此方法已废弃。实际Excel中没有备份考官推荐科室这一列。
     * 备份考官也使用考官2推荐科室池（getExaminer2RecommendedDepartments()）
     * 保留此方法仅为兼容性，但实际值通常为null
     */
    @Deprecated
    public String getBackupRecommendedDepartment() {
        return recommendedBackupDept;
    }
    
    /**
     * @deprecated 此方法混淆了考官2和备份考官的推荐科室，建议使用：
     * - getExaminer2RecommendedDepartments() - 获取考官2推荐科室池
     * - getBackupRecommendedDepartment() - 获取备份考官推荐科室
     * 🔧 修复：为了向后兼容，保留此方法，但移除了备份考官推荐科室
     */
    @Deprecated
    public java.util.List<String> getRecommendedDepartments() {
        java.util.List<String> departments = new java.util.ArrayList<>();
        if (recommendedExaminer1Dept != null && !recommendedExaminer1Dept.trim().isEmpty()) {
            departments.add(recommendedExaminer1Dept);
        }
        if (recommendedExaminer2Dept != null && !recommendedExaminer2Dept.trim().isEmpty()) {
            departments.add(recommendedExaminer2Dept);
        }
        // 🔧 修复：移除备份考官推荐科室（不应该在推荐科室池中）
        return departments;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Student student = (Student) o;
        return Objects.equals(id, student.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Student{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", department='" + department + '\'' +
                ", group='" + group + '\'' +
                '}';
    }
}