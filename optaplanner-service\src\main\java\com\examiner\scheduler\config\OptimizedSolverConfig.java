package com.examiner.scheduler.config;

import com.examiner.scheduler.domain.ExamSchedule;
import com.examiner.scheduler.domain.OptimizedConstraintConfiguration;
import com.examiner.scheduler.solver.OptimizedExamScheduleConstraintProvider;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.config.solver.termination.TerminationConfig;

import javax.enterprise.context.ApplicationScoped;

/**
 * 优化的求解器配置
 * 🎯 提供多种求解策略和智能终止条件
 */
@ApplicationScoped
public class OptimizedSolverConfig {
    
    /**
     * 创建带约束配置的求解器配置
     * 🔧 修复：添加Phase配置，确保LocalSearch执行
     */
    public SolverConfig createSolverConfigWithConstraints(int studentCount, OptimizedConstraintConfiguration constraints) {
        TerminationConfig termination = createDynamicTermination(studentCount);
        
        // 构造启发式配置 - 使用简单的FIRST_FIT，不需要难度比较器
        org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig constructionHeuristic = 
            new org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig();
        constructionHeuristic.setConstructionHeuristicType(
            org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicType.FIRST_FIT);
        
        // LocalSearch配置
        org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig localSearch = 
            new org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig();
        
        SolverConfig solverConfig = new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withPhaseList(java.util.Arrays.asList(
                    constructionHeuristic,
                    localSearch  // 🔧 添加LocalSearch阶段
                ))
                .withTerminationConfig(termination);
        
        // 根据约束配置调整求解器参数
        // ⚡ 即使有高权重软约束，也保持1分钟限制（智能预分配已优化）
        if (hasHighWeightConstraints(constraints)) {
            termination.withUnimprovedSecondsSpentLimit(15L);  // 最多15秒无改进
        }
        
        return solverConfig;
    }
    
    /**
     * 创建自动配置的求解器
     */
    public SolverConfig createAutoSolverConfig(int studentCount) {
        return createSolverConfigWithConstraints(studentCount, new OptimizedConstraintConfiguration());
    }
    
    /**
     * 创建默认求解器配置
     * 🔧 修复：添加Phase配置，确保LocalSearch执行
     */
    public SolverConfig createDefaultSolverConfig() {
        // 构造启发式配置 - 使用简单的FIRST_FIT，不需要难度比较器
        org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig constructionHeuristic = 
            new org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig();
        constructionHeuristic.setConstructionHeuristicType(
            org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicType.FIRST_FIT);
        
        // LocalSearch配置
        org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig localSearch = 
            new org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig();
        
        return new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withPhaseList(java.util.Arrays.asList(
                    constructionHeuristic,
                    localSearch  // 🔧 添加LocalSearch阶段
                ))
                .withTerminationConfig(new TerminationConfig()
                        .withSecondsSpentLimit(60L)  // ⚡ 最大1分钟限制
                        .withUnimprovedSecondsSpentLimit(10L));  // ⚡ 10秒无改进停止
                        // 🔧 移除 bestScoreLimit，允许充分优化软约束
    }
    
    /**
     * 创建带智能终止条件的求解器配置
     * 🚀 超级优化：智能检测收敛，自动提前终止
     * 🔧 修复：添加Phase配置，确保LocalSearch执行
     */
    public SolverConfig createSmartSolverConfig() {
        // 构造启发式配置 - 使用简单的FIRST_FIT，不需要难度比较器
        org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig constructionHeuristic = 
            new org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig();
        constructionHeuristic.setConstructionHeuristicType(
            org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicType.FIRST_FIT);
        
        // LocalSearch配置
        org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig localSearch = 
            new org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig();
        
        // 🔧 修复：使用标准终止配置替代过时的withTerminationClass
        TerminationConfig termination = new TerminationConfig()
                .withSecondsSpentLimit(60L)
                .withUnimprovedSecondsSpentLimit(10L);
                // 🔧 移除 bestScoreLimit，允许充分优化软约束
        
        return new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withPhaseList(java.util.Arrays.asList(
                    constructionHeuristic,
                    localSearch  // 🔧 添加LocalSearch阶段
                ))
                .withTerminationConfig(termination);
    }
    
    /**
     * 根据学员数量创建动态终止条件
     * 🔧 修复：移除bestScoreLimit，允许充分优化以检测和修复硬约束违反
     */
    private TerminationConfig createDynamicTermination(int studentCount) {
        // ⚡ 基础时间：大幅减少，因为智能预分配提供了良好起点
        long baseSeconds = Math.max(15, studentCount * 2);
        
        // ⚡ 最大时间限制：1分钟（智能预分配后无需长时间搜索）
        long maxSeconds = Math.min(baseSeconds, 60);
        
        // 🔧 无改进时间限制：增加到20秒，确保有足够时间修复硬约束
        long unimprovedSeconds = Math.max(15, maxSeconds / 3);
        
        return new TerminationConfig()
                .withSecondsSpentLimit(maxSeconds)
                .withUnimprovedSecondsSpentLimit(unimprovedSeconds);
                // 🔧 关键修复：移除bestScoreLimit，允许LocalSearch充分执行
                // 不再过早终止，确保有时间检测和修复所有硬约束违反
    }
    
    /**
     * 检查是否有高权重约束
     */
    private boolean hasHighWeightConstraints(OptimizedConstraintConfiguration constraints) {
        // 检查是否有权重超过默认值的约束
        return constraints != null && (
            constraints.getAllowDept37CrossUseWeight().softScore() > 50 ||
            constraints.getPreferNoGroupTeachersWeight().softScore() > 50 ||
            constraints.getNightShiftTeacherPriorityWeight().softScore() > 50 ||
            constraints.getFirstRestDayTeacherPriorityWeight().softScore() > 50 ||
            constraints.getSecondRestDayTeacherPriorityWeight().softScore() > 50
        );
    }
    
    /**
     * ⚡ 创建多阶段优化求解器配置（智能提前终止）
     * 🔧 修复：移除bestScoreLimit，避免过早终止导致的伪完美解问题
     */
    public SolverConfig createMultiPhaseSolverConfig(int studentCount) {
        // ⚡ 基于智能预分配，多阶段也只需1分钟
        long baseTime = Math.max(30, studentCount * 2);
        long maxTime = Math.min(baseTime, 60); // ⚡ 最大1分钟
        
        return new SolverConfig()
                .withSolutionClass(ExamSchedule.class)
                .withEntityClasses(com.examiner.scheduler.domain.ExamAssignment.class)
                .withConstraintProviderClass(OptimizedExamScheduleConstraintProvider.class)
                .withTerminationConfig(new TerminationConfig()
                        .withSecondsSpentLimit(maxTime)
                        .withUnimprovedSecondsSpentLimit(Math.min(15L, maxTime / 3)));
                        // 🔧 关键修复：移除bestScoreLimit，让求解器充分验证所有硬约束
                        // 避免过早终止返回未经充分验证的"伪完美"解
    }
}